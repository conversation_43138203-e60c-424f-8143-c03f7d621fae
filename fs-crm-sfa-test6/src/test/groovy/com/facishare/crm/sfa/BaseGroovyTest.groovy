package com.facishare.crm.sfa

import com.facishare.crm.describebuilder.ObjectDescribeBuilder
import com.facishare.crm.describebuilder.TextFieldDescribeBuilder
import com.facishare.crm.sfa.lto.utils.SearchUtil
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.util.Lang
import com.facishare.paas.license.Result.ModuleInfoResult
import com.facishare.paas.license.pojo.ModuleInfoPojo
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.api.search.Wheres
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.impl.search.Where
import com.facishare.paas.metadata.util.SpringContextUtil
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.github.autoconf.ConfigFactory
import com.github.autoconf.api.IChangeListener
import com.github.autoconf.api.IChangeable
import com.github.autoconf.api.IChangeableConfig
import com.github.autoconf.api.IConfigFactory
import com.github.autoconf.base.ProcessInfo
import com.github.autoconf.helper.ConfigHelper
import com.google.common.collect.Lists
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.MockRepository
import org.springframework.context.ApplicationContext
import spock.lang.Shared
import spock.lang.Specification

import static org.powermock.reflect.Whitebox.setInternalState

abstract class BaseGroovyTest extends Specification {
    class MyChangeableConfig implements IChangeableConfig {

        @Override
        IChangeable getEventBus() {
            return null
        }

        @Override
        String getName() {
            return null
        }

        @Override
        byte[] getContent() {
            return null
        }
    }

    class MyConfigFactory implements IConfigFactory {

        @Override
        List<IChangeableConfig> getAllConfig() {
            return null
        }

        @Override
        IChangeableConfig getConfig(String name) {
            return null
        }

        @Override
        IChangeableConfig getConfig(String name, IChangeListener listener) {
            return null
        }

        @Override
        IChangeableConfig getConfig(String name, IChangeListener listener, boolean loadAfterRegister) {
            return null
        }

        @Override
        IChangeableConfig getConfig(String name, String sectionNames, IChangeListener listener, boolean loadAfterRegister) {
            return null
        }

        @Override
        boolean hasConfig(String name) {
            return false
        }

        @Override
        IChangeableConfig getConfigNotCreate(String name) {
            return null
        }

        @Override
        IChangeableConfig addConfig(IChangeableConfig config) {
            return null
        }
    }

    @Shared
    protected ActionContext actionContext
    @Shared
    protected ControllerContext controllerContext
    @Shared
    protected ServiceContext serviceContext
    @Shared
    protected RequestContext requestContext
    @Shared
    protected String tenantId
    @Shared
    protected String userId
    @Shared
    protected User user
    @Shared
    protected String appId

    def cleanupSpec() {
        println "start releasing memory..."

        MockRepository.clear()
        MockRepository.suppressStaticInitializers.clear()

        actionContext = null;
        controllerContext = null;
        serviceContext = null;
        requestContext = null;
        tenantId = null
        userId = null
        user = null
        appId = null
    }

    def setupSpec() {
        System.setProperty("spring.profiles.active", "fstest");
        System.setProperty("config.mode", "localNoUpdate");
//        initI18nClient()
        initRequestContext()
//        initSpringContext()
        initActionContext()
        initControllerContext()
    }


    def setup() {
        showMemory()

        removeConfigFactory()
        removeI18N()
    }

    def removeI18N() {
        long startTime = System.currentTimeMillis()
        setInternalState(I18N, "THREAD_LOCAL", new InheritableThreadLocal<>())
        println("BaseGroovyTest removeI18N costTime: " + (System.currentTimeMillis() - startTime))
    }

    def removeConfigFactory() {
        long startTime = System.currentTimeMillis()
//        ProcessInfo processInfo = PowerMockito.mock(ProcessInfo.class) //很慢
        PowerMockito.stub(PowerMockito.method(ConfigHelper.class, "getProcessInfo"))
                .toReturn(new ProcessInfo())
        println("#1 BaseGroovyTest ConfigHelper costTime: " + (System.currentTimeMillis() - startTime))

        startTime = System.currentTimeMillis()
//        IConfigFactory configFactory = PowerMockito.mock(IConfigFactory.class) //很慢
        PowerMockito.stub(PowerMockito.method(ConfigFactory.class, "getInstance"))
                .toReturn(new MyConfigFactory())
        println("#2 BaseGroovyTest ConfigFactory costTime: " + (System.currentTimeMillis() - startTime))

        startTime = System.currentTimeMillis()
//        IChangeable changeable = PowerMockito.mock(IChangeable.class) //很慢
        PowerMockito.stub(PowerMockito.method(IConfigFactory.class, "getConfig", String.class, IChangeListener.class))
                .toReturn(new MyChangeableConfig())
        println("#3 BaseGroovyTest ConfigFactory costTime: " + (System.currentTimeMillis() - startTime))
    }

    def showMemory() {
        Runtime runtime = Runtime.getRuntime()

        long totalMemory = runtime.totalMemory()
        long freeMemory = runtime.freeMemory()
        long usedMemory = totalMemory - freeMemory
        long maxMemory = runtime.maxMemory()

        long toMB = 1024 * 1024

        println "Total Memory: ${totalMemory / toMB} MB"
        println "Free Memory: ${freeMemory / toMB} MB"
        println "Used Memory: ${usedMemory / toMB} MB"
        println "Max Memory: ${maxMemory / toMB} MB"
    }

    protected initI18nClient() {
        def field = I18nClient.class.getDeclaredField("impl")
        field.setAccessible(true)

        def stub = Stub(I18nServiceImpl)
        field.set(I18nClient.getInstance(), stub)
        stub.getOrDefault(_ as String, _ as Long, _ as String, _ as String) >> {
            String key, Long tenantId, String lang, String defaultVal ->
                return defaultVal
        }
    }

    RequestContext initRequestContext() {
        initUser()
        this.user = new User(tenantId, userId)
        RequestContext.RequestContextBuilder requestContextBuilder = RequestContext.builder()
        requestContextBuilder.tenantId(tenantId)
        requestContextBuilder.user(user)
        requestContextBuilder.appId(appId)
        requestContextBuilder.contentType(RequestContext.ContentType.FULL_JSON)
        requestContextBuilder.requestSource(RequestContext.RequestSource.CEP)
        requestContextBuilder.lang(Lang.zh_CN)
        requestContext = requestContextBuilder.build()
        return requestContext
    }

    protected ServiceContext getServiceContext(String serviceName, String serviceMethod) {
        return new ServiceContext(requestContext, serviceName, serviceMethod)
    }

    protected ServiceContext getServiceContext(String serviceName, String serviceMethod, boolean isOutUser) {
        ServiceContext serviceContext = new ServiceContext(RequestContext.builder().tenantId(this.user.getTenantId())
                .user(new User(this.user.getTenantId(), this.user.getUserId(), "333333", "23212112")).appId("crm").build(), serviceName, serviceMethod);
        return serviceContext
    }

    void initUser() {
        this.tenantId = "23456";
        this.userId = "1000";
    }

    User initUser1() {
        return new User("32", "100", "32", "43")
    }

    void initSpringContext() {
        def stubApplicationContext = Stub(ApplicationContext)
        def util = new SpringContextUtil()
        util.setApplicationContext(stubApplicationContext)
        Map<Class, Object> map = new HashMap<>()
        stubApplicationContext.getBean(_ as Class) >> { Class requiredType ->
            map.computeIfAbsent(requiredType, { key ->
                Stub(requiredType)
            })
        }

        Map<String, Object> map2 = new HashMap<>()
        stubApplicationContext.getBean(_ as String, _ as Class) >> { String name, Class requiredType ->
            map2.computeIfAbsent(name, { key ->
                Stub(requiredType)
            })
        }
    }

    ActionContext initActionContext() {
        actionContext = Mock(ActionContext)
        actionContext.requestContext >> requestContext
        actionContext.getTenantId() >> tenantId
        actionContext.getUser() >> user
        actionContext.getAppId() >> appId
    }

    ControllerContext initControllerContext() {
        controllerContext = Mock(ControllerContext)
        controllerContext.requestContext >> requestContext
        controllerContext.getTenantId() >> tenantId
        controllerContext.getUser() >> user
        controllerContext.getAppId() >> appId
    }

    def getDescribe(String apiName) {
        ObjectDescribeBuilder.builder()
                .apiName(apiName)
                .displayName(apiName)
                .tenantId(tenantId)
                .build()
    }

    def getDescribeWithDefaultFields(String apiName, List<String> defaultFields) {
        def describe = ObjectDescribeBuilder.builder()
                .apiName(apiName)
                .tenantId(tenantId)
                .build()
        defaultFields.each {
            IFieldDescribe fieldDescribe = TextFieldDescribeBuilder.builder().apiName(it).label(it).build()
            describe.addFieldDescribe(fieldDescribe)
        }
        describe
    }

    def getDescribeWithDefaultFields(String apiName, String defaultFields) {
        def describe = ObjectDescribeBuilder.builder()
                .apiName(apiName)
                .tenantId(tenantId)
                .build()
        MasterDetailFieldDescribe masterDetailFieldDescribe = new MasterDetailFieldDescribe();
        masterDetailFieldDescribe.setApiName(defaultFields)
        masterDetailFieldDescribe.setActive(true)
        masterDetailFieldDescribe.setIsExtend(false)
        masterDetailFieldDescribe.setFieldNum(null)
        masterDetailFieldDescribe.setStatus("released")
        masterDetailFieldDescribe.setDefineType("package")
        describe.addFieldDescribe(masterDetailFieldDescribe)
        describe
    }

    def getObjectData(List<String> fieldList) {
        def objectData = new ObjectData();
        fieldList.each {
            objectData.set(it, "xxxx")
        }
        objectData.setTenantId(tenantId)
        objectData
    }

    def getObjectData(List<String> fieldList, List<Object> valueList) {
        IObjectData objectData = new ObjectData();
        fieldList.eachWithIndex { item, index ->
            objectData.set(item, valueList[index])
        }
        objectData
    }

    def getObjectDataList(List<String> fieldList, int size) {
        def objectDataList = new ArrayList<ObjectData>();
        for (i in 0..<size) {
            def objectData = new ObjectData();
            fieldList.each {
                objectData.set(it, "xxxx")
            }
            objectData.setDeleted(false)
            objectData.setTenantId(tenantId)
            objectDataList.add(objectData)
        }
        objectDataList
    }

    def getObjectDataListDifferentValue(List<String> fieldList, int size) {
        def objectDataList = new ArrayList<ObjectData>();
        for (i in 0..<size) {
            def objectData = new ObjectData();
            fieldList.each {
                objectData.set(it, "xxxx" + i)
            }
            objectData.setTenantId(tenantId)
            objectDataList.add(objectData)
        }
        objectDataList
    }

    def searchQueryAddFieldFilter(List<String> fields, List<Object> values) {
        def query = buildSearchQuery()
        fields.eachWithIndex { String f, int i ->
            List<IFilter> list = Lists.newArrayList();
            SearchUtil.fillFilterEq(list, f, values[i]);
            query.getFilters().addAll(list)
        }
        query
    }

    def buildSearchQueryNoFilter() {
        SearchTemplateQuery query = new SearchTemplateQuery()
        query.setNeedReturnCountNum(false)
        query.setPermissionType(0)
        query.setNeedReturnQuote(false)
        query.setOffset(0)
        query.setLimit(1000)
        query
    }

    def buildSearchQuery() {
        def query = buildSearchQueryNoFilter()
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "is_deleted", true);
        query.setFilters(filters)

        List<IFilter> filters2 = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters2, "biz_status", "normal");
        query.setFilters(filters2)
        Wheres wheres1 = new Wheres();
        wheres1.setConnector(Where.CONN.OR.toString());
        wheres1.setFilters(filters2);
        query.setWheres([wheres1]);
        query
    }

    def buildQueryResult() {
        QueryResult queryResult = new QueryResult();
        queryResult.setData(getObjectDataList(["account_id", "_id"], 3));
        queryResult
    }

    QueryResult<IObjectData> getQueryResult() {
        QueryResult<IObjectData> result = new QueryResult<>();
        result.setData(getObjectDataList(Lists.newArrayList("name"), 2))
        return result;
    }

    class OK extends RuntimeException {

    }


    ModuleInfoResult getModuleInfoResult(String str) {
        ModuleInfoResult moduleInfoResult = new ModuleInfoResult();
        List<ModuleInfoPojo> result = new ArrayList<>()
        ModuleInfoPojo moduleInfoPojo = new ModuleInfoPojo();
        moduleInfoPojo.setModuleCode(str)
        result.add(moduleInfoPojo)
        moduleInfoResult.setResult(result)
        return moduleInfoResult;
    }
}
