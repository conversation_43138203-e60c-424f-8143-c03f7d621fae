package com.facishare.crm.sfa

import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.paas.I18N
import com.github.autoconf.ConfigFactory
import com.github.autoconf.api.IChangeListener
import com.github.autoconf.api.IChangeable
import com.github.autoconf.api.IChangeableConfig
import com.github.autoconf.api.IConfigFactory
import com.github.autoconf.base.ProcessInfo
import com.github.autoconf.helper.ConfigHelper
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import spock.lang.Specification

import static org.powermock.reflect.Whitebox.setInternalState

@PrepareForTest([GrayUtil.class, I18N.class, ConfigFactory.class, ConfigHelper.class])
@SuppressStaticInitializationFor([
        "com.facishare.crm.sfa.utilities.util.GrayUtil",
        "com.facishare.paas.I18N",
])
abstract class RemoveUseless extends Specification {
    class MyChangeableConfig implements IChangeableConfig {

        @Override
        IChangeable getEventBus() {
            return null
        }

        @Override
        String getName() {
            return null
        }

        @Override
        byte[] getContent() {
            return null
        }
    }

    class MyConfigFactory implements IConfigFactory {

        @Override
        List<IChangeableConfig> getAllConfig() {
            return null
        }

        @Override
        IChangeableConfig getConfig(String name) {
            return null
        }

        @Override
        IChangeableConfig getConfig(String name, IChangeListener listener) {
            return null
        }

        @Override
        IChangeableConfig getConfig(String name, IChangeListener listener, boolean loadAfterRegister) {
            return null
        }

        @Override
        IChangeableConfig getConfig(String name, String sectionNames, IChangeListener listener, boolean loadAfterRegister) {
            return null
        }

        @Override
        boolean hasConfig(String name) {
            return false
        }

        @Override
        IChangeableConfig getConfigNotCreate(String name) {
            return null
        }

        @Override
        IChangeableConfig addConfig(IChangeableConfig config) {
            return null
        }
    }

    def setupSpec() {

    }

    def setup() {
        removeConfigFactory()
        removeI18N()
    }

    def removeI18N() {
        long startTime = System.currentTimeMillis()
        setInternalState(I18N, "THREAD_LOCAL", new InheritableThreadLocal<>())
        println("RemoveUseless removeI18N costTime: " + (System.currentTimeMillis() - startTime))
    }

    def removeConfigFactory() {
        long startTime = System.currentTimeMillis()
//        ProcessInfo processInfo = PowerMockito.mock(ProcessInfo.class) //很慢
        PowerMockito.stub(PowerMockito.method(ConfigHelper.class, "getProcessInfo"))
                .toReturn(new ProcessInfo())
        println("#1 RemoveUseless ConfigHelper costTime: " + (System.currentTimeMillis() - startTime))

        startTime = System.currentTimeMillis()
//        IConfigFactory configFactory = PowerMockito.mock(IConfigFactory.class) //很慢
        PowerMockito.stub(PowerMockito.method(ConfigFactory.class, "getInstance"))
                .toReturn(new MyConfigFactory())
        println("#2 RemoveUseless ConfigFactory costTime: " + (System.currentTimeMillis() - startTime))

        startTime = System.currentTimeMillis()
//        IChangeable changeable = PowerMockito.mock(IChangeable.class) //很慢
        PowerMockito.stub(PowerMockito.method(IConfigFactory.class, "getConfig", String.class, IChangeListener.class))
                .toReturn(new MyChangeableConfig())
        println("#3 RemoveUseless ConfigFactory costTime: " + (System.currentTimeMillis() - startTime))
    }
}