package com.facishare.crm.sfa.utilities.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.constants.IncentivePolicyRuleConstants;
import com.facishare.crm.constants.SimplePolicyConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.service.aggregatevalue.AggregateRuleDao;
import com.facishare.crm.sfa.predefine.service.aggregatevalue.AggregateRuleExt;
import com.facishare.crm.sfa.predefine.service.aggregatevalue.model.AggregateRuleModel;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.predefine.service.pricepolicy.RuleEngineLogicService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.model.PricePolicy;
import com.facishare.crm.sfa.predefine.service.pricepolicy.model.PriceRule;
import com.facishare.crm.sfa.predefine.service.pricepolicy.model.PriceRuleFilter;
import com.facishare.crm.sfa.predefine.service.pricepolicy.model.PriceRuleWhere;
import com.facishare.crm.sfa.predefine.service.pricepolicy.rest.dto.BaseEngine;
import com.facishare.crm.sfa.predefine.service.pricepolicy.rest.dto.SaveRule;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.expression.*;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.paas.reference.data.DeleteArg;
import com.facishare.paas.reference.data.EntityReferenceArg;
import com.facishare.paas.reference.service.EntityReferenceService;
import com.fxiaoke.helper.CollectionHelper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

public class PricePolicyUtils {
    private static final EntityReferenceService entityReferenceService = SpringUtil.getContext().getBean(EntityReferenceService.class);
    private static final RuleEngineLogicService ruleEngineLogicService = SpringUtil.getContext().getBean(RuleEngineLogicService.class);
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    private static final AggregateRuleDao aggregateRuleDao = SpringUtil.getContext().getBean(AggregateRuleDao.class);
    private static final List<String> combinationRuleType = Lists.newArrayList(PricePolicyConstants.PricePolicyRuleType.COMBINATION_GIFT.getRuleType(),
            PricePolicyConstants.PricePolicyRuleType.COMBINATION_PRICING.getRuleType());
    private static final ExpressionCalculateLogicService expressionCalculateLogicService = (ExpressionCalculateLogicService) SpringUtil.getContext().getBean("expressionCalculateLogicService");
    private static final BizConfigThreadLocalCacheService configService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    private PricePolicyUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 适用客户非指定时，移除待删除从对象
     */
    public static void processDetailData(IObjectData dbObjectData, IObjectData objectData, Map<String, List<IObjectData>> detailDataMap) {
        DetailObjectUtils.processDetailData(dbObjectData, objectData, detailDataMap, getSpecialFieldMap());
    }

    /**
     * 主属性非自增编号，手动设置为系统时间+4位随机数
     */
    public static void setDetailName(List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        LocalDateTime localDateTime = LocalDateTime.now();
        String prefix = localDateTime.format(dateTimeFormatter);
        objectDataList.forEach(r -> {
            if (Strings.isNullOrEmpty(r.getName())) {
                r.setName(prefix + String.format("%04d", ThreadLocalRandom.current().nextInt(10000)));
            }
        });
    }

    /**
     * 保存字段及聚合值的引用关系
     */
    public static void createReferenceRelation(String tenantId, String pricePolicyId,
                                               Map<String, Set<String>> objectFields,
                                               Set<String> aggregateValueIds) {
        List<EntityReferenceArg> referenceArgList = Lists.newArrayList();
        for (Map.Entry<String, Set<String>> entry : objectFields.entrySet()) {
            Set<String> fields = entry.getValue();
            for (String field : fields) {
                referenceArgList.add(EntityReferenceArg.builder()
                        .tenantId(tenantId)
                        .sourceType(PricePolicyConstants.REFERENCE_SOURCE_TYPE)
                        .sourceLabel(PricePolicyConstants.REFERENCE_SOURCE_LABEL)
                        .sourceValue(pricePolicyId)
                        .targetType(EntityReferenceService.DESCRIBE_FIELD_TYPE)
                        .targetValue(String.join(".", entry.getKey(), field))
                        .build());
            }
        }
        for (String aggregateId : aggregateValueIds) {
            referenceArgList.add(EntityReferenceArg.builder()
                    .tenantId(tenantId)
                    .sourceType(PricePolicyConstants.REFERENCE_SOURCE_TYPE)
                    .sourceLabel(PricePolicyConstants.REFERENCE_SOURCE_LABEL)
                    .sourceValue(pricePolicyId)
                    .targetType(EntityReferenceService.ADVANCED_PRICING)
                    .targetValue(aggregateId)
                    .build());
        }
        if (CollectionUtils.notEmpty(referenceArgList)) {
            entityReferenceService.create(referenceArgList);
        }
    }

    /**
     * 更新字段及聚合值的引用关系，先删后插
     */
    public static void updateReferenceRelation(String tenantId, String pricePolicyId,
                                               Map<String, Set<String>> objectFields,
                                               Set<String> aggregateValueIds) {
        entityReferenceService.delete(tenantId, PricePolicyConstants.REFERENCE_SOURCE_TYPE, pricePolicyId);
        createReferenceRelation(tenantId, pricePolicyId, objectFields, aggregateValueIds);
    }

    public static void deleteReferenceRelation(String tenantId, List<String> sourceIdList) {
        if (CollectionUtils.empty(sourceIdList)) {
            return;
        }
        List<DeleteArg> deleteArgList = Lists.newArrayList();
        for (String id : sourceIdList) {
            DeleteArg.DeleteArgBuilder deleteArgBuilder = DeleteArg.builder();
            deleteArgBuilder.sourceType(PricePolicyConstants.REFERENCE_SOURCE_TYPE);
            deleteArgBuilder.sourceValue(id);
            deleteArgBuilder.targetValue(null);
            deleteArgList.add(deleteArgBuilder.build());
        }
        entityReferenceService.delete(tenantId, deleteArgList);
    }

    public static boolean batchSaveRule(User user, IObjectData policyData, List<IObjectData> ruleDataList, String masterApiName, String detailApiName) {
        List<SaveRule.MacroGroup> macroGroupList = buildMacroGroupByPolicy(policyData, ruleDataList, masterApiName, detailApiName);
        if (CollectionUtils.empty(macroGroupList)) {
            return true;
        }
        buildMultiRule(policyData, ruleDataList, masterApiName, detailApiName, macroGroupList);

        if (macroGroupList.size() > ConfigConstant.POLICY_RULE_PARTITION_SIZE && GrayUtil.needPartitionSavePolicyRule(user.getTenantId())) {
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            List<List<SaveRule.MacroGroup>> partitionList = Lists.partition(macroGroupList, ConfigConstant.POLICY_RULE_PARTITION_SIZE);
            Set<Boolean> savedResult = Sets.newHashSet();
            BaseEngine.Context ruleEngineContext = buildRuleEngineContext(user);
            for (List<SaveRule.MacroGroup> groupList : partitionList) {
                SaveRule.Arg arg = new SaveRule.Arg();
                arg.setContext(ruleEngineContext);
                arg.setMacroGroupList(groupList);
                parallelTask.submit(() -> {
                    boolean isSaved = ruleEngineLogicService.batchSaveRule(arg, user);
                    savedResult.add(isSaved);
                });
            }
            try {
                boolean result = parallelTask.await(10, TimeUnit.SECONDS);
                if (!result) {
                    return false;
                }
            } catch (TimeoutException e) {
                return false;
            }
            return !savedResult.contains(Boolean.FALSE);
        } else {
            SaveRule.Arg arg = new SaveRule.Arg();
            arg.setContext(buildRuleEngineContext(user));
            arg.setMacroGroupList(macroGroupList);
            return ruleEngineLogicService.batchSaveRule(arg, user);
        }
    }

    private static void buildMultiRule(IObjectData policyData, List<IObjectData> ruleDataList, String masterApiName, String detailApiName, List<SaveRule.MacroGroup> macroGroupList) {
        if (Utils.PRICE_POLICY_API_NAME.equals(masterApiName) &&
                PricePolicyConstants.PolicyObjectType.MULTIPLE.equals(policyData.get(PricePolicyConstants.PricePolicyField.POLICY_OBJECT_TYPE))) {
            List<String> apiNames = policyData.get(PricePolicyConstants.PricePolicyField.MULTIPLE_OBJECT_API_NAME, List.class);
            for (String apiName : apiNames) {
                String ruleApiName = ruleDataList.get(0).get(PricePolicyConstants.RuleField.SOURCE_OBJECT_API_NAME, String.class);
                if (apiName.equals(ruleApiName)) {
                    continue;
                }
                List<IObjectData> ruleDataListNew = makeNewRule(ruleDataList, apiName);
                List<SaveRule.MacroGroup> macroGroupListNew = buildMacroGroupByPolicy(policyData, ruleDataListNew, masterApiName, detailApiName);
                if (CollectionUtils.notEmpty(macroGroupListNew)) {
                    macroGroupList.addAll(macroGroupListNew);
                }
            }
        }
    }

    private static List<IObjectData> makeNewRule(List<IObjectData> ruleDataList, String apiName) {
        List<IObjectData> ruleDataListNew = Lists.newArrayList();
        for (IObjectData rule : ruleDataList) {
            String multipleJson = rule.get(PricePolicyConstants.RuleField.MULTIPLE_JSON, String.class);
            JSONArray array = JSON.parseArray(multipleJson);
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsonObject = array.getJSONObject(i);
                String ruleApiName = jsonObject.getString("objectApiName");
                if (apiName.equals(ruleApiName)) {
                    IObjectData objectData1 = new ObjectData();
                    objectData1.fromJsonString(rule.toJsonString());
                    objectData1.set(PricePolicyConstants.RuleField.SOURCE_OBJECT_API_NAME, apiName);
                    objectData1.set(PricePolicyConstants.RuleField.EXECUTION_RESULT, jsonObject.getString(PricePolicyConstants.RuleField.EXECUTION_RESULT));
                    objectData1.set(PricePolicyConstants.RuleField.RULE_CONDITION, jsonObject.getString(PricePolicyConstants.RuleField.RULE_CONDITION));
                    ruleDataListNew.add(objectData1);
                    break;
                }
            }
        }

        return ruleDataListNew;
    }

    public static void deleteRule(User user, String policyId, List<String> ruleDataIds) {
        List<String> apiNameList = Lists.newArrayList(policyId);
        apiNameList.addAll(ruleDataIds);
        ruleEngineLogicService.deleteRule(user, apiNameList);
    }

    public static List<PriceRuleWhere> getPriceRuleWhereList(IObjectData objectData) {
        String ruleCondition = objectData.get(PricePolicyConstants.RuleField.RULE_CONDITION, String.class);
        return getPriceRuleWhereList(ruleCondition);
    }

    public static List<PriceRuleWhere> getPriceRuleWhereList(String ruleCondition) {
        if (Strings.isNullOrEmpty(ruleCondition)) {
            return Lists.newArrayList();
        }
        List<PriceRuleWhere> priceRuleWhereList = Lists.newArrayList();
        List<String> conditions = JSON.parseArray(ruleCondition, String.class);
        if (CollectionUtils.notEmpty(conditions)) {
            for (String condition : conditions) {
                if (Strings.isNullOrEmpty(condition)) {
                    continue;
                }
                priceRuleWhereList.add(JSON.parseObject(condition, PriceRuleWhere.class));
            }
        }
        return priceRuleWhereList;
    }

    public static List<IFieldDescribe> getFieldDescribe(User user, Map<String, IObjectDescribe> objectDescribeMap,
                                                        String objectApiName, String fieldName) {
        IObjectDescribe objectDescribe = objectDescribeMap.get(objectApiName);
        if (null == objectDescribe) {
            return Lists.newArrayList();
        }
        if (isReferenceField(fieldName)) {
            List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
            String[] fields = fieldName.split("\\.", 2);
            IFieldDescribe referenceFieldDescribe = objectDescribe.getFieldDescribe(fields[0]);
            if (null == referenceFieldDescribe) {
                return Lists.newArrayList();
            }
            fieldDescribeList.add(referenceFieldDescribe);
            String targetApiName = (String) referenceFieldDescribe.get("target_api_name");
            if (null == targetApiName) {
                return Lists.newArrayList();
            }
            IObjectDescribe targetObjectDescribe;
            if (objectDescribeMap.containsKey(targetApiName)) {
                targetObjectDescribe = objectDescribeMap.get(targetApiName);
            } else {
                targetObjectDescribe = serviceFacade.findObject(user.getTenantId(), targetApiName);
                if (null != targetObjectDescribe) {
                    objectDescribeMap.put(targetApiName, targetObjectDescribe);
                } else {
                    return Lists.newArrayList();
                }
            }
            IFieldDescribe targetFieldDescribe = targetObjectDescribe.getFieldDescribe(fields[1]);
            if (null != targetFieldDescribe) {
                fieldDescribeList.add(targetFieldDescribe);
            }
            return fieldDescribeList;
        } else {
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
            if (null == fieldDescribe) {
                return Lists.newArrayList();
            }
            return Lists.newArrayList(fieldDescribe);
        }
    }

    private static boolean isReferenceField(String fieldName) {
        return null != fieldName && fieldName.contains(".");
    }

    private static BaseEngine.Context buildRuleEngineContext(User user) {
        BaseEngine.Context context = new BaseEngine.Context();
        context.setTenantId(user.getTenantId());
        context.setUserId(user.getUpstreamOwnerIdOrUserId());
        return context;
    }

    private static List<SaveRule.MacroGroup> buildMacroGroupByPolicy(IObjectData policyData,
                                                                     List<IObjectData> ruleDataList, String masterApiName, String detailApiName) {
        if (CollectionUtils.empty(ruleDataList)) {
            return Lists.newArrayList();
        }
        List<SaveRule.MacroGroup> macroGroupList = Lists.newArrayList();
        SaveRule.MacroGroup macroGroup = new SaveRule.MacroGroup();
        if (Strings.isNullOrEmpty(policyData.getId())) {
            policyData.setId(serviceFacade.generateId());
        }
        String useApiName = getUseApiName(ruleDataList, masterApiName);

        //价格政策
        SaveRule.RuleBasicInfo basicInfo = buildRuleBasicInfo(policyData.getId() + useApiName, policyData.getName(),
                masterApiName);
        macroGroup.setRuleMacroGroup(basicInfo);

        //价格规则
        List<SaveRule.RuleGroup> ruleGroups = Lists.newArrayList();
        for (IObjectData objectData : ruleDataList) {
            if (Strings.isNullOrEmpty(objectData.getId())) {
                objectData.setId(serviceFacade.generateId());
            }
            List<SaveRule.Rule> rules = Lists.newArrayList();
            int ruleOrder = 1;
            List<String> parseList = Lists.newArrayList();
            List<PriceRuleWhere> priceRuleWhereList = PricePolicyUtils.getPriceRuleWhereList(objectData);
            for (PriceRuleWhere priceRuleWhere : priceRuleWhereList) {
                int startOrder = ruleOrder;
                int endOrder = addRulesByPricingRuleFilters(priceRuleWhere.getFilters(), rules, ruleOrder);
                ruleOrder = endOrder;
                String parseStr = generateRuleParse(startOrder, endOrder);
                if (Strings.isNullOrEmpty(parseStr)) {
                    parseList.add(String.valueOf(ruleOrder));
                } else {
                    parseList.add(parseStr);
                }
                ruleOrder++;
            }

            if (CollectionUtils.notEmpty(rules)) {
                SaveRule.RuleGroup ruleGroup = new SaveRule.RuleGroup();
                ruleGroup.setRuleCode(PricePolicyConstants.RULE_CONDITION_PREFIX.concat(objectData.getId() + useApiName));
                ruleGroup.setObjectApiName(rules.get(0).getObjectApiName());
                ruleGroup.setPriority(objectData.get(PricePolicyConstants.RuleField.PRIORITY, Integer.class, 1));
                ruleGroup.setFieldApiName(objectData.get(PricePolicyConstants.RuleField.GROUP_NO, String.class, "1"));
                ruleGroup.setRules(rules);
                if (CollectionUtils.notEmpty(parseList)) {
                    ruleGroup.setRuleParse(String.format("(%s)", joptsimple.internal.Strings.join(parseList, " or ")));
                }
                ruleGroups.add(ruleGroup);
            }

            SaveRule.MacroGroup detailGroup = buildMacroGroupByDetailRule(objectData, detailApiName, useApiName);
            if (null != detailGroup) {
                macroGroupList.add(detailGroup);
            }
        }

        if (CollectionUtils.notEmpty(ruleGroups)) {
            macroGroup.setRuleGroups(ruleGroups);
            macroGroupList.add(macroGroup);
        }
        return macroGroupList;
    }

    public static String getUseApiName(List<IObjectData> ruleDataList, String masterApiName) {
        String useApiName = "";
        IObjectData policyRuleData = ruleDataList.get(0);
        if (SFAPreDefine.PricePolicy.getApiName().equals(masterApiName) && configService.isMultipleObjectPricePolicy(policyRuleData.getTenantId())) {
            useApiName = policyRuleData.get(PricePolicyConstants.RuleField.SOURCE_OBJECT_API_NAME, String.class);
        }
        return useApiName;
    }

    private static SaveRule.RuleBasicInfo buildRuleBasicInfo(String id, String name, String objectName) {
        SaveRule.RuleBasicInfo basicInfo = new SaveRule.RuleBasicInfo();
        basicInfo.setApiName(id);
        basicInfo.setName(id);
        basicInfo.setObjectApiName(objectName);
        basicInfo.setStatus(1);
        return basicInfo;
    }

    private static SaveRule.MacroGroup buildMacroGroupByDetailRule(IObjectData ruleData, String detailApiName, String useApiName) {
        SaveRule.MacroGroup macroGroup = new SaveRule.MacroGroup();
        SaveRule.RuleBasicInfo basicInfo = buildRuleBasicInfo(ruleData.getId() + useApiName, ruleData.getName(),
                detailApiName);
        macroGroup.setRuleMacroGroup(basicInfo);
        String executionResult = ruleData.get(PricePolicyConstants.RuleField.EXECUTION_RESULT, String.class);
        if (Strings.isNullOrEmpty(executionResult)) {
            return null;
        }
        PriceRule.ExecutionRule detailRule = JSON.parseObject(executionResult, PriceRule.ExecutionRule.class);
        if (Strings.isNullOrEmpty(detailRule.getCondition())) {
            return null;
        }
        List<SaveRule.RuleGroup> ruleGroups = Lists.newArrayList();
        List<PriceRuleWhere> filterList = JSON.parseArray(detailRule.getCondition(), PriceRuleWhere.class);
        if (CollectionUtils.empty(filterList)) {
            return null;
        }
        List<SaveRule.Rule> rules = Lists.newArrayList();
        int ruleOrder = 1;
        List<String> parseList = Lists.newArrayList();
        for (PriceRuleWhere filter : filterList) {
            int startOrder = ruleOrder;
            int endOrder = addRulesByFilters(detailRule.getObjectApiName(), filter.getFilters(), rules, ruleOrder);
            ruleOrder = endOrder;
            String parseStr = generateRuleParse(startOrder, endOrder);
            if (Strings.isNullOrEmpty(parseStr)) {
                parseList.add(String.valueOf(ruleOrder));
            } else {
                parseList.add(parseStr);
            }
            ruleOrder++;
        }
        if (CollectionUtils.empty(rules)) {
            return null;
        }
        SaveRule.RuleGroup ruleGroup = new SaveRule.RuleGroup();
        ruleGroup.setRuleCode(ruleData.getId() + useApiName);
        ruleGroup.setObjectApiName(detailRule.getObjectApiName());
        ruleGroup.setPriority(ruleData.get(PricePolicyConstants.RuleField.PRIORITY, Integer.class, 1));
        ruleGroup.setFieldApiName(ruleData.get(PricePolicyConstants.RuleField.GROUP_NO, String.class, "1"));
        ruleGroup.setRules(rules);
        if (CollectionUtils.notEmpty(parseList)) {
            ruleGroup.setRuleParse(String.format("(%s)", joptsimple.internal.Strings.join(parseList, " or ")));
        }
        ruleGroups.add(ruleGroup);
        if (CollectionUtils.empty(ruleGroups)) {
            return null;
        }
        macroGroup.setRuleGroups(ruleGroups);
        return macroGroup;
    }

    public static String generateRuleParse(int startOrder, int endOrder) {
        if (endOrder <= startOrder) {
            return null;
        }
        List<String> indexList = Lists.newArrayList();
        for (int x = startOrder; x <= endOrder; x = x + 1) {
            indexList.add(String.valueOf(x));
        }
        return String.format("(%s)", joptsimple.internal.Strings.join(indexList, " and "));
    }

    private static int addRulesByPricingRuleFilters(List<PriceRuleFilter> priceRuleFilterList,
                                                    List<SaveRule.Rule> ruleList, int ruleOrder) {
        if (CollectionUtils.empty(priceRuleFilterList)) {
            return ruleOrder;
        }
        List<SaveRule.Rule> rules = Lists.newArrayList();
        for (PriceRuleFilter priceRuleFilter : priceRuleFilterList) {
            String objectName;
            String fieldType = null;
            if (PricePolicyConstants.FieldNameType.FIELD.toString().toLowerCase().equals(priceRuleFilter.getFieldNameType())) {
                objectName = priceRuleFilter.getObjectApiName();
            } else if (IncentivePolicyRuleConstants.FieldNameType.METRIC.getType().equals(priceRuleFilter.getFieldNameType())) {
                objectName = "IncentiveMetricObj";
                fieldType = priceRuleFilter.getFieldType();
            } else if (IncentivePolicyRuleConstants.FieldNameType.APL.getType().equals(priceRuleFilter.getFieldNameType())) {
                objectName = "APL";
                fieldType = priceRuleFilter.getFieldType();
            } else {
                objectName = SFAPreDefine.AggregateRule.getApiName();
                fieldType = PricePolicyConstants.FieldNameType.AGGREGATE.toString().toLowerCase();
            }
            rules.add(SaveRule.Rule.builder()
                    .objectApiName(objectName)
                    .fieldApiName(priceRuleFilter.getFieldName())
                    .fieldType(fieldType)
                    .operate(OperateMappingUtils.operateMap.getOrDefault(priceRuleFilter.getOperator().toString(), priceRuleFilter.getOperator().toString()))
                    .fieldValue(Lists.newArrayList(priceRuleFilter.getFieldValues()))
                    .valueType(priceRuleFilter.getFieldValueType())
                    .ruleOrder(ruleOrder++)
                    .rightValueType(priceRuleFilter.getRightValueType())
                    .fieldValueType(priceRuleFilter.getRightFieldValueType())
                    .build());
        }
        ruleList.addAll(rules);
        ruleOrder--;
        return ruleOrder;
    }

    private static int addRulesByFilters(String objectName, List<PriceRuleFilter> filterList,
                                         List<SaveRule.Rule> ruleList, int ruleOrder) {
        if (CollectionUtils.empty(filterList)) {
            return ruleOrder;
        }
        List<SaveRule.Rule> rules = Lists.newArrayList();
        for (PriceRuleFilter filter : filterList) {
            if (PricePolicyConstants.FieldNameType.AGGREGATE.toString().toLowerCase().equals(filter.getFieldNameType())) {
                rules.add(SaveRule.Rule.builder()
                        .objectApiName(SFAPreDefine.AggregateRule.getApiName())
                        .fieldApiName(filter.getFieldName())
                        .operate(OperateMappingUtils.operateMap.getOrDefault(filter.getOperator().toString(), filter.getOperator().toString()))
                        .fieldType(PricePolicyConstants.FieldNameType.AGGREGATE.toString().toLowerCase())
                        .fieldValue(filter.getFieldValues())
                        .ruleOrder(ruleOrder++)
                        .build());
            } else {
                rules.add(SaveRule.Rule.builder()
                        .objectApiName(objectName)
                        .fieldApiName(filter.getFieldName())
                        .operate(OperateMappingUtils.operateMap.getOrDefault(filter.getOperator().toString(), filter.getOperator().toString()))
                        .fieldValue(filter.getFieldValues())
                        .ruleOrder(ruleOrder++)
                        .build());
            }
        }
        ruleList.addAll(rules);
        ruleOrder--;
        return ruleOrder;
    }

    private static Map<String, String> getSpecialFieldMap() {
        Map<String, String> fieldObjectNameMap = Maps.newHashMapWithExpectedSize(3);
        fieldObjectNameMap.put(PricePolicyConstants.PricePolicyField.ACCOUNT_RANGE, SFAPreDefine.PricePolicyAccount.getApiName());
        return fieldObjectNameMap;
    }

    public static void mergeFieldMap(Map<String, Set<String>> sourceFields, Map<String, Set<String>> targetFields) {
        sourceFields.forEach((sk, sv) -> {
            if (CollectionUtils.notEmpty(sv)) {
                targetFields.computeIfAbsent(sk, k -> Sets.newHashSet()).addAll(sv);
            }
        });
    }


    /**
     * 获取当单聚合规则中的聚合字段及聚合条件中引用的字段
     *
     * @param user
     * @param pricePolicies
     * @return
     */
    public static Map<String, Set<String>> getAggregateFieldMap(User user, List<PricePolicy> pricePolicies) {
        Set<String> aggRuleIds = Sets.newHashSet();
        pricePolicies.forEach(pricePolicy -> aggRuleIds.addAll(pricePolicy.getAggRuleIds()));
        if (CollectionUtils.empty(aggRuleIds)) {
            return Maps.newHashMap();
        }
        return getAggregateField(user, Lists.newArrayList(aggRuleIds));
    }

    public static Map<String, Set<String>> getAggregateField(User user, List<String> aggRuleIds) {
        Map<String, Set<String>> result = Maps.newHashMap();
        if (CollectionUtils.empty(aggRuleIds)) {
            return result;
        }
        List<AggregateRuleModel> aggRules = aggregateRuleDao.getAggregateRuleByIds(user, aggRuleIds);
        AggregateRuleExt aggregateRuleExt = AggregateRuleExt.ofList(aggRules);
        PricePolicyUtils.mergeFieldMap(aggregateRuleExt.getAggregateFieldMap(), result);
        PricePolicyUtils.mergeFieldMap(aggregateRuleExt.getAggregateConditionFieldMap(), result);
        return result;
    }

    public static void handleProductRangeType(IObjectData policyRule, ActionContext actionContext, String modifyType) {
        String productRangeType;
        Set<String> productFields = Sets.newHashSet();
        if (PricePolicyConstants.ModifyType.MASTER.equals(modifyType)) {
            //计算聚合规则产品查询条件，只包含聚合条件
            productRangeType = handleRuleCondition(policyRule, actionContext, productFields, PricePolicyConstants.AggregateRuleType.aggregate);
        } else if (PricePolicyConstants.ModifyType.DETAIL.equals(modifyType) && combinationRuleType.contains(policyRule.get(PricePolicyConstants.RuleField.RULE_TYPE, String.class))) {
            //计算聚合规则产品查询条件，只包含组合条件
            productRangeType = handleRuleCondition(policyRule, actionContext, productFields, PricePolicyConstants.AggregateRuleType.group);
        } else {
            //计算修改量产品查询条件
            productRangeType = handleRuleExecution(policyRule, productFields);
        }
        policyRule.set(PricePolicyConstants.RuleField.PRODUCT_RANGE_TYPE, productRangeType);
        if (PricePolicyConstants.ProductRangeType.CONDITION.getProductRangeType().equals(productRangeType)) {
            policyRule.set(PricePolicyConstants.RuleField.PRODUCT_FIELDS, Lists.newArrayList(productFields));
        }
    }

    private static String handleRuleCondition(IObjectData policyRule, ActionContext actionContext, Set<String> productFields, String aggValueType) {
        List<PriceRuleWhere> priceRuleWhereList = PricePolicyUtils.getPriceRuleWhereList(policyRule.get(PricePolicyConstants.RuleField.RULE_CONDITION, String.class));
        List<Boolean> includeAggregates = Lists.newArrayList();
        List<List<String>> aggregateIds = Lists.newArrayList();
        for (PriceRuleWhere priceRuleWhere : priceRuleWhereList) {
            List<PriceRuleFilter> priceRuleFilterList = priceRuleWhere.getFilters();
            if (CollectionUtils.empty(priceRuleFilterList)) {
                continue;
            }
            boolean includeAgg = false;
            List<String> filterAggregateIds = Lists.newArrayList();
            for (PriceRuleFilter priceRuleFilter : priceRuleFilterList) {
                if (PricePolicyConstants.FieldNameType.AGGREGATE.toString().toLowerCase().equals(priceRuleFilter.getFieldNameType()) && aggValueType.equals(priceRuleFilter.getAggValueType())) {
                    includeAgg = true;
                    filterAggregateIds.add(priceRuleFilter.getFieldName());
                }
            }
            includeAggregates.add(includeAgg);
            aggregateIds.add(filterAggregateIds);
        }
        if (includeAggregates.contains(false)) {
            return PricePolicyConstants.ProductRangeType.ALL.getProductRangeType();
        } else {
            return containProduct(aggregateIds, actionContext, productFields);
        }
    }

    private static String containProduct(List<List<String>> aggregateIds, ActionContext actionContext, Set<String> productFields) {
        List<String> aggregateAllIds = Lists.newArrayList();
        aggregateIds.forEach(aggregateAllIds::addAll);
        if (CollectionHelper.isEmpty(aggregateAllIds)) {
            return PricePolicyConstants.ProductRangeType.ALL.getProductRangeType();
        }
        //查询所有聚合规则
        List<AggregateRuleModel> aggregateRuleList = aggregateRuleDao.getAggregateRuleByIds(actionContext.getUser(), aggregateAllIds);
        List<Boolean> includeProducts = Lists.newArrayList();
        for (List<String> filterAggregateIds : aggregateIds) {
            boolean includeProduct = false;
            for (String str : filterAggregateIds) {
                AggregateRuleModel aggregateRuleModel = aggregateRuleList.stream().filter(o -> str.equals(o.getAggregateRuleId())).findFirst().orElse(null);
                if (aggregateRuleModel == null) {
                    continue;
                }
                //处理聚合规则中是否包含产品及产品字段
                Set<String> productField = Sets.newHashSet();
                if (SFAPreDefine.SalesOrderProduct.getApiName().equals(aggregateRuleModel.getAggregateObject()) ||
                        SFAPreDefine.QuoteLines.getApiName().equals(aggregateRuleModel.getAggregateObject())) {
                    if (StringUtils.isNotEmpty(aggregateRuleModel.getCondition())) {
                        productField = handleCondition(aggregateRuleModel.getCondition());
                    }
                }
                if (CollectionHelper.isNotEmpty(productField)) {
                    includeProduct = true;
                    productFields.addAll(productField);
                }
            }
            includeProducts.add(includeProduct);
        }
        if (includeProducts.contains(false)) {
            return PricePolicyConstants.ProductRangeType.ALL.getProductRangeType();
        } else {
            return PricePolicyConstants.ProductRangeType.CONDITION.getProductRangeType();
        }
    }

    private static String handleRuleExecution(IObjectData policyRule, Set<String> productFields) {
        PriceRule.ExecutionRule executionRule = PricePolicyUtils.parseRule(policyRule.get(PricePolicyConstants.RuleField.EXECUTION_RESULT, String.class));
        if (executionRule == null) {
            return PricePolicyConstants.ProductRangeType.ALL.getProductRangeType();
        }
        Set<String> fields = handleCondition(executionRule.getCondition());
        if (CollectionHelper.isNotEmpty(fields)) {
            productFields.addAll(fields);
            return PricePolicyConstants.ProductRangeType.CONDITION.getProductRangeType();
        } else {
            return PricePolicyConstants.ProductRangeType.ALL.getProductRangeType();
        }
    }

    private static Set<String> handleCondition(String condition) {
        Set<String> fields = Sets.newHashSet();
        List<PriceRuleWhere> priceRuleWhereList = JSON.parseArray(condition, PriceRuleWhere.class);
        for (PriceRuleWhere priceRuleWhere : priceRuleWhereList) {
            for (PriceRuleFilter priceRuleFilter : priceRuleWhere.getFilters()) {
                String[] fieldArray = priceRuleFilter.getFieldName().split("\\.");
                String field = PricePolicyUtils.handleField(fieldArray);
                if (field != null) {
                    fields.add(field);
                }
            }
        }
        return fields;
    }

    public static PriceRule.ExecutionRule parseRule(String text) {
        return JSON.parseObject(text, PriceRule.ExecutionRule.class);
    }

    public static String handleField(String[] fieldArray) {
        String field = null;
        if (PricePolicyConstants.PRODUCT_ID.equals(fieldArray[0])) {
            if (fieldArray.length > 1) {
                field = fieldArray[1];
            } else {
                field = DBRecord.ID;
            }
        }
        return field;
    }

    public static void removeListHeaderButton(StandardListHeaderController.Result result) {
        if (null == result.getLayout()) {
            return;
        }
        ILayout layout = new Layout(result.getLayout());
        removeImportBtn(layout);
    }

    public static void removeImportBtn(ILayout layout) {
        Optional.ofNullable(layout)
                .map(ILayout::getButtons)
                .filter(CollectionUtils::notEmpty)
                .ifPresent(buttons -> {
                    buttons.removeIf(x -> ObjectAction.BATCH_IMPORT.getActionCode().equals(x.getAction()));
                    layout.setButtons(buttons);
                });
    }

    /**
     * 是否是通用模式
     *
     * @param objectData 对象数据
     * @return boolean
     */
    public static boolean isGeneralMode(IObjectData objectData) {
        String modeType = objectData.get(SimplePolicyConstants.ModeType.ModeType, String.class);
        //简易模式或者通用模式
        return StringUtils.isNotBlank(modeType) && !SimplePolicyConstants.ModeType.COMPLEX.equals(modeType);

    }

    public static BigDecimal calculateDefaultVal(User user,
                                                 int decimalPlaces,
                                                 PriceRule.ExecuteExpression exp,
                                                 List<IObjectData> detailDataList,
                                                 IObjectData masterData,
                                                 Map<String, String> aggValueMap, String objectApiName) {
        String defaultValue = exp.getRight();
        Map<String, IObjectDescribe> apiNameToDescribe = Maps.newHashMap();
        IObjectData data = CollectionUtils.empty(detailDataList) ? null : detailDataList.get(0);
        IObjectDescribe objectDescribe = apiNameToDescribe.computeIfAbsent(objectApiName, key -> serviceFacade.findObject(user.getTenantId(), key));
        if (StringUtils.isNotBlank(PricePolicyConstants.MASTER_DETAIL_API_NAME.get(objectApiName))) {
            data = masterData;
        }
        //构造SimpleExpression
        SimpleExpression simpleExpression = SimpleExpression.builder()
                .id(serviceFacade.generateId())
                .expression(defaultValue)
                .returnType("number")
                .nullAsZero(true)
                .decimalPlaces(decimalPlaces)
                .build();
        //构造Expression并解析自定义变量
        Expression expression = ExpressionFactory.createExpression(objectDescribe, simpleExpression);
        List<ExpressionVariableFactory.ExtVariable> extVariables = expression.getExtVariablesByType("AGGR");
        List<SimpleExpression.VariableInfo> extVariableInfos = extVariables.stream()
                .map(x -> SimpleExpression.VariableInfo.of(x.getName(), "number"))
                .collect(Collectors.toList());
        simpleExpression.setExtVariables(extVariableInfos);
        Map<String, Object> result = Maps.newHashMap();
        aggValueMap.forEach((aggregateId, value) -> {
            result.put("EXT#AGGR#".concat(aggregateId), value);
        });
        //调接口执行计算，calculateResult的key是"test_exp__c"，value是计算结果
        Map<String, Object> calculateResult = expressionCalculateLogicService.calculateWithExpression(objectDescribe, data, result, Lists.newArrayList(simpleExpression));
        //取出计算结果
        return (BigDecimal) calculateResult.getOrDefault(simpleExpression.getId(), BigDecimal.ZERO);
    }

    /**
     * 多对象冗余到规则中
     */
    public static void fillMultiApiName(List<IObjectData> objectDataList, IObjectData masterData) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        Object multiApiName = masterData.get(PricePolicyConstants.PricePolicyField.MULTIPLE_OBJECT_API_NAME);
        objectDataList.forEach(x -> x.set(PricePolicyConstants.PricePolicyField.MULTIPLE_OBJECT_API_NAME, multiApiName));
    }
}
