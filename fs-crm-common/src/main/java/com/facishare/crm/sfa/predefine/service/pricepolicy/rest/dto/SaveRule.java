package com.facishare.crm.sfa.predefine.service.pricepolicy.rest.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

public interface SaveRule {
    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Arg extends BaseEngine.Arg {
        List<MacroGroup> macroGroupList;
    }

    @Data
    class MacroGroup {
        RuleBasicInfo ruleMacroGroup;
        List<RuleGroup> ruleGroups;
    }

    @Data
    class RuleBasicInfo {
        @JSONField(name = "entityId")
        @JsonProperty("entityId")
        @SerializedName("entityId")
        String objectApiName;
        String apiName;
        String name;
        @JSONField(name = "isRealTime")
        @JsonProperty("isRealTime")
        @SerializedName("isRealTime")
        boolean realTimeUpdate;
        /**
         * 规则状态: 1启用，0停用
         */
        int status;
        String remark;
        @JSONField(name = "result")
        @JsonProperty("result")
        @SerializedName("result")
        /**
         * 结果规则，传json字符串
         */
                String ruleResult;
    }

    @Data
    class RuleGroup {
        @JSONField(name = "entityId")
        @JsonProperty("entityId")
        @SerializedName("entityId")
        String objectApiName;
        /**
         * 加减多少分
         */
        @JSONField(name = "result")
        @JsonProperty("result")
        @SerializedName("result")
        String calculateScore;
        @JSONField(name = "category")
        @JsonProperty("category")
        @SerializedName("category")
        String fieldApiName;
        int shouldStopIfMatched = 0;
        String ruleParse;
        int priority;
        List<Rule> rules;
        String ruleCode;
    }

    @Data
    @Builder
    class Rule {
        @JSONField(name = "entityId")
        @JsonProperty("entityId")
        @SerializedName("entityId")
        private String objectApiName;
        int ruleOrder;
        @JSONField(name = "fieldName")
        @JsonProperty("fieldName")
        @SerializedName("fieldName")
        String fieldApiName;
        String operate;
        List<String> fieldValue;
        String fieldType;
        String valueType;
        private String rightValueType;
        private String fieldValueType;
    }


    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Result extends BaseEngine.Result<List<String>> {
    }
}
