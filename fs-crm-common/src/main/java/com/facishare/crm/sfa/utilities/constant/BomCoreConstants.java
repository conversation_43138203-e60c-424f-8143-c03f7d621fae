package com.facishare.crm.sfa.utilities.constant;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * BomCore 常量类
 * @IgnoreI18nFile
 */
public interface BomCoreConstants {

    String DESC_API_NAME = "BomCoreObj";

    String FIELD_PRODUCT_ID = "product_id";

    String FIELD_CORE_VERSION = "core_version";

    String FIELD_CATEGORY = "category";

    String FIELD_PURPOSE = "purpose";

    String FIELD_REMARK = "remark";

    String SALE_STRATEGY = "sale_strategy";

    String FIELD_ATTRIBUTE_CONSTRAINT_ID = "attribute_constraint_id";

    String BOM_CORE_NEED_CREATE_CALLBACK_DATA_KEY = "bom_core_need_create_callback_data_key";

    String BOM_CORE_APPROVE_BOM_GROUP_RELATE_ID_KEY = "bom_core_approve_bom_group_relate_id_key";

    String BOM_CORE_APPROVE_GROUP_KEY = "bom_core_approve_group_key";

    List<String> FILTER_QUERY_FIELD = Lists.newArrayList(FIELD_CATEGORY,IObjectData.ID);

    List<String> SUPPORT_CREATBOMOBJ_LIST = Lists.newArrayList(Utils.SALES_ORDER_PRODUCT_API_NAME, Utils.SALE_CONTRACT_LINE_API_NAME, Utils.QUOTE_LINES_API_NAME);
    List<String> SUPPORT_CREATBOMOBJ_MASTER_LIST = Lists.newArrayList(Utils.SALES_ORDER_API_NAME, Utils.SALE_CONTRACT_API_NAME, Utils.QUOTE_API_NAME);

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ProductGroupArg{
        private List<ObjectDataDocument> groupToAdd;
        private List<ObjectDataDocument> groupToUpdate;
        private List<ObjectDataDocument> groupToDelete;
    }

    enum category {
        configure("configure", "配置BOM"), standard("standard", "标准BOM");
        private final String value;
        private final String name;

        category(String status, String name) {
            this.value = status;
            this.name = name;
        }

        public String getValue() {
            return value;
        }
    }

    enum purpose {
        sale("sale", "销售BOM"), service("service", "服务BOM");
        private final String value;
        private final String name;

        purpose(String status, String name) {
            this.value = status;
            this.name = name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 根据下单选配结果生成标准bom的策略
     */
    enum BomGeneratedStrategy {
        NONE(0, "不生成"), AUTO(1, "自动"), MANUAL(2, "手动");
        private int strategy;
        private String name;

        BomGeneratedStrategy(int strategy, String name) {
            this.strategy = strategy;
            this.name = name;
        }

        public static BomGeneratedStrategy getStrategy(int strategy) {
            return Arrays.stream(BomGeneratedStrategy.values()).filter(st -> st.strategy == strategy).findFirst().orElse(NONE);
        }
    }

    enum SaleStrategy {
        whole("whole", "整套售卖"), sub("sub", "子件售卖");
        private final String value;
        private final String name;

        SaleStrategy(String status, String name) {
            this.value = status;
            this.name = name;
        }

        public String getValue() {
            return value;
        }
    }
}