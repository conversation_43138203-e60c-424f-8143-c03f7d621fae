package com.facishare.crm.sfa.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.task.model.AsyncTaskMessage;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.github.autoconf.ConfigFactory;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;

import javax.annotation.PreDestroy;

/**
 * 异步任务生产者
 *
 * <AUTHOR>
 */
public class AsyncTaskCommonProducer {
    private String topic;
    private AutoConfMQProducer producer;

    public void init(String mqProducerConfigName, String mqTopicConfigKey) {
        producer = new AutoConfMQProducer(mqProducerConfigName);
        ConfigFactory.getConfig(mqProducerConfigName, c -> topic = c.get(mqTopicConfigKey));
    }

    public void initBySection(String configName, String sectionNames) {
        producer = new AutoConfMQProducer(configName, sectionNames);
    }

    @PreDestroy
    public void destroy() {
        producer.close();
    }

    /**
     * 创建异步任务
     *
     * @param biz         任务标识
     * @param messageBody 消息体
     */
    public void create(@NonNull String biz, String messageBody) {
        create(biz, messageBody, null, null, null);
    }

    /**
     * @param biz            任务标识
     * @param messageBody    消息体
     * @param messageKey     消息关键字，方便控制台搜索
     * @param delayTimeLevel 延迟发送级别 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     */
    public void create(@NonNull String biz, String messageBody, String messageKey, Integer delayTimeLevel) {
        create(biz, messageBody, messageKey, null, delayTimeLevel);
    }

    /**
     * 创建异步任务
     *
     * @param biz         任务标识
     * @param messageBody 消息体
     * @param messageKey  消息关键字，方便控制台搜索
     */
    public void create(@NonNull String biz, String messageBody, String messageKey) {
        create(biz, messageBody, messageKey, null, null);
    }

    /**
     * 创建异步任务
     *
     * @param biz         任务标识
     * @param messageBody 消息体
     * @param queueMod    MQ取模的值，消息要求有序队列时设置此值
     */
    public void create(@NonNull String biz, String messageBody, Integer queueMod) {
        create(biz, messageBody, null, queueMod, null);
    }

    /**
     * 创建异步任务
     *
     * @param biz            任务标识
     * @param messageBody    消息体
     * @param messageKey     消息关键字，方便控制台搜索
     * @param queueMod       MQ取模的值，消息要求有序队列时设置此值
     * @param delayTimeLevel 延迟消息等级
     */
    public void create(@NonNull String biz, String messageBody, String messageKey, Integer queueMod, Integer delayTimeLevel) {
        AsyncTaskMessage asyncTaskMessage = AsyncTaskMessage.builder()
                .biz(biz)
                .messageBody(messageBody)
                .queueMod(queueMod)
                .messageKey(messageKey)
                .delayTimeLevel(delayTimeLevel)
                .build();
        create(asyncTaskMessage);
    }

    /**
     * 创建异步任务
     *
     * @param asyncTaskMessage
     */
    private void create(AsyncTaskMessage asyncTaskMessage) {
        Message message;
        if (StringUtils.isBlank(topic)) {
            message = new Message(producer.getDefaultTopic(), asyncTaskMessage.getBiz()
                    , asyncTaskMessage.getMessageKey(), asyncTaskMessage.getMessageBody().getBytes());
        } else {
            message = new Message(topic, asyncTaskMessage.getBiz()
                    , asyncTaskMessage.getMessageKey(), asyncTaskMessage.getMessageBody().getBytes());
        }
        int delayTimeLevel = asyncTaskMessage.getDelayTimeLevel() != null ? asyncTaskMessage.getDelayTimeLevel() : 0;
        if (delayTimeLevel > 0) {
            message.setDelayTimeLevel(delayTimeLevel);
        }
        String messageBody = asyncTaskMessage.getMessageBody();
        if (isJsonObject(messageBody)) {
            JSONObject messageJson = JSON.parseObject(messageBody);
            if (messageJson.containsKey("tenantId")) {
                message.putUserProperty("x-fs-ei", messageJson.getString("tenantId"));
            }
        }
        SendResult sendResult;
        if (asyncTaskMessage.getQueueMod() != null) {
            sendResult = producer.send(message,
                    (mqs, msg, arg) -> mqs.get(arg.hashCode() & Integer.MAX_VALUE % mqs.size()),
                    asyncTaskMessage.getQueueMod());
        } else {
            sendResult = producer.send(message);
        }

        if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
            throw new RuntimeException("send mq failed. " + sendResult.getSendStatus());
        }
    }

    private static boolean isJsonObject(String jsonStr) {
        if (StringUtils.isEmpty(jsonStr)) {
            return false;
        } else {
            try {
                JSON.parseObject(jsonStr);
                return true;
            } catch (Exception e) {
                return false;
            }
        }
    }
}
