package com.facishare.crm.sfa.predefine.service.cpq;

import com.facishare.crm.sfa.predefine.service.cpq.model.BomTreeModel;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.appframework.common.util.StopWatch;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/6/29 11:06 上午
 * @illustration  bomV3 核心接口
 */
public interface BomCoreV3Service {


    /**
     * 保存BOM树
     * @param user
     * @param bomTree
     * @param deletedBomAndGroupList
     * @param rootProductId
     * @param stopWatch
     * @param arg
     */
    List<IObjectData> saveBomTreeV3(User user, List<ObjectDataDocument> bomTree, List<ObjectDataDocument> deletedBomAndGroupList, String rootProductId, StopWatch stopWatch, BomTreeModel.Arg arg);


    /**
     * 同步bom树
     *
     * @param user
     * @param bomTree
     * @param rootBomId
     * @param allNodeList
     * @return Map<String, List<ObjectDataDocument>>
     */
    Map<String, List<ObjectDataDocument>> analyzeBOMV3Tree(User user, List<ObjectDataDocument> bomTree, String rootBomId, int total, List<IObjectData> allNodeList,String rootProductId);


    /**
     * 创建根节点
     *
     * @param user
     * @param bomIdToProductId
     * @param bomDesc
     * @param masterData
     * @return
     */
    List<IObjectData> bulkCreateBomRootNode(User user, Map<String, String> bomIdToProductId, IObjectDescribe bomDesc, IObjectData masterData);


    /**
     * 更新产品的组合状态
     *
     * @param user
     * @param ids
     * @param isPackage
     */
    void updateProductIsPackage(User user, List<String> ids, Boolean isPackage);


    /**
     * 获取根节点ID
     *
     * @param user
     * @param rootProductId
     * @return
     */
    IObjectData getRootBomData(User user, String rootProductId);

    /**
     * 同步bom到其他节点
     * @param user
     * @param nodeList
     * @param node
     * @param same
     */
    List<String> execute(User user, List<BomTreeModel.SubParam> nodeList, BomTreeModel.SubParam node, boolean same);

    /**
     * 校验bom节点数量
     * @param tenantId
     * @param total
     */
    void checkQuantity(String tenantId, int total);

    /**
     * 删除bom同时删除属性约束关系
     * @param user
     * @param masterData
     * @param dataList
     */
    void checkBomCycle(User user, IObjectData masterData, List<IObjectData> dataList,int depth);

    /**
     * 主从类型校验
     * @param detailList
     * @param objectData
     */
    void checkType(List<IObjectData> detailList, IObjectData objectData) ;
    /**
     * 非标产品校验
     * @param objectData
     * @param user
     */
    void checkNonStandardProduct(IObjectData objectData, User user) ;

    /**
     * 售卖方式校验
     * @param objectDescribe
     * @param tenantId
     * @param nodeCoreList
     * @param saleStrategy
     */
    void checkSaleStrategy(IObjectDescribe objectDescribe, String tenantId, List<String> nodeCoreList, String saleStrategy);
}
