package com.facishare.crm.sfa.utilities.util;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.rest.SettingRestApi;
import com.facishare.crm.rest.dto.SetConfigModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by renlb on 2019/1/17.
 */
public class SFAConfigUtil {

    private static final ConfigService configService = SpringUtil.getContext().getBean(ConfigService.class);
    private static final ObjectDataServiceImpl objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);

    public static void setConfigValue(String tenantId, String userId, String key, String value) {
        SettingRestApi restApi = SpringUtil.getContext().getBean(SettingRestApi.class);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-fs-ei", tenantId);
        headerMap.put("x-fs-userInfo", User.SUPPER_ADMIN_USER_ID);
        restApi.setConfigValue(SetConfigModel.Arg.builder().key(key).value(value).build(), headerMap);
    }

    public static String getConfigValue(String tenantId, String configKey, String userId) {
        User user = new User(tenantId, userId);
        String queryRst = configService.findTenantConfig(user, configKey);
        if (com.google.common.base.Strings.isNullOrEmpty(queryRst)) {
            return getDefaultConfigValue(configKey);
        } else {
            return queryRst;
        }
    }

    public static boolean isConfigOpen(String tenantId, String key) {
        try {
            List<Map> result = objectDataService.findBySql(tenantId, ConcatenateSqlUtils.getConfigSql(tenantId, key));
            if (CollectionUtils.notEmpty(result)) {
                return Objects.equals(result.get(0).get("value_string"), "1");
            }
        } catch (MetadataServiceException e) {
        }
        return false;
    }

    public static Map<String, String> getConfigValues(String tenantId, String userId, String[] configKeys) {
        List<String> keys = Lists.newArrayList(configKeys);
        User user = new User(tenantId, userId);
        Map<String, String> queryRst = configService.queryTenantConfigs(user, keys);
        keys.forEach(x -> {
            if (!queryRst.containsKey(x)) {
                queryRst.put(x, ConfigType.getConfigType(x).getDefaultValue());
            }
        });
        return queryRst;

    }

    public static boolean mustHaveDetail(String tenantId) {
        boolean needDetail = false;
        String configValue = getConfigValue(tenantId, "16", User.SUPPER_ADMIN_USER_ID);
        if (Strings.isNullOrEmpty(configValue)) {
            configValue = "1,0,0";
        }
        String[] strArray = configValue.split(",", 3);

        if (strArray.length > 0) {
            if (Objects.equals(strArray[0], "1")) {
                needDetail = true;
            }

            if (strArray.length > 2) {
                if (Objects.equals(strArray[2], "1")) {
                    needDetail = true;
                }
            }
        }

        return needDetail;
    }

    public static boolean hasErpStock(String tenantId) {
        String configValue = getConfigValue(tenantId, "erp_stock_switch", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "2");
    }

    public static boolean hasDhtStock(String tenantId) {
        String configValue = getConfigValue(tenantId, "dht_stock_switch", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "2") || Objects.equals(configValue, "3");
    }

    public static boolean isStockEnable(String tenantId) {
        String configValue = getConfigValue(tenantId, "dht_stock_switch", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "2");
    }

    public static boolean hasDeliveryNote(String tenantId) {
        String configValue = getConfigValue(tenantId, "delivery_note_status", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "2");
    }

    public static boolean isNewOpportunityEnabled(String tenantId) {
        String configValue = getConfigValue(tenantId, "config_newopportunity_open", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "open");
    }

    public static boolean isDetailRepeatable(String tenantId) {
        String configValue = getConfigValue(tenantId, "43", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean isCustomerAccountEnabled(String tenantId) {
        String configValue = getConfigValue(tenantId, "29", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean isDeliveryNoteEnabled(String tenantId) {
        String configValue = getConfigValue(tenantId, "33", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean isStockEnabled(String tenantId) {
        String configValue = getConfigValue(tenantId, "30", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean isAllowNoRightSeeCompleteCustomerName(String tenantId, String userId) {
        //String configValue = getConfigValue(tenantId, "7", User.SUPPER_ADMIN_USER_ID);
        String configValue = getConfigValue(tenantId, "7", userId);
        return Objects.equals(configValue, "1");
    }

    public static boolean isPartnerOpen(String tenantId) {
        String configValue = getConfigValue(tenantId, "config_partner_open", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "open");
    }

    public static boolean isMultiUnitOpen(String tenantId) {
        String configValue = getConfigValue(tenantId, "multiple_unit", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean isCPQ(String tenantId) {
        String configValue = getConfigValue(tenantId, "cpq", User.SUPPER_ADMIN_USER_ID);
        if (Objects.equals(configValue, "1")) {
            return true;
        }
        return isSimpleBom(tenantId);
    }

    public static boolean isSimpleBom(String tenantId) {
        String configValue = getConfigValue(tenantId, "simple_cpq", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean isOpenBomCoreExplosionDiagramSwitch(String tenantId) {
        String configValue = getConfigValue(tenantId, "bom_core_explosion_diagram_switch", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean enableBom(String tenantId) {
        String configValue = getConfigValue(tenantId, "cpq", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean checkBomTree(String tenantId) {
        String configValue = getConfigValue(tenantId, ConfigType.CHECK_BOM_TREE.getKey(), User.SUPPER_ADMIN_USER_ID);
        return !Objects.equals(configValue, "1");
    }

    public static String getDefaultConfigValue(String key) {
        if (ConfigType.getConfigType(key).equals(ConfigType.NONE)) {
            return "";
        } else {
            return ConfigType.getConfigType(key).getDefaultValue();
        }
    }

    public static boolean isSpuOpen(String tenantId) {
        String configValue = getConfigValue(tenantId, ConfigType.SPU.getKey(), User.SUPPER_ADMIN_USER_ID);
        return "1".equals(configValue);
    }

    public static boolean isSaleContractOpen(String tenantId) {
        return StringUtils.equals("1", getConfigValue(tenantId, ConfigType.MODULE_SALE_CONTRACT.getKey(), User.SUPPER_ADMIN_USER_ID));
    }

    public static boolean priceBookEnabled(String tenantId) {
        return Objects.equals("1", getConfigValue(tenantId, ConfigType.IS_PRICE_BOOK_ENABLED.getKey(), User.SUPPER_ADMIN_USER_ID));
    }

    public static boolean attributeEnabled(String tenantId) {
        return Objects.equals("1", getConfigValue(tenantId, ConfigType.IS_OPEN_ATTRIBUTE.getKey(), User.SUPPER_ADMIN_USER_ID));
    }

    public static boolean isCustomerAccountEnable(String tenantId){
        return Objects.equals("2", getConfigValue(tenantId, ConfigType.IsNewCustomerAccountEnabled.getKey(), User.SUPPER_ADMIN_USER_ID));
    }

    public static boolean isAccountAuthEnable(String tenantId){
        return Objects.equals("2", getConfigValue(tenantId, ConfigType.ACCOUNT_AUTH.getKey(), User.SUPPER_ADMIN_USER_ID));
    }



    public static boolean isAccountCheckEnable(String tenantId){
        return Objects.equals("2", getConfigValue(tenantId, ConfigType.ACCOUNT_CHECK_ENABLE.getKey(), User.SUPPER_ADMIN_USER_ID));
    }

    public static boolean advancedFormulaEnable(String tenantId) {
        String configValue = getConfigValue(tenantId, ConfigType.SFA_ADVANCED_FORMULA_OPEN.getKey(), User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean quoterEnable(String tenantId) {
        String configValue = getConfigValue(tenantId, ConfigType.IS_OPEN_QUOTER.getKey(), User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean isSalesOrderPayDirectlyEnable(String tenantId) {
        String configValue = getConfigValue(tenantId, "is_sales_order_pay_directly_enable", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean closeOrderEnable(String tenantId) {
        String configValue = getConfigValue(tenantId, ConfigType.ORDER_CLOSE.getKey(), User.SUPPER_ADMIN_USER_ID);
        if (StringUtils.isBlank(configValue)) {
            return false;
        }
        Map<String, String> valueMap = (Map<String, String>) JSON.parseObject(configValue, Map.class);
        return Objects.equals(valueMap.getOrDefault("status","0"), "1");
    }

    public static boolean periodicProductEnable(String tenantId) {
        String configValue = getConfigValue(tenantId, "periodic_product", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean bomCoreCheckEnable(String tenantId) {
        String configValue = getConfigValue(tenantId, ConfigType.BOM_CORE_ID_CHECK.getKey(), User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

}
