package com.facishare.crm.sfa.holder;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/10
 */
public class DataHolder {

    private static final ThreadLocal<List<String>> contextHolder = new ThreadLocal<>();

    public static void setContext(List<String> context) {
        contextHolder.set(context);
    }

    public static List<String> getContext() {
        return contextHolder.get();
    }

    public static void clearContext() {
        contextHolder.remove();
    }
}
