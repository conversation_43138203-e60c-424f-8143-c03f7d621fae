package com.facishare.crm.sfa.utilities.util;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.CommonConstants;
import com.facishare.crm.constants.SaleContractConstants;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.rest.MultiShoppingMallProxy;
import com.facishare.crm.rest.dto.MultiShoppingMallModel;
import com.facishare.crm.sfa.holder.DataHolder;
import com.facishare.crm.sfa.model.ProductRangeCheckModel;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.PriceBookCommonService;
import com.facishare.crm.sfa.predefine.service.UnitCoreService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.GetAvailableRangeByPartnerModel;
import com.facishare.crm.sfa.predefine.service.model.GetPriceBookListByProductIdsModel;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ExceptionUtils;
import com.facishare.crm.sfa.utilities.common.UseRangeFieldDataRender;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.*;
import com.facishare.crm.sfa.utilities.constant.dmConstants.MultiUnitConstants;
import com.facishare.crm.util.DoubleParamFunction;
import com.facishare.crm.util.MtCurrentUtil;
import com.facishare.crm.util.ProxyUtils;
import com.facishare.crm.util.ThreeParamFunction;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.QueryAllSuperDeptsByDeptIds;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISpecifiedTableParameter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.SpecifiedTableParameter;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.common.SqlEscaper;
import com.fxiaoke.helper.CollectionHelper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AvailableRangeUtils {
    protected static final List<String> RELATED_FIELD_API_NAME =
            Lists.newArrayList("salesorderproduct_product_list"
                    , "pricebookproduct_salesorderproduct_list"
                    , "price_book_product_quote_lines_list"
                    , "price_book_sales_order_list"
                    , "price_book_quote_list"
                    , "product_quote_lines_list"
                    , "price_book_product_new_opportunity_lines_list"
                    , "product_new_opportunity_lines_list"
                    , "price_book_new_opportunity_list"
                    , "price_book_sale_contract_list"
                    , "product_sale_contract_line_list"
                    , "spu_sku_list"
                    , "SPUObj_salesorderproduct_list");
    private static final List<String> MERCHANT_RANGE_FILTER_API_NAME = Lists.newArrayList(SFAPreDefine.AvailableRange.getApiName(), SFAPreDefine.PriceBook.getApiName(), Utils.GOODS_RECEIVED_NOTE_API_NAME);
    private static final ThreadLocal<List<String>> RANGE_ORG_LIST = new ThreadLocal<>();
    static {
        RequestContextManager.addContextRemoveListener(c -> RANGE_ORG_LIST.remove());
    }

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private PriceBookCommonService priceBookCommonService;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Autowired
    private MultiUnitService multiUnitService;
    @Autowired
    private UnitCoreService unitCoreService;
    @Autowired
    private FunctionUtils functionUtils;
    @Resource
    private MultiShoppingMallProxy multiShoppingMallProxy;
    public static final int PRIORITY_DEFAUTL_VAL = ********;
    private static final String SALE_CONTRACT_PRICEBOOK_LIST = "sale_contract_pricebook_list";
    private static final String SALE_CONTRACT_AVAILABLE_RANGE_LIST = "sale_contract_available_range_list";
    private static final String ACCOUNT_DISPLAY_NAME = "AccountObj.attribute.self.display_name";

    public void buildRangeSearchQueryForProduct(User user, SearchTemplateQuery query, String relatedListName,
                                                String accountId, String partnerId, String priceBookId, String fieldName,
                                                StopWatch stopWatch, IObjectData objectData) {
        buildRangeSearchQueryForProduct(user, query, relatedListName, accountId, partnerId, priceBookId, fieldName,
                stopWatch, objectData, false);
    }

    public void buildRangeSearchQueryForProduct(User user, SearchTemplateQuery query, String relatedListName,
                                                String accountId, String partnerId, String priceBookId, String fieldName,
                                                StopWatch stopWatch, IObjectData objectData, boolean domainQueryAvailableRangeEnable) {
        log.debug("PPPTV-AvailableRangeUtils.buildRangeSearchQueryForProduct start");

        //增加可售范围的校验
        if (!RELATED_FIELD_API_NAME.contains(relatedListName) && !domainQueryAvailableRangeEnable) {
            return;
        }
        if (StringUtils.isEmpty(accountId)) {
            return;
        }
        //支持场景筛选
        //如果开启可售范围,从[从产品添加]入口进入后不走常规的数据权限,走可售范围的配置
        if (!GrayUtil.supportScene4RelatedList(user.getTenantId()) && bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId())) {
            query.setDataRightsParameter(null);
            query.setPermissionType(0);
        }
        stopWatch.lap("ARU.bRSQueryForProduct start");
        List<String> availableProducts = getAvailableProductListForRelatedList(user, accountId, partnerId, objectData);
        log.debug("PPPTV-AvailableRangeUtils.buildRangeSearchQueryForProduct 2");
        stopWatch.lap("ARU.bRSQueryForProduct end");
        stopWatch.logSlow(1000);
        if (CollectionUtils.empty(availableProducts)) {
            //给一个不存在的值,保证查不出任何数据
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, fieldName, "-99");
            return;
        }
        if (availableProducts.contains(AvailableConstants.PublicConstants.RANGE_ALL)) {
            return;
        }
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, fieldName, availableProducts, 10);
    }

    /**
     * 拼装符合该产品id的产品/价目表产品信息
     *
     * @return
     */
    public List<IObjectData> getEffectivePriceBookProductList(boolean availableRangeFlag, boolean priceBookFlag, String productId
            , List<IObjectData> baseProductList, List<IObjectData> availableProductList, List<IObjectData> standardPriceBookProductList
            , boolean needUseStandardPriceBook, User user, BigDecimal amount) {
        boolean isStratifiedOrTieredPrice = bizConfigThreadLocalCacheService.isOpenStratifiedOrTieredPrice(user.getTenantId());
        if (availableRangeFlag && priceBookFlag) {
            List<IObjectData> tmpPriceBookProductList = baseProductList.stream()
                    .filter(x -> x.get(PriceBookConstants.ProductField.PRODUCTID.getApiName()).toString().equals(productId)).collect(Collectors.toList());
            List<IObjectData> rst = Lists.newArrayList();
            //和可售范围产品拍平表交叉验证
            for (IObjectData data : tmpPriceBookProductList) {
                if (isStratifiedOrTieredPrice) {
                    boolean validateTieredPrice = priceBookProductUseful(data, amount);
                    if (!validateTieredPrice) {
                        continue;
                    }
                }
                List<String> rangIdList = castList(data.get("available_range_id"), String.class);
                if (CollectionUtils.notEmpty(rangIdList)) {
                    List<String> prdResultList = availableProductList.stream().filter(x -> rangIdList.contains(x.get(AvailableConstants.ProductResultField.AVAILABLE_RANGE_ID, String.class)))
                            .map(x -> x.get(AvailableConstants.ProductResultField.PRODUCT_ID, String.class)).collect(Collectors.toList());
                    if (prdResultList.contains(AvailableConstants.PublicConstants.RANGE_ALL) || prdResultList.contains(data.get(PriceBookConstants.ProductField.PRODUCTID.getApiName(), String.class))) {
                        rst.add(data);
                    }
                }
            }
            if (CollectionUtils.empty(rst)) {
                return standardPriceBookEnsure(user, needUseStandardPriceBook, productId, standardPriceBookProductList);
            }
            return rst;
        }
        List<IObjectData> rst = Lists.newArrayList();
        List<IObjectData> tmpPriceBookProductList = baseProductList.stream()
                .filter(x -> x.get(PriceBookConstants.ProductField.PRODUCTID.getApiName()).toString().equals(productId))
                .collect(Collectors.toList());
        for (IObjectData data : tmpPriceBookProductList) {
            if (isStratifiedOrTieredPrice) {
                boolean validateTieredPrice = priceBookProductUseful(data, amount);
                if (!validateTieredPrice) {
                    continue;
                }
            }
            rst.add(data);
        }
        if (CollectionUtils.empty(rst)) {
            return standardPriceBookEnsure(user, needUseStandardPriceBook, productId, standardPriceBookProductList);
        }
        return rst;
    }

    private List<IObjectData> standardPriceBookEnsure(User user, boolean needUseStandardPriceBook, String productId, List<IObjectData> standardPriceBookProductList) {
        if (!needUseStandardPriceBook && CollectionUtils.notEmpty(standardPriceBookProductList)) {
            IObjectData data = standardPriceBookProductList.get(0);
            if (null != data && !Strings.isNullOrEmpty(user.getTenantId())
                    && Boolean.TRUE.equals(GrayUtil.needRemoveProductNotInAvailableRange(user.getTenantId()))) {
                return Lists.newArrayList();
            }
        }
        //标准价目表里的数据为兜底数据
        Optional<IObjectData> standardProduct = standardPriceBookProductList.stream()
                .filter(x -> x.get(PriceBookConstants.ProductField.PRODUCTID.getApiName(), String.class).equals(productId))
                .findFirst();
        return standardProduct.map(Lists::newArrayList).orElseGet(Lists::newArrayList);
    }

    public boolean priceBookProductUseful(IObjectData data, BigDecimal amount) {
        if (amount == null) {
            return true;
        }
        BigDecimal startCount = data.get(PriceBookConstants.ProductField.START_COUNT.getApiName(), BigDecimal.class);
        BigDecimal endCount = data.get(PriceBookConstants.ProductField.END_COUNT.getApiName(), BigDecimal.class);
        if (startCount != null && startCount.compareTo(amount) >= 0) {
            return false;
        }
        return endCount == null || endCount.compareTo(amount) >= 0;
    }

    public boolean priceBookProductDateUseful(IObjectData data, Long nowDateTime) {
        if (nowDateTime == null) {
            nowDateTime = System.currentTimeMillis();
        }
        Long startDate = data.get(PriceBookConstants.ProductField.START_DATE.getApiName(), Long.class);
        Long endDate = data.get(PriceBookConstants.ProductField.END_DATE.getApiName(), Long.class);
        if (startDate != null && startDate > nowDateTime) {
            return false;
        }
        return endDate == null || endDate >= nowDateTime;
    }

    public void buildRangeSearchQueryForPriceBook(User user, SearchTemplateQuery query, String relatedListName, String accountId, String partnerId, Long businessDate, boolean domainEnable, IObjectData objectData) {
        //增加可售范围的校验
        if (!RELATED_FIELD_API_NAME.contains(relatedListName) && !domainEnable) {
            return;
        }
        if (SALE_CONTRACT_PRICEBOOK_LIST.equals(relatedListName)) {
            return;
        }
        if (StringUtils.isEmpty(accountId)) {
            return;
        }
        StopWatch stopWatch = StopWatch.create("AvailableRangeUtils-buildRangeSearchQueryForPriceBook");
        stopWatch.lap("ARU.bRSQueryForPriceBook start");
        List<IObjectData> priceBookList = getAvailablePriceBookList(user, accountId, partnerId, businessDate, objectData);
        stopWatch.lap("ARU.getAPriceBookList end");
        stopWatch.logSlow(1000);
        if (CollectionUtils.empty(priceBookList)) {
            //给一个不存在的值,保证查不出任何数据
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, DBRecord.ID, "-99");
            return;
        }
        List<String> priceBookIdList = priceBookList.stream().map(x -> x.get(PriceBookConstants.Field.PRICE_BOOK_ID.getApiName(), String.class)).collect(Collectors.toList());
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, DBRecord.ID, Lists.newArrayList(priceBookIdList));
    }

    public List<IObjectData> getProductByPriceBookList(User user, List<String> productIdList, List<IObjectData> priceBooks, List<String> actualUnitIdList) {
        return getProductByPriceBookList(user, productIdList, priceBooks, actualUnitIdList, null);
    }

    public List<IObjectData> getProductByPriceBookList(User user, List<String> productIdList, List<IObjectData> priceBooks, List<String> actualUnitIdList, Long businessDate) {
        return getProductByPriceBookList(user, productIdList, priceBooks, actualUnitIdList, businessDate, Boolean.FALSE, Boolean.TRUE, Boolean.FALSE);
    }

    public List<IObjectData> getProductByPriceBookList(User user, List<String> productIdList, List<IObjectData> priceBooks,
                                                       List<String> actualUnitIdList, Long businessDate, Boolean onlySimpleSearch,
                                                       Boolean fillRefObj, Boolean fromSpuList) {
        if (CollectionUtils.empty(priceBooks) || CollectionUtils.empty(productIdList)) {
            return Lists.newArrayList();
        }
        Set<String> priceBookIds = priceBooks.stream().map(DBRecord::getId).collect(Collectors.toSet());
        Map<String, IObjectData> priceBookMap = priceBooks.stream().collect(Collectors.toMap(DBRecord::getId, x -> x));

        List<IObjectData> rst = GetProductByPriceBookIdList(user, productIdList, Lists.newArrayList(priceBookIds), actualUnitIdList, businessDate, onlySimpleSearch, fillRefObj, fromSpuList);
        if (CollectionUtils.empty(rst)) {
            return Lists.newArrayList();
        }
        for (IObjectData data : rst) {
            //将优先级id加入价目表产品数据中
            String priceBookId = data.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName(), String.class);
            if (priceBookMap.containsKey(priceBookId)) {
                IObjectData priceBookData = priceBookMap.get(priceBookId);
                Integer priceBookPriority = priceBookData.get(PriceBookConstants.Field.PRIORITY.getApiName()) != null ?
                        priceBookData.get(PriceBookConstants.Field.PRIORITY.getApiName(), Integer.class) : PRIORITY_DEFAUTL_VAL;
                long lastModifiedTime = Optional.ofNullable(priceBookData.getLastModifiedTime()).orElse(0L);
                data.set("price_book_priority", priceBookPriority);
                data.set("price_book_start_date", priceBookData.get(PriceBookConstants.Field.STARTDATE.getApiName()));
                data.set("price_book_end_date", priceBookData.get(PriceBookConstants.Field.ENDDATE.getApiName()));
                data.set("price_book_last_mod_time", lastModifiedTime);
                data.setLastModifiedTime(priceBookData.getLastModifiedTime());
                //如果有可售范围,则加入产品对象中
                List<String> rangeIdObj = castList(priceBookData.get("available_range_id"), String.class);
                if (CollectionUtils.notEmpty(rangeIdObj)) {
                    data.set("available_range_id", rangeIdObj);
                }
            }
        }
        return rst;
    }

    private List<IObjectData> GetProductByPriceBookIdList(User user, List<String> productIdList, List<String> priceBookIdList,
                                                          List<String> actualUnitIds, Long businessDate, Boolean onlySimpleSearch,
                                                          Boolean fillRefObj, Boolean fromSpuList) {
        if (CollectionUtils.empty(productIdList) || CollectionUtils.empty(priceBookIdList)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(0);
        searchQuery.setNeedReturnQuote(true);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);
        searchQuery.setDataRightsParameter(null);
        if (priceBookIdList.size() > 1) {
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, PriceBookConstants.ProductField.PRICEBOOKID.getApiName(), priceBookIdList);
        } else {
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, PriceBookConstants.ProductField.PRICEBOOKID.getApiName(), priceBookIdList.get(0));
        }
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, "life_status", "normal");
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, "is_deleted", "0");
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, PriceBookConstants.ProductField.PRODUCTID.getApiName(), productIdList);
        boolean ignoreAll = Boolean.TRUE.equals(onlySimpleSearch) || GrayUtil.skipEditFunction(user.getTenantId());
        QueryResult<IObjectData> queryResult = ignoreAll
                ? metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, SFAPreDefine.PriceBookProduct.getApiName(), searchQuery)
                : metaDataFindServiceExt.findBySearchQuery(user, SFAPreDefine.PriceBookProduct.getApiName(), searchQuery);

        if (CollectionUtils.empty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        List<IObjectData> dataList;
        if (bizConfigThreadLocalCacheService.isOpenPriceBookProductValidPeriod(user.getTenantId())) {
            Long nowDateTime = null == businessDate ? Long.valueOf(System.currentTimeMillis()) : businessDate;
            //过滤有效期内的价目表产品
            dataList = queryResult.getData().stream()
                    .filter(priceBookProduct -> {
                        Long startDate = priceBookProduct.get(PriceBookConstants.ProductField.START_DATE.getApiName(), Long.class);
                        Long endDate = priceBookProduct.get(PriceBookConstants.ProductField.END_DATE.getApiName(), Long.class);
                        return (startDate == null || startDate <= nowDateTime) && (endDate == null || endDate >= nowDateTime);
                    }).collect(Collectors.toList());
            if (CollectionUtils.empty(dataList)) {
                return Lists.newArrayList();
            }
        } else {
            dataList = queryResult.getData();
        }
        if (!Boolean.FALSE.equals(fillRefObj)) {
            if (!Boolean.TRUE.equals(fromSpuList) || !GrayUtil.isSpuRelatedListOptimize(user.getTenantId())) {
                fillObjectDataWithRefObject(user, dataList);
            }
        }
        return dataList;
    }

    private void fillObjectDataWithRefObject(User user, List<IObjectData> dataList) {
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), Utils.PRICE_BOOK_PRODUCT_API_NAME);
        serviceFacade.fillObjectDataWithRefObject(describe, dataList, user);
    }

    /**
     * 是否为可售价目表
     *
     * @param user
     * @param availableRangeIds
     * @param priceBookId
     * @return
     */
    public boolean isAvailablePriceBook(User user, String accountId, String partnerId, List<String> availableRangeIds, String priceBookId, IObjectData masterData, Map<String, List<IObjectData>> detailDataList) {
        List<IObjectData> availablePriceBookList = getAvailableRangePriceBookList(user, availableRangeIds);
        if (!GrayUtil.isPriceBookReform(user.getTenantId()) && CollectionUtils.empty(availablePriceBookList)) {
            return false;
        }
        Map<String, List<ObjectDataDocument>> details = convertIObjectDataToDocument(detailDataList);
        SearchTemplateQuery priceBookRangeQuery = buildPriceBookRangeSearchQuery(user, masterData, details, PriceBookConstants.PriceBookRangeType.DETAIL.getRangeType());
        if (GrayUtil.isPriceBookReform(user.getTenantId())) {
            boolean isAllPriceBook = isAllPriceBook(user, availableRangeIds, availablePriceBookList, Lists.newArrayList(), null);
            Map<String, List<IObjectData>> availablePriceBookListMap = availablePriceBookList.stream().collect(Collectors.groupingBy(o -> o.get(AvailableConstants.PriceBookField.PRICE_BOOK_ID).toString(), Collectors.toList()));
            List<String> priceBookIds = isAllPriceBook ? Lists.newArrayList() : Lists.newArrayList(availablePriceBookListMap.keySet());
            availablePriceBookList = priceBookCommonService.getPriceBookList(user, accountId, partnerId, priceBookIds, priceBookRangeQuery, false, masterData);
        } else if (priceBookRangeQuery != null) {
            List<String> priceBookIds = availablePriceBookList.stream().map(o -> o.get(AvailableConstants.PriceBookField.PRICE_BOOK_ID, String.class)).collect(Collectors.toList());
            SearchTemplateQueryExt.of(priceBookRangeQuery).addFilter(Operator.IN, DBRecord.ID, priceBookIds);
            if (bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(user.getTenantId())) {
                String recordType = masterData == null ? null : masterData.getRecordType();
                if (MultiLevelOrderConstants.RECORD_TYPE.equals(recordType)) {
                    SearchTemplateQueryExt.of(priceBookRangeQuery).addFilter(Operator.EQ, MultiRecordType.RECORD_TYPE, Lists.newArrayList(recordType));
                } else {
                    SearchTemplateQueryExt.of(priceBookRangeQuery).addFilter(Operator.NEQ, MultiRecordType.RECORD_TYPE, Lists.newArrayList(MultiLevelOrderConstants.RECORD_TYPE));
                }
            }
            availablePriceBookList = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, Utils.PRICE_BOOK_API_NAME, priceBookRangeQuery).getData();
            if (CollectionUtils.notEmpty(availablePriceBookList)) {
                availablePriceBookList.forEach(priceBook -> priceBook.set(AvailableConstants.PriceBookField.PRICE_BOOK_ID, priceBook.getId()));
            }
        }
        return availablePriceBookList.stream()
                .anyMatch(x -> priceBookId.equals(x.get(AvailableConstants.PriceBookField.PRICE_BOOK_ID, String.class)));
    }

    /**
     * 获取可售价目表
     */
    public List<IObjectData> getAvailablePriceBookList(User user, String accountId, String partnerId, IObjectData objectData, IObjectData saleContractObjectData) {
        return getAvailablePriceBookList(user, accountId, partnerId, null, objectData, null, null, false, saleContractObjectData);
    }

    public List<IObjectData> getAvailablePriceBookList(User user, String accountId, String partnerId, IObjectData objectData,
                                                       Map<String, List<ObjectDataDocument>> details, String rangeType) {
        return getAvailablePriceBookList(user, accountId, partnerId, null, objectData, details, rangeType);
    }

    public List<IObjectData> getAvailablePriceBookList(User user, String accountId, String partnerId, Long businessDate, IObjectData objectData) {
        return getAvailablePriceBookList(user, accountId, partnerId, businessDate, objectData, null, null);
    }

    public List<IObjectData> getAvailablePriceBookList(User user, String accountId, String partnerId, Long businessDate, IObjectData objectData,
                                                       Map<String, List<ObjectDataDocument>> details, String rangeType) {
        return getAvailablePriceBookList(user, accountId, partnerId, null, null, businessDate, Lists.newArrayList(), objectData,
                details, rangeType, false);
    }

    public List<IObjectData> getAvailablePriceBookList(User user, String accountId, String partnerId, Long businessDate, IObjectData objectData,
                                                       Map<String, List<ObjectDataDocument>> details, String rangeType, boolean isActCurrentDetailRow,
                                                       IObjectData saleContractObjectData) {
        return getAvailablePriceBookList(user, accountId, partnerId, null, null, businessDate, Lists.newArrayList(), objectData,
                details, rangeType, isActCurrentDetailRow, saleContractObjectData);
    }

    public List<IObjectData> getAvailablePriceBookList(User user, String accountId, String partnerId, SearchTemplateQuery availablePriceBookQuery, SearchTemplateQuery availableRangeQuery,
                                                       Long businessDate, List<String> backFillRangeFieldApiName, IObjectData masterData, Map<String, List<ObjectDataDocument>> details,
                                                       String rangeType, boolean isActCurrentDetailRow) {
        IObjectData saleContractObjectData = findSaleContractInfo(user, masterData);
        return getAvailablePriceBookList(user, accountId, partnerId, availablePriceBookQuery, availableRangeQuery, businessDate, backFillRangeFieldApiName, masterData,
                details, rangeType, isActCurrentDetailRow, saleContractObjectData);
    }

    /**
     * availablePriceBookQuery参数是可售价目表的过滤条件，科顺客开及订货通使用
     * availableRangeQuery参数是可售范围的过滤条件，科顺客开及订货通使用
     */
    public List<IObjectData> getAvailablePriceBookList(User user, String accountId, String partnerId,
                                                       SearchTemplateQuery availablePriceBookQuery,
                                                       SearchTemplateQuery availableRangeQuery,
                                                       Long businessDate,
                                                       List<String> backFillRangeFieldApiName,
                                                       IObjectData masterData,
                                                       Map<String, List<ObjectDataDocument>> details,
                                                       String rangeType,
                                                       boolean isActCurrentDetailRow,
                                                       IObjectData saleContractObjectData) {
        if (!bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId())) {
            SearchTemplateQuery priceBookRangeQuery = buildPriceBookRangeSearchQuery(user, masterData, details, rangeType, isActCurrentDetailRow);
            if (GrayUtil.isPriceBookReform(user.getTenantId())) {
                return priceBookCommonService.getPriceBookList(user, accountId, partnerId, businessDate, priceBookRangeQuery, saleContractObjectData);
            }
            return getAllPriceBookList(user, businessDate, priceBookRangeQuery, saleContractObjectData);
        }
        if (Strings.isNullOrEmpty(accountId) || ("--".equals(accountId) && !bizConfigThreadLocalCacheService.isLeadsTransferUseAllPriceBook(user.getTenantId()))) {
            List<IObjectData> resultList = Lists.newArrayList();
            resultList.add(priceBookCommonService.getStandardPriceBook(user));
            return resultList;
        }
        log.debug("AvailableRangeUtils.getAvailablePriceBookList accountId:{},partnerId:{}", accountId, partnerId);
        List<String> rangeIdList = getAvailableRangeIdList(user, accountId, partnerId, "", "", availableRangeQuery, masterData, Maps.newHashMap(), saleContractObjectData);
        List<IObjectData> priceBookList = getPriceBookListByRangeIds(user, accountId, partnerId, rangeIdList, availablePriceBookQuery, false, businessDate, masterData, details, rangeType, isActCurrentDetailRow, saleContractObjectData);
        if (CollectionUtils.notEmpty(backFillRangeFieldApiName)) {
            //填充 需要的回填字段
            backFillRangeFieldApiName.add(DBRecord.ID);
            backFillRangeFieldApiName.add("extend_obj_data_id");
            SearchTemplateQuery rangeSearchTmpl = SoCommonUtils.buildSearchTemplateQuery(1000);
            SearchTemplateQueryExt.of(rangeSearchTmpl).addFilter(Operator.IN, DBRecord.ID, rangeIdList);
            QueryResult<IObjectData> rangeInfoQueryResult = metaDataFindServiceExt.findBySearchQueryWithFields(user,
                    SFAPreDefine.AvailableRange.getApiName(), rangeSearchTmpl,
                    backFillRangeFieldApiName, true);
            if (null != rangeInfoQueryResult && CollectionUtils.notEmpty(rangeInfoQueryResult.getData())) {
                priceBookList.forEach(x -> {
                    List<String> idList = castList(x.get("available_range_id"), String.class);
                    List<IObjectData> rangInfoList = rangeInfoQueryResult.getData().stream().
                            filter(y -> idList.contains(y.getId())).collect(Collectors.toList());
                    x.set("range_info", ObjectDataDocument.ofList(rangInfoList));
                });
            }
        }
        return priceBookList;
    }

    public List<IObjectData> getPriceBookListByProductIds(User user, List<String> productIdList, List<GetPriceBookListByProductIdsModel.ProductInfo> productInfos,
                                                          String accountId, String partnerId, Long businessDate, IObjectData masterData,
                                                          Map<String, List<ObjectDataDocument>> details, String rangeType) {
        if (GrayUtil.isPriceBookReform(user.getTenantId()) && CollectionUtils.notEmpty(productInfos)) {
            productIdList = productInfos.stream().map(GetPriceBookListByProductIdsModel.ProductInfo::getProductId).collect(Collectors.toList());
        }
        IObjectData saleContractObjectData = findSaleContractInfo(user, masterData);
        List<IObjectData> priceBookList = getAvailablePriceBookList(user, accountId, partnerId, businessDate, masterData, details, rangeType, true, saleContractObjectData);
        boolean realPriceConstraintMode = isRealPriceConstraintMode(user, saleContractObjectData);
        if (CollectionUtils.empty(priceBookList) && !realPriceConstraintMode) {
            return findActiveStandardPriceBookProductList(user, productIdList);
        }
        List<IObjectData> rst = getProductByPriceBookList(user, productIdList, priceBookList, new ArrayList<>());
        if (bizConfigThreadLocalCacheService.isOpenStratifiedOrTieredPrice(user.getTenantId())) {
            rst.removeIf(objectData -> {
                if (CollectionUtils.notEmpty(productInfos)) {
                    Optional<GetPriceBookListByProductIdsModel.ProductInfo> productInfo = productInfos.stream()
                            .filter(o -> Objects.equals(o.getProductId(), objectData.get("product_id", String.class)))
                            .findFirst();
                    return productInfo.isPresent() && !priceBookProductUseful(objectData, productInfo.get().getAmount());
                }
                return false;
            });
        }
        if (CollectionUtils.empty(rst) && !realPriceConstraintMode) {
            return findActiveStandardPriceBookProductList(user, productIdList);
        }
        return rst;
    }

    public List<IObjectData> findActiveStandardPriceBookProductList(User user, List<String> productIdList) {
        IObjectData standardPriceBook = priceBookCommonService.getStandardPriceBook(user);
        if (null == standardPriceBook || PriceBookConstants.ActiveStatus.OFF.getStatus().equals(standardPriceBook.get(PriceBookConstants.Field.ACTIVESTATUS.getApiName()))) {
            //没有标准价目表或者标准价目表禁用
            log.warn("No standard priceBook or standard priceBook no active ,tenantId:{},userId:{},productIdList:{}",
                    user.getTenantId(),
                    user.getUpstreamOwnerIdOrUserId(),
                    StringUtils.join(productIdList, ","));
            return Lists.newArrayList();
        }
        return getProductByPriceBookList(user, productIdList, Lists.newArrayList(standardPriceBook), new ArrayList<>());
    }

    public List<IObjectData> findStandardPriceBookProductList(User user, List<String> productIdList) {
        IObjectData standardPriceBook = priceBookCommonService.getStandardPriceBook(user);
        if (null == standardPriceBook) {
            //没有标准价目表
            log.warn("No standard priceBook,tenantId:{},userId:{},productIdList:{}",
                    user.getTenantId(),
                    user.getUpstreamOwnerIdOrUserId(),
                    StringUtils.join(productIdList, ","));
            return Lists.newArrayList();
        }
        return getProductByPriceBookList(user, productIdList, Lists.newArrayList(standardPriceBook), new ArrayList<>());
    }

    private List<IObjectData> getAvailableRangePriceBookList(User user, List<String> availableRangeIdList) {
        return getAvailableRangePriceBookList(user, availableRangeIdList, null);
    }

    public List<IObjectData> getAvailableRangePriceBookList(User user, List<String> availableRangeIdList, SearchTemplateQuery searchTemplateQuery) {
        if (CollectionUtils.empty(availableRangeIdList)) {
            return Lists.newArrayList();
        }
        if (GrayUtil.isPriceBookReform(user.getTenantId()) && !bizConfigThreadLocalCacheService.isOpenAvailablePriceBook(user.getTenantId())) {
            return Lists.newArrayList();
        }
        //价目表的有效期等过滤已通过拍平表过滤掉了
        SearchTemplateQuery searchQuery = null == searchTemplateQuery ? SoCommonUtils.buildSearchTemplateQuery(0) : searchTemplateQuery;
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, "is_deleted", "0");
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, AvailableConstants.PriceBookField.AVAILABLE_RANGE_ID, availableRangeIdList);
        QueryResult<IObjectData> queryResult = metaDataFindServiceExt.findBySearchQueryWithFields(user, SFAPreDefine.AvailablePriceBook.getApiName(), searchQuery,
                Lists.newArrayList(AvailableConstants.PriceBookField.PRICE_BOOK_ID, AvailableConstants.PriceBookField.AVAILABLE_RANGE_ID), true);

        if (CollectionUtils.empty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }

    public List<IObjectData> getPriceBookListByRangeIds(User user, String accountId, String partnerId, List<String> availableRangeIdList, Long businessDate) {
        return getPriceBookListByRangeIds(user, accountId, partnerId, availableRangeIdList, null, false, businessDate, null, null, null);
    }

    public List<IObjectData> getPriceBookListByRangeIds(User user, String accountId, String partnerId, List<String> availableRangeIdList, Long businessDate, IObjectData saleContractObjectData, IObjectData masterData) {
        return getPriceBookListByRangeIds(user, accountId, partnerId, availableRangeIdList, null, false, businessDate, masterData, null, null, false, saleContractObjectData);
    }

    public List<IObjectData> getPriceBookListByRangeIds(User user, String accountId, String partnerId, List<String> availableRangeIdList, Long businessDate,
                                                        IObjectData objectData, Map<String, List<ObjectDataDocument>> details, String rangeType, boolean isActCurrentDetailRow, IObjectData saleContractObjectData) {
        return getPriceBookListByRangeIds(user, accountId, partnerId, availableRangeIdList, null, false, businessDate, objectData, details, rangeType, isActCurrentDetailRow, saleContractObjectData);
    }

    public List<IObjectData> getPriceBookListByRangeIds(User user, String accountId, List<String> availableRangeIdList,
                                                        IObjectData objectData, Map<String, List<ObjectDataDocument>> details, String rangeType) {
        return getPriceBookListByRangeIds(user, accountId, null, availableRangeIdList, null, false, null, objectData, details, rangeType);
    }

    public List<IObjectData> getPriceBookListByRangeIdsWithStaleData(User user, List<String> availableRangeIdList) {
        return getPriceBookListByRangeIds(user, null, null, availableRangeIdList, null, true, null, null, null, null);
    }

    public List<IObjectData> getPriceBookListByRangeIds(User user, String accountId, String partnerId, List<String> availableRangeIdList, SearchTemplateQuery searchTemplateQuery
            , boolean includeStaleData, Long businessDate, IObjectData objectData, Map<String, List<ObjectDataDocument>> details, String rangeType) {
        return getPriceBookListByRangeIds(user, accountId, partnerId, availableRangeIdList, searchTemplateQuery, includeStaleData, businessDate, objectData, details, rangeType, false, null);
    }

    public List<IObjectData> getPriceBookListByRangeIds(User user, String accountId, String partnerId, List<String> availableRangeIdList, SearchTemplateQuery searchTemplateQuery
            , boolean includeStaleData, Long businessDate, IObjectData objectData, Map<String, List<ObjectDataDocument>> details, String rangeType, boolean isActCurrentDetailRow
            , IObjectData saleContractObjectData) {
        StopWatch stopWatch = StopWatch.create("AvailableRangeUtils-getPriceBookListByRangeIds");
        return getPriceBookListByRangeIds(user, accountId, partnerId, availableRangeIdList, searchTemplateQuery, includeStaleData, businessDate, objectData, details, rangeType, isActCurrentDetailRow, saleContractObjectData, stopWatch);
    }

    public List<IObjectData> getPriceBookListByRangeIds(User user, String accountId, String partnerId, List<String> availableRangeIdList, SearchTemplateQuery searchTemplateQuery
            , boolean includeStaleData, Long businessDate, IObjectData objectData, Map<String, List<ObjectDataDocument>> details, String rangeType, boolean isActCurrentDetailRow, IObjectData saleContractObjectData, StopWatch stopWatch) {
        if (CollectionUtils.empty(availableRangeIdList)) {
            if (!GrayUtil.isPriceBookReform(user.getTenantId())
                    || (GrayUtil.isPriceBookReform(user.getTenantId()) && bizConfigThreadLocalCacheService.isOpenAvailablePriceBook(user.getTenantId()))
                    || !isRealPriceConstraintMode(user, saleContractObjectData)) {
                return Lists.newArrayList();
            }
        }
        stopWatch.lap("ARU.getPriceBookListByRangeIds 1");
        log.debug("ARU.getPriceBookListByRangeIds 1 availableRangeIdList:{}", String.join(",", availableRangeIdList));
        List<IObjectData> availablePriceBookList = getAvailableRangePriceBookList(user, availableRangeIdList, searchTemplateQuery);
        stopWatch.lap("ARU.getPriceBookListByRangeIds 2");
        if (!GrayUtil.isPriceBookReform(user.getTenantId()) && CollectionUtils.empty(availablePriceBookList)) {
            return Lists.newArrayList();
        }
        boolean isAllPriceBook = false;
        List<String> noAvailableRangeList = Lists.newArrayList();
        if (GrayUtil.isPriceBookReform(user.getTenantId())) {
            isAllPriceBook = isAllPriceBook(user, availableRangeIdList, availablePriceBookList, noAvailableRangeList, searchTemplateQuery);
        }
        Map<String, List<IObjectData>> availablePriceBookListMap = availablePriceBookList.stream().parallel().collect(Collectors.groupingBy(o -> o.get(AvailableConstants.PriceBookField.PRICE_BOOK_ID).toString(), Collectors.toList()));
        if (log.isDebugEnabled()) {
            log.debug("ARU.getPriceBookListByRangeIds 2 availablePriceBookList:{}", String.join(",", Lists.newArrayList(availablePriceBookListMap.keySet())));
        }
        List<IObjectData> tmpPriceBookDataList;
        SearchTemplateQuery priceBookRangeQuery = buildPriceBookRangeSearchQuery(user, objectData, details, rangeType, isActCurrentDetailRow);
        if (GrayUtil.isPriceBookReform(user.getTenantId())) {
            List<String> priceBookIds = isAllPriceBook ? Lists.newArrayList() : Lists.newArrayList(availablePriceBookListMap.keySet());
            tmpPriceBookDataList = priceBookCommonService.getPriceBookList(user, accountId, partnerId, priceBookIds, businessDate, priceBookRangeQuery, includeStaleData, saleContractObjectData, objectData);
        } else {
            tmpPriceBookDataList = findPriceBook(user, objectData, saleContractObjectData, availablePriceBookListMap, priceBookRangeQuery);
        }
        stopWatch.lap("ARU.getPriceBookListByRangeIds 3");
        if (CollectionUtils.empty(tmpPriceBookDataList)) {
            return Lists.newArrayList();
        }
        List<IObjectData> rst = Lists.newArrayList();
        for (IObjectData data : tmpPriceBookDataList) {
            if (Boolean.TRUE.equals(priceBookCommonService.isPriceBookUseful(data, businessDate))) {
                data.set("price_book_expired", false);
                fillPriceBookData(user, data, availablePriceBookListMap, noAvailableRangeList);
                rst.add(data);
            } else {
                data.set("price_book_expired", true);
                if (includeStaleData) {
                    fillPriceBookData(user, data, availablePriceBookListMap, noAvailableRangeList);
                    rst.add(data);
                }
            }
        }
        stopWatch.lap("ARU.getPriceBookListByRangeIds 4");
        stopWatch.logSlow(500);
        return rst;
    }

    private List<IObjectData> findPriceBook(User user, IObjectData objectData, IObjectData saleContractObjectData, Map<String, List<IObjectData>> availablePriceBookListMap, SearchTemplateQuery priceBookRangeQuery) {
        if (priceBookRangeQuery == null) {
            priceBookRangeQuery = SoCommonUtils.buildSearchTemplateQuery(0);
        }
        SearchTemplateQueryExt.of(priceBookRangeQuery).addFilter(Operator.IN, DBRecord.ID, Lists.newArrayList(availablePriceBookListMap.keySet()));
        if (bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(user.getTenantId())) {
            String recordType = objectData == null ? null : objectData.getRecordType();
            if (MultiLevelOrderConstants.RECORD_TYPE.equals(recordType)) {
                SearchTemplateQueryExt.of(priceBookRangeQuery).addFilter(Operator.EQ, MultiRecordType.RECORD_TYPE, Lists.newArrayList(recordType));
            } else {
                SearchTemplateQueryExt.of(priceBookRangeQuery).addFilter(Operator.NEQ, MultiRecordType.RECORD_TYPE, Lists.newArrayList(MultiLevelOrderConstants.RECORD_TYPE));
            }
        }
        List<String> priceBookConstraintIds = handleRealPriceConstraintQuery(user, saleContractObjectData, SFAPreDefine.SaleContractPriceBookRelation.getApiName(),
                SaleContractPriceBookConstants.Field.PRICEBOOK_ID.getApiName());
        if (CollectionUtils.notEmpty(priceBookConstraintIds)) {
            SearchTemplateQueryExt.of(priceBookRangeQuery).addFilter(Operator.IN, DBRecord.ID, priceBookConstraintIds);
        }
        priceBookRangeQuery.setOrders(Lists.newArrayList(SearchUtil.orderByLastModifiedTime()));
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, Utils.PRICE_BOOK_API_NAME, priceBookRangeQuery).getData();
    }

    private void fillPriceBookData(User user, IObjectData data, Map<String, List<IObjectData>> availablePriceBookListMap, List<String> noAvailableRangeList) {
        data.set("price_book_start_date", data.get("start_date"));
        data.set("price_book_end_date", data.get("end_date"));

        if (!GrayUtil.isPriceBookReform(user.getTenantId())) {
            if (availablePriceBookListMap.containsKey(data.getId())) {
                data.set(AvailableConstants.PriceBookField.PRICE_BOOK_ID, data.getId());
                data.set("available_range_id", availablePriceBookListMap.get(data.getId()).stream().map(x -> x.get(AvailableConstants.PriceBookField.AVAILABLE_RANGE_ID, String.class)).collect(Collectors.toList()));
            }
        } else {
            data.set(AvailableConstants.PriceBookField.PRICE_BOOK_ID, data.getId());
            List<String> priceBookAvailableRangeIds = Lists.newArrayList();
            if (availablePriceBookListMap.containsKey(data.getId())) {
                priceBookAvailableRangeIds.addAll(availablePriceBookListMap.get(data.getId()).stream().map(x -> x.get(AvailableConstants.PriceBookField.AVAILABLE_RANGE_ID, String.class)).collect(Collectors.toList()));
            }
            if (CollectionUtils.notEmpty(noAvailableRangeList)) {
                priceBookAvailableRangeIds.addAll(noAvailableRangeList);
            }
            data.set("available_range_id", priceBookAvailableRangeIds);
        }
    }

    public IObjectData getHighestPriorityPriceBook(List<IObjectData> priceBookList) {
        if (CollectionUtils.notEmpty(priceBookList)) {
            //优先以priority正序，同priority以last_modified_time倒序
            Optional<IObjectData> objectDataOptional = priceBookList.stream().min((x, y) -> {
                Integer firstPriority = x.get(PriceBookConstants.Field.PRIORITY.getApiName(), Integer.class);
                Integer secondPriority = y.get(PriceBookConstants.Field.PRIORITY.getApiName(), Integer.class);
                if (null == firstPriority) {
                    firstPriority = PRIORITY_DEFAUTL_VAL;
                }
                if (null == secondPriority) {
                    secondPriority = PRIORITY_DEFAUTL_VAL;
                }
                if (Objects.equals(firstPriority, secondPriority)) {
                    return y.getLastModifiedTime().compareTo(x.getLastModifiedTime());
                } else {
                    return firstPriority.compareTo(secondPriority);
                }
            });
            if (objectDataOptional.isPresent()) {
                return objectDataOptional.get();
            }
        }
        return null;
    }

    public List<IObjectData> getAllPriceBookList(User user, Long businessDate, SearchTemplateQuery priceBookRangeQuery) {
        return getAllPriceBookList(user, businessDate, priceBookRangeQuery, null);
    }

    public List<IObjectData> getAllPriceBookList(User user, Long businessDate, SearchTemplateQuery priceBookRangeQuery, IObjectData saleContractObjectData) {
        SearchTemplateQuery searchQuery;
        if (priceBookRangeQuery == null) {
            searchQuery = SoCommonUtils.buildSearchTemplateQuery(0);
        } else {
            searchQuery = priceBookRangeQuery;
        }
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, "life_status", "normal");
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, "is_deleted", "0");
        List<String> priceBookIds = handleRealPriceConstraintQuery(user, saleContractObjectData, SFAPreDefine.SaleContractPriceBookRelation.getApiName(),
                SaleContractPriceBookConstants.Field.PRICEBOOK_ID.getApiName());
        if (CollectionUtils.notEmpty(priceBookIds)) {
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, DBRecord.ID, priceBookIds);
        }
        searchQuery.setOrders(Lists.newArrayList(SearchUtil.orderByLastModifiedTime()));
        QueryResult<IObjectData> queryResult = metaDataFindServiceExt.findBySearchQuery(user, SFAPreDefine.PriceBook.getApiName(), searchQuery);
        if (CollectionUtils.empty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        List<IObjectData> rst = Lists.newArrayList();
        for (IObjectData data : queryResult.getData()) {
            if (Boolean.TRUE.equals(priceBookCommonService.isPriceBookUseful(data, businessDate))) {
                data.set(PriceBookConstants.Field.PRICE_BOOK_ID.getApiName(), data.getId());
                rst.add(data);
            }
        }
        return rst;
    }

    public List<String> getPriceBookProductListByMap(User user, String accountId, String partnerId,
                                                     Map<String, List<String>> priceBookId2productIds,
                                                     List<IObjectData> availablePriceBookDataList,
                                                     Long businessDate, IObjectData masterData) {
        ProductRangeCheckModel productRangeCheckModel = getPriceBookProductListByMap(user, accountId, partnerId, priceBookId2productIds, availablePriceBookDataList, businessDate, masterData, null);
        return productRangeCheckModel.getAvailableProductList();
    }

    public ProductRangeCheckModel getPriceBookProductListByMap(User user, String accountId, String partnerId,
                                                               Map<String, List<String>> priceBookId2productIds,
                                                               List<IObjectData> availablePriceBookDataList,
                                                               Long businessDate, IObjectData masterData, IObjectData saleContractObjectData) {
        StopWatch stopWatch = StopWatch.create("AvailableRangeUtils-getPriceBookProductListByMap");
        return getPriceBookProductListByMap(user, accountId, partnerId, priceBookId2productIds, availablePriceBookDataList, businessDate, masterData, saleContractObjectData, stopWatch);
    }

    /**
     * availablePriceBookDataList用于校验所选价目表是否在可售范围
     *
     * @param user
     * @param accountId
     * @param partnerId
     * @param priceBookId2productIds
     * @param availablePriceBookDataList
     * @return
     */
    public ProductRangeCheckModel getPriceBookProductListByMap(User user, String accountId, String partnerId,
                                                               Map<String, List<String>> priceBookId2productIds,
                                                               List<IObjectData> availablePriceBookDataList,
                                                               Long businessDate, IObjectData masterData,
                                                               IObjectData saleContractObjectData, StopWatch stopWatch) {
        if (CollectionUtils.empty(priceBookId2productIds)) {
            return ProductRangeCheckModel.builder().build();
        }
        Set<String> productIdSet = Sets.newHashSet();
        boolean isOpenMultiPriceBook = bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(user.getTenantId());
        boolean isOpenStratifiedOrTieredPrice = bizConfigThreadLocalCacheService.isOpenStratifiedOrTieredPrice(user.getTenantId());
        for (List<String> pIds : priceBookId2productIds.values()) {
            if (isOpenMultiPriceBook || isOpenStratifiedOrTieredPrice) {
                for (String p : pIds) {
                    String[] p_a = p.split("_");
                    productIdSet.add(p_a[0]);
                }
            } else {
                productIdSet.addAll(pIds);
            }
        }
        List<String> productIdList = Lists.newArrayList(productIdSet);
        if (!bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId())) {
            if (GrayUtil.isPriceBookReform(user.getTenantId())) {
                List<IObjectData> priceBookList = priceBookCommonService.getPriceBookList(user, accountId, partnerId, businessDate, null, saleContractObjectData);
                if (CollectionUtils.notEmpty(priceBookList)) {
                    availablePriceBookDataList.addAll(priceBookList);
                }
            }
            List<String> saleContractProductIds = Lists.newArrayList();
            boolean detailConstraintMode = getSaleContractLineIdList(user, masterData, productIdList, saleContractObjectData, saleContractProductIds);
            return ProductRangeCheckModel.builder().availableProductList(Lists.newArrayList(AvailableConstants.PublicConstants.RANGE_ALL))
                    .saleContractProductList(saleContractProductIds).detailConstraintMode(detailConstraintMode).build();
        }
        List<String> availableRangeIdList = getAvailableRangeIdList(user, accountId, partnerId, "", masterData, Maps.newHashMap(), saleContractObjectData);
        stopWatch.lap("getAvailableRange");
        List<IObjectData> availableProductList = getAvailableProductDataList(user, availableRangeIdList, productIdList);
        stopWatch.lap("getAvailableProductDataList");
        List<IObjectData> availablePriceBookList = getPriceBookListByRangeIds(user, accountId, partnerId, availableRangeIdList, businessDate, saleContractObjectData, masterData);
        stopWatch.lap("getPriceBookListByRangeIds");
        if (CollectionUtils.notEmpty(availablePriceBookList)) {
            availablePriceBookDataList.addAll(availablePriceBookList);
        }
        List<String> saleContractProductIds = Lists.newArrayList();
        boolean detailConstraintMode = getSaleContractLineIdList(user, masterData, productIdList, saleContractObjectData, saleContractProductIds);
        stopWatch.lap("getSaleContractLineDataList");
        List<String> availableProductIds = availableProductList.stream().map(r -> r.get(AvailableConstants.ProductResultField.PRODUCT_ID, String.class)).collect(Collectors.toList());
        stopWatch.logSlow(1000);
        return ProductRangeCheckModel.builder().availableProductList(availableProductIds).saleContractProductList(saleContractProductIds).detailConstraintMode(detailConstraintMode).build();
    }

    /**
     * 获取不在可售的产品,通常用于校验
     */
    public List<IObjectData> getProductDataListOutOfScope(User user, String accountId, String
            partnerId, String priceBookId, List<String> productIdList) {
        List<String> idList = getProductIdListOutOfScope(user, accountId, partnerId, priceBookId, productIdList);
        if (CollectionUtils.empty(idList)) {
            return Lists.newArrayList();
        }
        return serviceFacade.findObjectDataByIdsIgnoreFormula(user.getTenantId(), idList, SFAPreDefine.Product.getApiName());
    }

    /**
     * 获取不在可售的产品,通常用于校验
     */
    public List<String> getProductIdListOutOfScope(User user, String accountId, String
            partnerId, String priceBookId, List<String> productIdList) {
        if (CollectionUtils.empty(productIdList)) {
            return Lists.newArrayList();
        }
        List<String> outOfScopeProductIdList;
        if (bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId())) {
            outOfScopeProductIdList = getProductIdListOutOfScopeOpenRange(user, accountId, partnerId, priceBookId, productIdList);
        } else {
            outOfScopeProductIdList = getProductIdListOutOfScopeNotOpenRange(user, accountId, partnerId, priceBookId, productIdList);
        }
        return outOfScopeProductIdList;
    }

    /**
     * 获取不在可售的产品,通常用于校验
     */
    private List<String> getProductIdListOutOfScopeOpenRange(User user, String accountId, String
            partnerId, String priceBookId, List<String> productIdList) {
        if (StringUtils.isBlank(accountId)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        if (CollectionUtils.empty(productIdList)) {
            return Lists.newArrayList();
        }
        Set<String> productRangeList = getAvailableProductList(user, accountId, partnerId, priceBookId, productIdList, null);
        if (productRangeList.contains(AvailableConstants.PublicConstants.RANGE_ALL)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.empty(productRangeList)) {
            return productIdList;
        }
        List<String> rst = Lists.newArrayList();
        for (String s : productIdList) {
            if (!productRangeList.contains(s)) {
                rst.add(s);
            }
        }
        return rst;
    }

    /**
     * 获取不在可售的产品,通常用于校验
     */
    private List<String> getProductIdListOutOfScopeNotOpenRange(User user, String accountId, String partnerId, String
            priceBookId, List<String> productIdList) {
        if (StringUtils.isBlank(priceBookId) || !bizConfigThreadLocalCacheService.isPriceBookEnabled(user.getTenantId())) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.empty(productIdList)) {
            return Lists.newArrayList();
        }
        IObjectData priceBook = null;
        if (GrayUtil.isPriceBookReform(user.getTenantId())) {
            List<IObjectData> objectDataList = priceBookCommonService.getPriceBookList(user, accountId, partnerId, Lists.newArrayList(priceBookId), null, false);
            if (CollectionUtils.notEmpty(objectDataList)) {
                priceBook = objectDataList.get(0);
            }
        } else {
            priceBook = serviceFacade.findObjectDataIgnoreAll(user, priceBookId, SFAPreDefine.PriceBook.getApiName());
        }
        if (priceBook == null || !priceBookCommonService.isPriceBookUseful(priceBook)) {
            return productIdList;
        }
        SearchTemplateQuery searchQuery = SoCommonUtils.buildSearchTemplateQuery(0);
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, PriceBookConstants.ProductField.PRICEBOOKID.getApiName(), priceBookId);
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, PriceBookConstants.ProductField.PRODUCTID.getApiName(), productIdList);
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, "life_status", "normal");
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, "is_deleted", "0");
        QueryResult<IObjectData> queryResult = metaDataFindServiceExt.findBySearchQuery(user, SFAPreDefine.PriceBookProduct.getApiName(), searchQuery);
        if (CollectionUtils.empty(queryResult.getData())) {
            return productIdList;
        }
        if (bizConfigThreadLocalCacheService.isOpenPriceBookProductValidPeriod(user.getTenantId())) {
            Iterator<IObjectData> iterator = queryResult.getData().iterator();
            while (iterator.hasNext()) {
                IObjectData objectData = iterator.next();
                if (!priceBookProductDateUseful(objectData, null)) {
                    iterator.remove();
                }
            }
            if (CollectionUtils.empty(queryResult.getData())) {
                return productIdList;
            }
        }
        Map<String, List<IObjectData>> priceBookProductMap = queryResult.getData().stream().collect(Collectors.groupingBy(x -> x.get(PriceBookConstants.ProductField.PRODUCTID.getApiName(), String.class)));
        List<String> rst = Lists.newArrayList();
        for (String s : productIdList) {
            if (!priceBookProductMap.containsKey(s)) {
                rst.add(s);
            }
        }
        return rst;
    }

    public List<String> getAvailableProductList4Spu(User user, String accountId, String partnerId, IObjectData objectData) {
        if (!bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId())) {
            return Lists.newArrayList(AvailableConstants.PublicConstants.RANGE_ALL);
        }
        IObjectData saleContractObjectData = findSaleContractInfo(user, objectData);
        List<String> availableRangeIdList = getAvailableRangeIdList(user, accountId, partnerId, "", objectData, Maps.newHashMap(), saleContractObjectData);
        if (GrayUtil.isSpuRelatedListOptimize(user.getTenantId())) {
            DataHolder.setContext(availableRangeIdList);
        }
        if (CollectionUtils.empty(availableRangeIdList)) {
            return Lists.newArrayList();
        }
        if (isAllProductAvailable(user.getTenantId(), availableRangeIdList)) {
            return Lists.newArrayList(AvailableConstants.PublicConstants.RANGE_ALL);
        }
        return availableRangeIdList;
    }

    public List<String> getAvailableProductListForSubQuery(User user, String accountId, List<String> availableRangeIds) {
        if (!bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId())) {
            return Lists.newArrayList(AvailableConstants.PublicConstants.RANGE_ALL);
        }
        if (isAllProductAvailable(user.getTenantId(), availableRangeIds)) {
            return Lists.newArrayList(AvailableConstants.PublicConstants.RANGE_ALL);
        }
        return buildSubQuery(availableRangeIds);
    }

    /**
     * 选价目表明细、产品过滤可售产品，不包含全部时使用子查询过滤
     *
     * @param user
     * @param accountId
     * @param partnerId
     * @return
     */
    public List<String> getAvailableProductListForRelatedList(User user, String accountId, String partnerId, IObjectData objectData) {
        if (!bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId())) {
            return Lists.newArrayList(AvailableConstants.PublicConstants.RANGE_ALL);
        }
        IObjectData saleContractObjectData = findSaleContractInfo(user, objectData);
        List<String> availableRangeIdList = getAvailableRangeIdList(user, accountId, partnerId, "", objectData, Maps.newHashMap(), saleContractObjectData);
        if (GrayUtil.isSpuRelatedListOptimize(user.getTenantId())) {
            DataHolder.setContext(availableRangeIdList);
        }
        if (CollectionUtils.empty(availableRangeIdList)) {
            return Lists.newArrayList();
        }
        if (isAllProductAvailable(user.getTenantId(), availableRangeIdList)) {
            return Lists.newArrayList(AvailableConstants.PublicConstants.RANGE_ALL);
        }
        return buildSubQuery(availableRangeIdList);
    }

    public Set<String> getAvailableProductList(User user, String accountId, String partnerId,
                                               List<String> availableRangeIds, IObjectData objectData) {
        return getAvailableProductList(user, accountId, partnerId, "", Lists.newArrayList(), availableRangeIds, objectData);
    }

    public Set<String> getAvailableProductList(User user, String accountId, String partnerId, String priceBookId,
                                               List<String> productIdList, IObjectData objectData) {
        return getAvailableProductList(user, accountId, partnerId, priceBookId, productIdList, null, objectData);
    }

    /**
     * 获取可售产品范围
     */
    public Set<String> getAvailableProductList(User user, String accountId, String partnerId, String priceBookId,
                                               List<String> productIdList, List<String> availableRangeIds, IObjectData objectData) {
        StopWatch stopWatch = StopWatch.create("AvailableRangeUtils-getAvailableProductList");
        if (!bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId())) {
            Set<String> rst = new HashSet<>(productIdList);
            rst.add(AvailableConstants.PublicConstants.RANGE_ALL);
            return rst;
        }
        log.debug("PPPTV-AvailableRangeUtils.getAvailableProductList start");
        stopWatch.lap("ARU.getAvailableProductList start");
        IObjectData saleContractObjectData = findSaleContractInfo(user, objectData);
        List<String> availableRangeIdList = CollectionUtils.empty(availableRangeIds) ?
                getAvailableRangeIdList(user, accountId, partnerId, priceBookId, objectData, Maps.newHashMap(), saleContractObjectData) : availableRangeIds;
        log.debug("PPPTV-AvailableRangeUtils.getAvailableProductList 1");
        stopWatch.lap("ARU.getAvailableProductList 1");
        List<IObjectData> prodList = getAvailableProductDataList(user, availableRangeIdList, productIdList);
        log.debug("PPPTV-AvailableRangeUtils.getAvailableProductList 2");
        stopWatch.lap("ARU.getAvailableProductList 2");
        if (CollectionUtils.empty(prodList)) {
            return Sets.newHashSet();
        }
        Set<String> availableProductIdList = prodList.stream().map(x -> x.get(AvailableConstants.ProductResultField.PRODUCT_ID, String.class)).collect(Collectors.toSet());
        if (availableProductIdList.contains(AvailableConstants.PublicConstants.RANGE_ALL)) {
            Set<String> rst = new HashSet<>(productIdList);
            rst.add(AvailableConstants.PublicConstants.RANGE_ALL);
            return rst;
        }

        if (StringUtils.isNotBlank(priceBookId)) {
            List<IObjectData> priceBookProductList = priceBookCommonService.getPriceBookProducts(user, priceBookId, Lists.newArrayList(availableProductIdList));
            stopWatch.lap("ARU.getAvailableProductList 3");
            stopWatch.logSlow(1000);
            log.debug("PPPTV-AvailableRangeUtils.getAvailableProductList 3");
            if (CollectionUtils.empty(priceBookProductList)) {
                return Sets.newHashSet();
            }
            return priceBookProductList.stream().parallel()
                    .map(x -> x.get(PriceBookConstants.ProductField.PRODUCTID.getApiName(), String.class)).collect(Collectors.toSet());
        }
        log.debug("PPPTV-AvailableRangeUtils.getAvailableProductList end");
        return availableProductIdList;
    }

    @NotNull
    public List<String> getRangeOrgList(User user, String userId) {
        List<String> rangeOrgList = RANGE_ORG_LIST.get();
        if (CollectionUtils.empty(rangeOrgList)) {
            List<String> orgList = Lists.newArrayList();
            orgList.add("e." + userId);
            List<String> deptList = getSuperDeptIdsByUserId(user, userId);
            orgList.addAll(deptList.stream().map(x -> String.format("d.%s", x)).collect(Collectors.toList()));
            //把all加进去
            orgList.add(AvailableConstants.PublicConstants.RANGE_ALL);
            RANGE_ORG_LIST.set(orgList);
        }
        return RANGE_ORG_LIST.get();
    }

    public List<String> getAvailableRangeIdList(User user, String accountId
            , IObjectData masterData
            , Map<String, List<IObjectData>> details) {
        return getAvailableRangeIdList(user, accountId, "", "", "", null
                , masterData, details, null);
    }

    public List<String> getAvailableRangeIdList(User user, String accountId, String partnerId, IObjectData masterData,
                                                Map<String, List<IObjectData>> details) {
        return getAvailableRangeIdList(user, accountId, partnerId, "", "", null
                , masterData, details, null);
    }

    public List<String> getAvailableRangeIdList(User user, String accountId, SearchTemplateQuery searchTemplateQuery
            , IObjectData masterData
            , Map<String, List<IObjectData>> details) {
        return getAvailableRangeIdList(user, accountId, "", "", ""
                , searchTemplateQuery, masterData, details, null);
    }

    public List<String> getAvailableRangeIdList(User user, String accountId, String partnerId, String priceBookID
            , IObjectData masterData
            , Map<String, List<IObjectData>> details
            , IObjectData saleContractObjectData) {
        return getAvailableRangeIdList(user, accountId, partnerId, priceBookID, "", null
                , masterData, details, saleContractObjectData);
    }

    public List<String> getAvailableRangeIdList(User user, String accountId, String partnerId, String priceBookID,
                                                String availableRangeId, SearchTemplateQuery searchTemplateQuery
            , IObjectData masterData
            , Map<String, List<IObjectData>> details
            , IObjectData saleContractObjectData) {
        return getAvailableRangeList(user, accountId, partnerId, priceBookID, availableRangeId, searchTemplateQuery
                , masterData, details, saleContractObjectData)
                .stream().map(x -> (String) x.get(AvailableConstants.AccountResultField.AVAILABLE_RANGE_ID))
                .collect(Collectors.toList());
    }

    public List<IObjectData> getAvailableRangeList(User user
            , String accountId
            , String partnerId
            , String priceBookID
            , String availableRangeId
            , SearchTemplateQuery searchTemplateQuery
            , IObjectData masterData
            , Map<String, List<IObjectData>> details
            , IObjectData saleContractObjectData
    ) {
        StopWatch stopWatch = StopWatch.create("AvailableRangeUtils-getAvailableRangeList");
        return getAvailableRangeList(user, accountId, partnerId, priceBookID, availableRangeId, searchTemplateQuery, masterData, details, saleContractObjectData, stopWatch);
    }

    /**
     * 获取可售范围
     *
     * @param user
     * @param partnerId
     * @param accountId
     * @param priceBookID
     */
    public List<IObjectData> getAvailableRangeList(User user
            , String accountId
            , String partnerId
            , String priceBookID
            , String availableRangeId
            , SearchTemplateQuery searchTemplateQuery
            , IObjectData masterData
            , Map<String, List<IObjectData>> details
            , IObjectData saleContractObjectData
            , StopWatch stopWatch
    ) {
        stopWatch.lap("ARU.getAvailableRange start");
        log.debug("PPPTV-AvailableRangeUtils.getAvailableRange start");
        if (StringUtils.isBlank(accountId)) {
            throw new ValidateException(String.format(I18N.text(SFAI18NKeyUtil.SFA_OBJECT_DISPLAY_NAME_NOTNULL), I18N.text(ACCOUNT_DISPLAY_NAME)));
        }
        if (!bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId())) {
            return Lists.newArrayList();
        }

        String userId = user.getUpstreamOwnerIdOrUserId();
        List<String> accountList = Lists.newArrayList(accountId, AvailableConstants.PublicConstants.RANGE_ALL);
        //如果是外部用户（PRM下游客户）,则不验证适用部门
        List<String> orgList = user.isOutUser() ? Lists.newArrayList() : getRangeOrgList(user, userId);
        stopWatch.lap("ARU.getAvailableRange getRangeOrgList");
        List<String> partnerIdList = Lists.newArrayList();
        if (bizConfigThreadLocalCacheService.isPartnerEnabled(user.getTenantId())) {
            log.debug("PPPTV-AvailableRangeUtils.getAvailableRange 11 partenrId:{}", partnerId);
            if (StringUtils.isNotBlank(partnerId)) {
                partnerIdList.add(AvailableConstants.PublicConstants.RANGE_ALL);
                partnerIdList.add(partnerId);
            } else {
                partnerIdList.add(AvailableConstants.PublicConstants.RANGE_NONE);
                partnerIdList.add(AvailableConstants.PublicConstants.RANGE_ALL);
            }
        }
        //销售合同约束处理
        List<String> availableRangeConstraintIds = handleRealPriceConstraintQuery(user, saleContractObjectData, SFAPreDefine.SaleContractAvailableRangeRelation.getApiName(),
                SaleContractAvailableRangeConstants.Field.AVAILABLE_RANGE_ID.getApiName());
        List<Map> queryResult = findAvailableRange(user.getTenantId(), accountList, orgList, partnerIdList, availableRangeId, availableRangeConstraintIds, masterData, partnerId);
        stopWatch.lap("ARU.getAvailableRange 2");
        log.debug("PPPTV-AvailableRangeUtils.getAvailableRange 2");
        if (CollectionUtils.empty(queryResult)) {
            return Lists.newArrayList();
        }
        List<IObjectData> queryList;
        searchTemplateQuery = functionUtils.handleFiltersByFunction(user, "AvailableRangeObj", searchTemplateQuery, masterData, details);
        stopWatch.lap("ARU.getAvailableRange function");
        if (null == searchTemplateQuery) {
            queryList = queryResult.stream().map(ObjectData::new).collect(Collectors.toList());
        } else {
            queryList = filterAvailableRange(user, queryResult, searchTemplateQuery);
            stopWatch.lap("ARU.getAvailableRange functionFilter");
        }
        //配件商城过滤
        filterAvailableRangeMultiShoppingIds(user, queryList);
        stopWatch.lap("ARU.getAvailableRange filterAvailableRangeMultiShoppingIds");
        List<IObjectData> rst = handleAvailableRangePriority(user.getTenantId(), queryList);
        //根据价目表过滤
        if (StringUtils.isBlank(priceBookID)) {
            stopWatch.logSlow(500);
            return rst;
        }
        List<String> rangeIdList = rst.stream().map(x -> (String) x.get(AvailableConstants.AccountResultField.AVAILABLE_RANGE_ID))
                .collect(Collectors.toList());
        if (log.isDebugEnabled()) {
            log.debug("PPPTV-AvailableRangeUtils.getAvailableRange 12 rst:{}",
                    String.join(",", rangeIdList));
        }
        List<IObjectData> priceBookListByRangeIds = getAvailableRangePriceBookList(user, Lists.newArrayList(rangeIdList));
        stopWatch.lap("ARU.getAvailableRange 3");
        log.debug("PPPTV-AvailableRangeUtils.getAvailableRange 3");

        List<String> tmpList;
        if (!GrayUtil.isPriceBookReform(user.getTenantId())) {
            if (CollectionUtils.empty(priceBookListByRangeIds)) {
                return Lists.newArrayList();
            }
            tmpList = priceBookListByRangeIds.stream().filter(x -> priceBookID.equals(x.get(AvailableConstants.PriceBookField.PRICE_BOOK_ID, String.class)))
                    .map(x -> x.get(AvailableConstants.PriceBookField.AVAILABLE_RANGE_ID, String.class)).collect(Collectors.toList());
        } else {
            tmpList = priceBookListByRangeIds.stream().filter(x -> priceBookID.equals(x.get(AvailableConstants.PriceBookField.PRICE_BOOK_ID, String.class)))
                    .map(x -> x.get(AvailableConstants.PriceBookField.AVAILABLE_RANGE_ID, String.class)).collect(Collectors.toList());
            List<String> noAvailableRangeList = Lists.newArrayList();
            isAllPriceBook(user, rangeIdList, priceBookListByRangeIds, noAvailableRangeList, null);
            if (CollectionUtils.notEmpty(noAvailableRangeList)) {
                IObjectData priceBookData = serviceFacade.findObjectDataIgnoreAll(user, priceBookID, SFAPreDefine.PriceBook.getApiName());
                if (priceBookData != null) {
                    tmpList.addAll(noAvailableRangeList);
                }
            }
        }
        stopWatch.logSlow(500);
        return rst.stream().filter(x -> tmpList.contains(x.get(AvailableConstants.AccountResultField.AVAILABLE_RANGE_ID, String.class))).collect(Collectors.toList());
    }

    private List<IObjectData> handleAvailableRangePriority(String tenantId, List<IObjectData> dataList) {
        List<IObjectData> rst = Lists.newArrayList();
        if (bizConfigThreadLocalCacheService.isOpenAvailableRangePriority(tenantId)) {
            //取最小优先级的数据
            int minPriority = PRIORITY_DEFAUTL_VAL;
            for (IObjectData data : dataList) {
                Integer priority = data.get(AvailableConstants.AvailableRangeField.PRIORITY, Integer.class, 0);
                if (priority == minPriority) {
                    rst.add(data);
                } else if (minPriority > priority) {
                    rst.clear();
                    minPriority = priority;
                    rst.add(data);
                }
            }
        } else {
            rst.addAll(dataList);
        }
        return rst;
    }

    public List<String> getAvailableRangeIdList(User user) {
        if (!bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId())) {
            return Lists.newArrayList();
        }
        String userId = user.getUpstreamOwnerIdOrUserId();
        List<String> orgList = Lists.newArrayList();
        //如果是外部用户（PRM下游客户）,则不验证适用部门
        if (!user.isOutUser()) {
            orgList = getRangeOrgList(user, userId);
        }
        List<Map> queryResult = findAvailableRangeWithoutAccount(user.getTenantId(), orgList);
        if (CollectionUtils.empty(queryResult)) {
            return Lists.newArrayList();
        }
        List<IObjectData> queryList = queryResult.stream().map(ObjectData::new).collect(Collectors.toList());
        List<IObjectData> rst = handleAvailableRangePriority(user.getTenantId(), queryList);
        return rst.stream().map(r -> r.getId()).collect(Collectors.toList());
    }

    public List<IObjectData> getAvailableRangeByIds(User user, List<String> rangeIdList, String partnerId) {
        if (CollectionUtils.empty(rangeIdList)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(0);
        searchQuery.setNeedReturnQuote(false);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);
        searchQuery.setDataRightsParameter(null);
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, "life_status", Lists.newArrayList("normal", "in_change"));
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, "is_deleted", "0");
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, "status", "1");
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, "_id", rangeIdList);//修改前是id 改为_id
        if (!user.isOutUser()) {
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.HASANYOF, "org_ids", getRangeOrgList(user, user.getUpstreamOwnerIdOrUserId()));
        }
        List<String> partnerIdList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(partnerId)) {
            partnerIdList.add(AvailableConstants.PublicConstants.RANGE_ALL);
            partnerIdList.add(partnerId);
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.HASANYOF, "partner_ids", partnerIdList);
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.N, "apply_to_partner", Lists.newArrayList("false"));
        }
        if (bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(user.getTenantId())) {
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.NEQ, MultiRecordType.RECORD_TYPE, Lists.newArrayList(MultiLevelOrderConstants.RECORD_TYPE));
        }
        QueryResult<IObjectData> queryResult = metaDataFindServiceExt.findBySearchQuery(user, SFAPreDefine.AvailableRange.getApiName(), searchQuery);
        if (CollectionUtils.empty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }

    public List<IObjectData> getPriceBookByIds(User user, List<String> priceBookIds, String partnerId) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(0);
        searchQuery.setNeedReturnQuote(false);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);
        searchQuery.setDataRightsParameter(null);
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, "life_status", Lists.newArrayList("normal", "in_change"));
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, "is_deleted", "0");
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, "active_status", "1");
        if (CollectionUtils.notEmpty(priceBookIds)) {
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, "_id", priceBookIds);
        }
        if (!user.isOutUser()) {
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.HASANYOF, "org_ids", getRangeOrgList(user, user.getUpstreamOwnerIdOrUserId()));
        }
        List<String> partnerIdList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(partnerId)) {
            partnerIdList.add(AvailableConstants.PublicConstants.RANGE_ALL);
            partnerIdList.add(partnerId);
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.HASANYOF, "partner_ids", partnerIdList);
        }
        if (bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(user.getTenantId())) {
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.NEQ, MultiRecordType.RECORD_TYPE, Lists.newArrayList(MultiLevelOrderConstants.RECORD_TYPE));
        }
        QueryResult<IObjectData> queryResult = metaDataFindServiceExt.findBySearchQuery(user, SFAPreDefine.PriceBook.getApiName(), searchQuery);
        if (CollectionUtils.empty(queryResult.getData())) {
            return Lists.newArrayList();
        }

        List<IObjectData> rst = Lists.newArrayList();
        for (IObjectData data : queryResult.getData()) {
            data.set(AvailableConstants.PriceBookField.PRICE_BOOK_ID, data.getId());
            if (priceBookCommonService.isPriceBookUseful(data, null)) {
                rst.add(data);
            }
        }
        return rst;
    }

    public List<IObjectData> getAvailableRangeListByAccountIds(User user, List<String> accountIds, String partnerId) {
        StopWatch stopWatch = StopWatch.create("AvailableRangeUtils-getAvailableRangeListByAccountIds");
        return getAvailableRangeListByAccountIds(user, accountIds, partnerId, stopWatch);
    }

    public Set<String> getAvailableRangeIdsByPartnerId(User user, @NotNull String partnerId) {
        SearchTemplateQuery templateQuery = new SearchTemplateQuery();
        // 可售范围启用状态
        SearchUtil.fillFilterEq(templateQuery.getFilters(), "status", "1");
        List<String> partnerIdList = Lists.newArrayList(partnerId);
        partnerIdList.add(AvailableConstants.PublicConstants.RANGE_ALL);
        partnerIdList.add(partnerId);
        SearchUtil.fillFilterHasAnyOf(templateQuery.getFilters(), "partner_ids", partnerIdList);
        templateQuery.setLimit(0);
        return metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, "AvailableRangeObj",
                        templateQuery,
                        Lists.newArrayList("_id"))
                .stream()
                .map(DBRecord::getId)
                .collect(Collectors.toSet());
    }

    public Set<String> getAvailableProductIdsByAvailableIds(User user, Set<String> availableRangeIds) {
        SearchTemplateQuery templateQuery = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(templateQuery.getFilters(), "available_range_id", availableRangeIds);
        templateQuery.setLimit(0);
        return metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(user,
                        "AvailableProductObj", templateQuery, Lists.newArrayList("product_id"))
                .stream()
                .filter(x -> x.get("product_id") != null)
                .map(x -> x.get("product_id").toString())
                .collect(Collectors.toSet());
    }


    public List<IObjectData> getAvailableRangeListByAccountIds(User user, List<String> accountIds, String partnerId, StopWatch stopWatch) {
        stopWatch.lap("ARU.getAvailableRangeListByAccountIds start");
        log.debug("PPPTV-AvailableRangeUtils.getAvailableRangeListByAccountIds start");
        if (CollectionUtils.empty(accountIds)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        if (!bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId())) {
            return Lists.newArrayList();
        }

        String userId = user.getUpstreamOwnerIdOrUserId();
        List<String> accountList = Lists.newArrayList(AvailableConstants.PublicConstants.RANGE_ALL);
        accountList.addAll(accountIds);
        List<String> orgList = Lists.newArrayList();
        if (!user.isOutUser()) {
            orgList = getRangeOrgList(user, userId);
        }
        List<String> partnerIdList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(partnerId)) {
            partnerIdList.add(AvailableConstants.PublicConstants.RANGE_ALL);
            partnerIdList.add(partnerId);
        }
        List<Map> queryResult = findAvailableRange(user.getTenantId(), accountList, orgList,
                partnerIdList, "", null, null, partnerId);
        stopWatch.lap("ARU.getAvailableRangeListByAccountIds 2");
        log.debug("PPPTV-AvailableRangeUtils.getAvailableRangeListByAccountIds 2");
        if (CollectionUtils.empty(queryResult)) {
            return Lists.newArrayList();
        }
        List<IObjectData> queryList = queryResult.stream().map(ObjectData::new).collect(Collectors.toList());
        stopWatch.logSlow(1000);
        if (bizConfigThreadLocalCacheService.isOpenAvailableRangePriority(user.getTenantId())) {
            Map<String, List<IObjectData>> accountId2RangeData = queryList.stream().collect(Collectors.groupingBy(o ->
                    o.get(AvailableConstants.AccountResultField.ACCOUNT_ID, String.class)));
            List<IObjectData> rst = Lists.newArrayList();
            //取最小优先级的数据
            int priority = ********9;
            for (Map.Entry<String, List<IObjectData>> entry : accountId2RangeData.entrySet()) {
                for (IObjectData data : entry.getValue()) {
                    Object objDataPriority = data.get(AvailableConstants.AvailableRangeField.PRIORITY);
                    int dataPriority = objDataPriority == null ? 0 : (int) objDataPriority;
                    if (dataPriority == priority) {
                        rst.add(data);
                    } else if (priority > dataPriority) {
                        rst.clear();
                        priority = dataPriority;
                        rst.add(data);
                    }
                }
            }
            stopWatch.logSlow(1000);
            return rst;
        }
        return queryList;
    }

    /**
     * 根据员工id获取其主属部门的上级部门
     *
     * @param user
     * @param userId
     * @return
     */
    private List<String> getSuperDeptIdsByUserId(User user, String userId) {
        List<String> deptIds = Lists.newArrayList();
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfo = serviceFacade
                .getMainDeptInfo(user.getTenantId(), user.getUpstreamOwnerIdOrUserId(), Lists.newArrayList(userId));
        if (mainDeptInfo.containsKey(userId)) {
            String mainDeptId = mainDeptInfo.get(userId).getDeptId();
            Map<String, List<String>> allSuperDeptIds = serviceFacade
                    .getAllSuperDeptIdsByDeptIds(user.getTenantId(), user.getUpstreamOwnerIdOrUserId()
                            , Lists.newArrayList(mainDeptId));
            if (allSuperDeptIds.containsKey(mainDeptId)) {
                return allSuperDeptIds.get(mainDeptId);
            }
        }
        return deptIds;
    }

    private List<Map> findAvailableRange(String tenantId, List<String> accountList, List<String> orgList,
                                         List<String> partnerIdList, String availableRangeId, List<String> availableRangeConstraintIds,
                                         IObjectData masterData, String partnerId) {
        boolean isFromDingHuoTong = DhtUtil.isDhtOrAccessoriesMallRequest(RequestContextManager.getContext().getPeerName(), RequestContextManager.getContext().getAppId());
        boolean isFromPrm = AppIdMapping.isPRM(RequestContextManager.getContext().getAppId());
        String recordType = masterData == null ? null : masterData.getRecordType();
        boolean isMerchantRangeControl = isMerchantRangeControl(tenantId, partnerId, recordType);
        String sql = ConcatenateSqlUtils.getAvailableRangeSql(tenantId, accountList, orgList, partnerIdList, availableRangeId, availableRangeConstraintIds, isFromDingHuoTong, isFromPrm, recordType, isMerchantRangeControl);
        try {
            List<Map> rst = objectDataService.findBySql(tenantId, sql);
            log.debug("PPPTV-findAvailableRange sql:{}", sql);
            return rst;
        } catch (MetadataServiceException e) {
            log.error("findAvailableRange metadata findBySql error. sql:{} ", sql, e);
            throw new APPException("system error!");
        }
    }

    public boolean isMerchantRangeControl(String tenantId, String partnerId, String recordType) {
        if (!bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(tenantId)
                || !MultiLevelOrderConstants.RECORD_TYPE.equals(recordType)
                || StringUtils.isEmpty(partnerId)) {
            return false;
        }
        IObjectData partnerData;
        try {
            IActionContext actionContext = ActionContextExt.of(User.systemUser(tenantId)).skipRelevantTeam().getContext();
            partnerData = objectDataService.findById(partnerId, tenantId, actionContext, SFAPreDefine.Partner.getApiName());
        } catch (MetadataServiceException e) {
            log.error("findById error:", e);
            return false;
        }
        if (partnerData != null && Boolean.TRUE.equals(partnerData.get("product_range_control", Boolean.class))) {
            return true;
        } else {
            return false;
        }
    }

    private List<Map> findAvailableRangeWithoutAccount(String tenantId, List<String> orgList) {
        String sql = ConcatenateSqlUtils.getAvailableRangeWithoutAccountSql(tenantId, orgList);
        try {
            List<Map> rst = objectDataService.findBySql(tenantId, sql);
            return rst;
        } catch (MetadataServiceException e) {
            log.error("findAvailableRangeWithoutAccount error. sql:{} ", sql, e);
        }
        return Lists.newArrayList();
    }

    public List<IObjectData> getAvailableProductDataList(User user, List<String> availableRangeIdList, List<String> productIdList) {
        if (CollectionUtils.empty(availableRangeIdList)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchQuery = SoCommonUtils.buildSearchTemplateQuery(0);
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, AvailableConstants.ProductResultField.AVAILABLE_RANGE_ID, availableRangeIdList);
        if (!CollectionUtils.empty(productIdList)) {
            List<String> tmpProductIdList = Lists.newArrayList(productIdList);
            tmpProductIdList.add(AvailableConstants.PublicConstants.RANGE_ALL);
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, AvailableConstants.ProductResultField.PRODUCT_ID, tmpProductIdList);
        }
        QueryResult<IObjectData> queryResult = metaDataFindServiceExt.findBySearchQueryWithFields(user,
                SFAPreDefine.AvailableProduct.getApiName(), searchQuery,
                Lists.newArrayList(AvailableConstants.ProductResultField.AVAILABLE_RANGE_ID, AvailableConstants.ProductResultField.PRODUCT_ID), true);
        if (CollectionUtils.empty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }

    public List<IObjectData> getAvailableProductDataList(User user, String productId) {
        if (StringUtils.isEmpty(productId)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchQuery = SoCommonUtils.buildSearchTemplateQuery(0);

        List<String> tmpProductIdList = Lists.newArrayList();
        tmpProductIdList.add(productId);
        tmpProductIdList.add(AvailableConstants.PublicConstants.RANGE_ALL);
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, AvailableConstants.ProductResultField.PRODUCT_ID, tmpProductIdList);
        QueryResult<IObjectData> queryResult = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, SFAPreDefine.AvailableProduct.getApiName(), searchQuery);
        if (CollectionUtils.empty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }

    public <T> List<T> castList(Object obj, Class<T> clazz) {
        List<T> result = Lists.newArrayList();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return Lists.newArrayList();
    }

    public void setAvailableRangeDefaultValue(String tenantId, IObjectData objectData, boolean needClearIds) {
        if (needClearIds) {
            objectData.set(AvailableConstants.AvailableRangeField.PARTNER_IDS, null);
            objectData.set(AvailableConstants.AvailableRangeField.ORG_IDS, null);
        }
        objectData.set(AvailableConstants.AvailableRangeField.CALCULATE_STATUS,
                AvailableConstants.CalculateStatus.EFFECTIVE.getStatus());
        String accountRange = objectData.get(AvailableConstants.AvailableRangeField.ACCOUNT_RANGE, String.class);
        if (Strings.isNullOrEmpty(accountRange)) {
            objectData.set(AvailableConstants.AvailableRangeField.ACCOUNT_RANGE, "{\"type\":\"ALL\"}");
            objectData.set(AvailableConstants.AvailableRangeField.PRIORITY,
                    AvailableConstants.AvailableRangePriority.ALL.getPriority());
        } else {
            UseRangeFieldDataRender.UseRangeInfo useRangeInfo = JSON.parseObject(accountRange, UseRangeFieldDataRender.UseRangeInfo.class);
            if (UseRangeFieldDataRender.UseRangeType.FIXED.toString().equals(useRangeInfo.getType())) {
                objectData.set(AvailableConstants.AvailableRangeField.PRIORITY,
                        AvailableConstants.AvailableRangePriority.FIXED.getPriority());
            } else if (UseRangeFieldDataRender.UseRangeType.CONDITION.toString().equals(useRangeInfo.getType())) {
                objectData.set(AvailableConstants.AvailableRangeField.PRIORITY,
                        AvailableConstants.AvailableRangePriority.CONDITION.getPriority());
            } else if (UseRangeFieldDataRender.UseRangeType.ALL.toString().equals(useRangeInfo.getType())) {
                objectData.set(AvailableConstants.AvailableRangeField.PRIORITY,
                        AvailableConstants.AvailableRangePriority.ALL.getPriority());
            } else {
                objectData.set(AvailableConstants.AvailableRangeField.PRIORITY, null);
            }
        }
        String productRange = objectData.get(AvailableConstants.AvailableRangeField.PRODUCT_RANGE, String.class);
        if (Strings.isNullOrEmpty(productRange)) {
            objectData.set(AvailableConstants.AvailableRangeField.PRODUCT_RANGE, "{\"type\":\"ALL\"}");
        }
        if (bizConfigThreadLocalCacheService.isPartnerEnabled(tenantId)) {
            String partnerRange = objectData.get(AvailableConstants.AvailableRangeField.PARTNER_RANGE, String.class);
            if (Strings.isNullOrEmpty(partnerRange)) {
                if (bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(tenantId)) {
                    objectData.set(AvailableConstants.AvailableRangeField.PARTNER_RANGE, "{\"type\":\"NONE\"}");
                } else {
                    objectData.set(AvailableConstants.AvailableRangeField.PARTNER_RANGE, "{\"type\":\"ALL\"}");
                }
            }
        }
    }

    public void removeMobileButton(ILayout layout) {
        if (null == layout) {
            return;
        }
        List<IButton> buttons = layout.getButtons();
        if (CollectionUtils.empty(buttons)) {
            return;
        }
        List<String> toRemoveButtons = Lists.newArrayList(ObjectAction.CREATE.getActionCode(),
                ObjectAction.UPDATE.getActionCode(), ObjectAction.DELETE.getActionCode(),
                ObjectAction.START_BPM.getActionCode(), ObjectAction.CLONE.getActionCode(),
                ObjectAction.ENTER_ACCOUNT.getActionCode(),ObjectAction.INTELLIGENTFORM.getActionCode());
        buttons.removeIf(x -> toRemoveButtons.contains(x.getAction()));
        layout.setButtons(buttons);
    }

    /**
     * 当适用范围为全部时，不显示可售客户上的客户名称和可售产品上的产品名称
     *
     * @param fieldApiName
     * @param objectDataList
     */
    public void handleALL(String fieldApiName, List<IObjectData> objectDataList) {
        objectDataList.forEach(objectData -> {
            if (AvailableConstants.PublicConstants.RANGE_ALL.equals(objectData.get(fieldApiName, String.class))) {
                objectData.set(fieldApiName, null);
            }
        });
    }

    public void processDetailData(IObjectData dbObjectData, IObjectData objectData, Map<String, List<IObjectData>> detailDataMap) {
        DetailObjectUtils.processDetailData(dbObjectData, objectData, detailDataMap, getSpecialFieldMap());
    }

    private Map<String, String> getSpecialFieldMap() {
        Map<String, String> fieldObjectNameMap = Maps.newHashMapWithExpectedSize(3);
        fieldObjectNameMap.put(AvailableConstants.AvailableRangeField.ACCOUNT_RANGE, SFAPreDefine.AvailableAccount.getApiName());
        fieldObjectNameMap.put(AvailableConstants.AvailableRangeField.PRODUCT_RANGE, SFAPreDefine.AvailableProduct.getApiName());
        return fieldObjectNameMap;
    }

    private List<IObjectData> filterAvailableRange(User user, List<Map> mapList, SearchTemplateQuery searchTemplateQuery) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        List<String> availableRangeIds = Lists.newArrayList();
        for (Map map : mapList) {
            Object id = map.get(AvailableConstants.PriceBookField.AVAILABLE_RANGE_ID);
            if (null != id) {
                availableRangeIds.add(id.toString());
            }
        }
        IFilter filter = new Filter();
        filter.setFieldName(DBRecord.ID);
        filter.setOperator(Operator.IN);
        filter.setFieldValues(availableRangeIds);
        searchTemplateQuery.getFilters().add(filter);
        searchTemplateQuery.setLimit(5000);
        QueryResult<IObjectData> queryResult = metaDataFindServiceExt.findBySearchQueryWithFields(user, SFAPreDefine.AvailableRange.getApiName(), searchTemplateQuery,
                Lists.newArrayList(DBRecord.ID, AvailableConstants.AvailableRangeField.NAME, AvailableConstants.AvailableRangeField.ORG_RANGE,
                        AvailableConstants.AvailableRangeField.ACCOUNT_RANGE, AvailableConstants.AvailableRangeField.PRIORITY), true);
        if (null != queryResult && CollectionUtils.notEmpty(queryResult.getData())) {
            objectDataList = queryResult.getData();
        }
        //外部使用了available_range_id字段，重新赋值
        objectDataList.forEach(r -> r.set(AvailableConstants.PriceBookField.AVAILABLE_RANGE_ID, r.getId()));
        return objectDataList;
    }

    public boolean isAllProductAvailable(String tenantId, List<String> availableRangeIds) {
        if (CollectionUtils.empty(availableRangeIds)) {
            return false;
        }
        String sql = ConcatenateSqlUtils.getAllAvailableProductSql(tenantId, availableRangeIds);
        try {
            List<Map> rst = objectDataService.findBySql(tenantId, sql);
            return CollectionUtils.notEmpty(rst);
        } catch (MetadataServiceException e) {
            log.error("isAllProductAvailable findBySql error. sql:{} ", sql, e);
        }
        return false;
    }

    private IFilter buildFilter(String fieldName, List<String> fieldValues, Operator operator) {
        IFilter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setFieldValues(fieldValues);
        filter.setOperator(operator);
        return filter;
    }

    public List<String> buildSubQuery(List<String> availableRangeIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(0);
        List<IFilter> filterList = Lists.newArrayList();
        filterList.add(buildFilter(AvailableConstants.ProductResultField.AVAILABLE_RANGE_ID, availableRangeIds, Operator.IN));
        filterList.add(buildFilter(DBRecord.IS_DELETED, Lists.newArrayList("0"), Operator.EQ));
        query.setFilters(filterList);
        return Lists.newArrayList(JSON.toJSONString(query), SFAPreDefine.AvailableProduct.getApiName(), AvailableConstants.ProductResultField.PRODUCT_ID);
    }

    public boolean isAllPriceBook(User user, List<String> availableRangeIdList, List<IObjectData> availablePriceBookList, List<String> noAvailableRangeList, SearchTemplateQuery searchTemplateQuery) {
        availableRangeIdList = availableRangeIdList.stream().distinct().collect(Collectors.toList());
        List<String> tmpAvailableRangeIdList = availablePriceBookList.stream().map(o -> o.get(AvailableConstants.PriceBookField.AVAILABLE_RANGE_ID, String.class)).distinct().collect(Collectors.toList());
        List<String> tmpNoAvailableRangeList = ListUtils.removeAll(availableRangeIdList, tmpAvailableRangeIdList);
        if (CollectionUtils.notEmpty(tmpNoAvailableRangeList)) {
            if (searchTemplateQuery == null) {
                noAvailableRangeList.addAll(tmpNoAvailableRangeList);
                return true;
            } else {
                List<IObjectData> noAvailablePriceBookList = getAvailableRangePriceBookList(user, tmpNoAvailableRangeList);
                List<String> tmpNoAvailableRangeIdList = noAvailablePriceBookList.stream().map(o -> o.get(AvailableConstants.PriceBookField.AVAILABLE_RANGE_ID, String.class)).distinct().collect(Collectors.toList());
                tmpNoAvailableRangeList = ListUtils.removeAll(tmpNoAvailableRangeList, tmpNoAvailableRangeIdList);
                if (CollectionUtils.notEmpty(tmpNoAvailableRangeList)) {
                    noAvailableRangeList.addAll(tmpNoAvailableRangeList);
                    return true;
                }
            }
        }
        return false;
    }

    //取价查询小工具
    public boolean openAvailablePriceBookNotNullToll(User user, List<String> available) {
        if (bizConfigThreadLocalCacheService.isOpenAvailablePriceBook(user.getTenantId())) {
            SearchTemplateQuery searchQuery = new SearchTemplateQuery();
            searchQuery.setLimit(1);
            searchQuery.setNeedReturnQuote(false);
            searchQuery.setNeedReturnCountNum(false);
            searchQuery.setPermissionType(0);
            searchQuery.setDataRightsParameter(null);
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, "is_deleted", "0");
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, "available_range_id", available);
            QueryResult<IObjectData> queryResult = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, SFAPreDefine.AvailablePriceBook.getApiName(), searchQuery);
            if (CollectionUtils.notEmpty(queryResult.getData())) {
                return true;
            }
            return false;
        }
        return false;
    }

    public void buildValidPeriodTieredPriceSearchQuery(String tenantId, SearchTemplateQuery searchTemplateQuery, IObjectData sourceData, ObjectDataDocument objectData) {
        if (objectData == null) {
            return;
        }
        boolean isStratifiedOrTieredPrice = bizConfigThreadLocalCacheService.isOpenStratifiedOrTieredPrice(tenantId);
        boolean isOpenValidPeriod = bizConfigThreadLocalCacheService.isOpenPriceBookProductValidPeriod(tenantId);
        if (!isStratifiedOrTieredPrice && !isOpenValidPeriod) {
            return;
        }
        boolean isFilterQuantity = false;
        BigDecimal filterQuantity = objectData.toObjectData().get("filter_quantity", BigDecimal.class);
        if (isStratifiedOrTieredPrice && filterQuantity != null) {
            isFilterQuantity = true;
        }
        String priceBookId = objectData.toObjectData().get("pricebook_id", String.class);
        if (StringUtils.isEmpty(priceBookId)) {
            return;
        }
        StringBuilder name = new StringBuilder();
        Long businessDate = ValidDateUtils.getValidDate(tenantId, null, ObjectDataDocument.of(sourceData));
        Long nowDateTime = null == businessDate ? Long.valueOf(System.currentTimeMillis()) : businessDate;
        StringBuilder partitionField = new StringBuilder("product_id");
        boolean isOpenMultiUnitPriceBook = bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(tenantId);
        if (isOpenMultiUnitPriceBook) {
            partitionField.append(",actual_unit");
        }
        if (isStratifiedOrTieredPrice && !isFilterQuantity && GrayUtil.isTieredPriceListOptimize(tenantId)) {
            isStratifiedOrTieredPrice = isMultiTieredPrice(tenantId, priceBookId, isOpenMultiUnitPriceBook, isOpenValidPeriod);
        }
        if (isFilterQuantity) {
            String quantityCondition = "(start_count is null or start_count < " + filterQuantity + ") AND (end_count is null or end_count >= " + filterQuantity + ")";
            if (isOpenValidPeriod) {
                String dateCondition = "(start_date is null or start_date <= " + nowDateTime + ") AND (end_date is null or end_date >= " + nowDateTime + ")";
                name.append(String.format("(SELECT id FROM price_book_product A WHERE tenant_id = '%s' AND pricebook_id = '%s' AND is_deleted = 0 AND %s AND %s) AS newTable", SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(priceBookId), dateCondition, quantityCondition));
            } else {
                name.append(String.format("(SELECT id FROM price_book_product A WHERE tenant_id = '%s' AND pricebook_id = '%s' AND is_deleted = 0 AND %s) AS newTable", SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(priceBookId), quantityCondition));
            }
        } else {
            if (isStratifiedOrTieredPrice) {
                if (isOpenValidPeriod) {
                    String dateCondition = "(start_date is null or start_date <= " + nowDateTime + ") AND (end_date is null or end_date >= " + nowDateTime + ")";
                    name.append(String.format("(SELECT id FROM ( SELECT id, product_id, ROW_NUMBER() OVER (PARTITION BY " + partitionField.toString() + " ORDER BY start_count) AS rank FROM price_book_product WHERE tenant_id = '%s' AND pricebook_id = '%s' AND is_deleted = 0 AND %s) A WHERE rank = 1) AS newTable", SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(priceBookId), dateCondition));
                } else {
                    name.append(String.format("(SELECT id FROM ( SELECT id, product_id, ROW_NUMBER() OVER (PARTITION BY " + partitionField.toString() + " ORDER BY start_count) AS rank FROM price_book_product WHERE tenant_id = '%s' AND pricebook_id = '%s' AND is_deleted = 0) A WHERE rank = 1) AS newTable", SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(priceBookId)));
                }
            } else if (isOpenValidPeriod) {
                String dateCondition = "(start_date is null or start_date <= " + nowDateTime + ") AND (end_date is null or end_date >= " + nowDateTime + ")";
                name.append(String.format("(SELECT id FROM price_book_product A WHERE tenant_id = '%s' AND pricebook_id = '%s' AND is_deleted = 0 AND %s) AS newTable", SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(priceBookId), dateCondition));
            }
        }
        if (StringUtils.isEmpty(name)) {
            return;
        }
        ISpecifiedTableParameter specifiedTableParameter = new SpecifiedTableParameter();
        ISpecifiedTableParameter.JoinCondition joinCondition = ISpecifiedTableParameter.JoinCondition.builder().mainTableColumn("id").joinTableColumn("id").build();
        specifiedTableParameter.setJoinConditions(Lists.newArrayList(joinCondition));
        specifiedTableParameter.setJoinPattern("inner");
        specifiedTableParameter.setTableName(name.toString());
        specifiedTableParameter.setTableNameAlias("newTable");
        searchTemplateQuery.setSpecifiedTableParameter(specifiedTableParameter);
    }

    private boolean isMultiTieredPrice(String tenantId, String priceBookId, boolean isOpenMultiUnitPriceBook, boolean isOpenValidPeriod) {
        String sql = ConcatenateSqlUtils.getIsMultiTieredPriceSql(tenantId, priceBookId, isOpenMultiUnitPriceBook, isOpenValidPeriod);
        try {
            List<Map> rst = objectDataService.findBySql(tenantId, sql);
            return CollectionUtils.notEmpty(rst);
        } catch (MetadataServiceException e) {
            log.error("getSpecifiedTieredPriceSql findBySql error. sql:{} ", sql, e);
            throw new APPException("system error!");
        }
    }

    public List<IObjectData> findMinTieredPriceData(List<IObjectData> priceBookProductList) {
        Map<String, IObjectData> minObjectData = priceBookProductList.stream().collect(Collectors.toMap(o -> o.get("pricebook_id", String.class), Function.identity(), (c1, c2) -> {
            if (c1.get("start_count", BigDecimal.class) == null) {
                return c1;
            }
            if (c2.get("start_count", BigDecimal.class) == null) {
                return c2;
            }
            return c1.get("start_count", BigDecimal.class).compareTo(c2.get("start_count", BigDecimal.class)) <= 0 ? c1 : c2;
        }));
        return Lists.newArrayList(minObjectData.values());
    }

    public Map<String, List<IObjectData>> processDetailProductAccountRangeData(IObjectData dbMasterData,
                                                                               IObjectData objectData,
                                                                               Map<String, IObjectDescribe> objectDescribes,
                                                                               Map<String, List<IObjectData>> detailObjectData,
                                                                               User user,
                                                                               List<IObjectData> detailsToUpdate,
                                                                               List<IObjectData> detailsToAdd,
                                                                               Map<String, List<IObjectData>> tempAvailableRangeMap,
                                                                               DoubleParamFunction<IObjectData, IObjectData> modifyObjectDataByDbData,
                                                                               ThreeParamFunction<String, IObjectDescribe, List<IObjectData>> addMasterDetailFieldIntoDetailDataList,
                                                                               ThreeParamFunction<IObjectData, String, List<IObjectData>> modifyDetailObjectDataToAddWhenEdit) {
        Map<String, List<IObjectData>> dbAvailableRangeDataMap = Maps.newHashMap();
        //可售客户条件改指定
        List<IObjectData> tempAvailableAccountList = Lists.newArrayList();
        List<IObjectData> dbAvailableAccountData = processDetailRangeData(dbMasterData, objectData, objectDescribes, detailObjectData, user, detailsToUpdate, detailsToAdd, tempAvailableAccountList,
                SFAPreDefine.AvailableAccount.getApiName(), AvailableConstants.UseRangeLabel.ACCOUNT_RANGE_LABEL, AvailableConstants.AvailableRangeField.ACCOUNT_RANGE, modifyObjectDataByDbData,
                addMasterDetailFieldIntoDetailDataList, modifyDetailObjectDataToAddWhenEdit);
        if (CollectionUtils.notEmpty(dbAvailableAccountData)) {
            dbAvailableRangeDataMap.put(SFAPreDefine.AvailableAccount.getApiName(), dbAvailableAccountData);
        }
        if (CollectionUtils.notEmpty(tempAvailableAccountList)) {
            tempAvailableRangeMap.put(SFAPreDefine.AvailableAccount.getApiName(), tempAvailableAccountList);
        }
        //可售产品条件改指定
        List<IObjectData> tempAvailableProductList = Lists.newArrayList();
        List<IObjectData> dbAvailableProductData = processDetailRangeData(dbMasterData, objectData, objectDescribes, detailObjectData, user, detailsToUpdate, detailsToAdd, tempAvailableProductList,
                SFAPreDefine.AvailableProduct.getApiName(), AvailableConstants.UseRangeLabel.PRODUCT_RANGE_LABEL, AvailableConstants.AvailableRangeField.PRODUCT_RANGE, modifyObjectDataByDbData,
                addMasterDetailFieldIntoDetailDataList, modifyDetailObjectDataToAddWhenEdit);
        if (CollectionUtils.notEmpty(dbAvailableProductData)) {
            dbAvailableRangeDataMap.put(SFAPreDefine.AvailableProduct.getApiName(), dbAvailableProductData);
        }
        if (CollectionUtils.notEmpty(tempAvailableProductList)) {
            tempAvailableRangeMap.put(SFAPreDefine.AvailableProduct.getApiName(), tempAvailableProductList);
        }
        return dbAvailableRangeDataMap;
    }

    public List<IObjectData> processDetailRangeData(IObjectData dbMasterData,
                                                    IObjectData objectData,
                                                    Map<String, IObjectDescribe> objectDescribes,
                                                    Map<String, List<IObjectData>> detailObjectData,
                                                    User user,
                                                    List<IObjectData> detailsToUpdate,
                                                    List<IObjectData> detailsToAdd,
                                                    List<IObjectData> tempAvailableRangeList,
                                                    String detailObjectApiName,
                                                    String rangeLabel,
                                                    String rangeType,
                                                    DoubleParamFunction<IObjectData, IObjectData> modifyObjectDataByDbData,
                                                    ThreeParamFunction<String, IObjectDescribe, List<IObjectData>> addMasterDetailFieldIntoDetailDataList,
                                                    ThreeParamFunction<IObjectData, String, List<IObjectData>> modifyDetailObjectDataToAddWhenEdit) {
        String oldRange = dbMasterData.get(rangeType, String.class);
        if (StringUtils.isEmpty(oldRange)) {
            return Lists.newArrayList();
        }
        String range = objectData.get(rangeType, String.class);
        if (StringUtils.isEmpty(range)) {
            return Lists.newArrayList();
        }
        AvailableConstants.UseRangeInfo oldRangeInfo = ExceptionUtils.trySupplier(() -> JSON.parseObject(oldRange, AvailableConstants.UseRangeInfo.class), rangeLabel);
        AvailableConstants.UseRangeInfo rangeInfo = ExceptionUtils.trySupplier(() -> JSON.parseObject(range, AvailableConstants.UseRangeInfo.class), rangeLabel);
        boolean isProcess = isProcessRangeData(user, objectData, oldRangeInfo, rangeInfo);
        if (isProcess) {
            String idType;
            if (SFAPreDefine.AvailableAccount.getApiName().equals(detailObjectApiName) || SFAPreDefine.PricePolicyAccount.getApiName().equals(detailObjectApiName) || SFAPreDefine.PriceBookAccount.getApiName().equals(detailObjectApiName)) {
                idType = "account_id";
            } else {
                idType = "product_id";
            }
            List<IObjectData> rangeList = detailObjectData.get(detailObjectApiName);
            if (CollectionUtils.empty(rangeList)) {
                throw new ValidateException(String.format(I18N.text(rangeLabel), I18N.text("available_range.fix.available_account.warn")));
            }
            List<String> rangeIds = rangeList.stream().map(o -> o.get(idType, String.class)).collect(Collectors.toList());
            if (CollectionUtils.empty(rangeIds)) {
                throw new ValidateException(String.format(I18N.text(rangeLabel), I18N.text("available_range.fix.available_account.warn")));
            }
            //判断指定客户或产品在符合指定条件拍平数据中
            SearchTemplateQuery query = SoCommonUtils.buildSearchTemplateQuery(2000);
            List<IFilter> filters = Lists.newArrayList();
            if (SFAPreDefine.AvailableAccount.getApiName().equals(detailObjectApiName) || SFAPreDefine.AvailableProduct.getApiName().equals(detailObjectApiName)) {
                SearchUtil.fillFilterEq(filters, "available_range_id", objectData.getId());
            } else if (SFAPreDefine.PricePolicyAccount.getApiName().equals(detailObjectApiName)) {
                SearchUtil.fillFilterEq(filters, "price_policy_id", objectData.getId());
            } else {
                SearchUtil.fillFilterEq(filters, "price_book_id", objectData.getId());
            }
            SearchUtil.fillFilterIn(filters, idType, rangeIds);
            SearchUtil.fillFilterEq(filters, "is_deleted", "0");
            query.setFilters(filters);
            QueryResult<IObjectData> result = serviceFacade.findBySearchQuery(user, detailObjectApiName, query);
            Map<String, IObjectData> oldDataMap = Maps.newHashMap();
            if (CollectionUtils.notEmpty(result.getData())) {
                oldDataMap = result.getData().stream().collect(Collectors.toMap(o -> o.get(idType, String.class), x -> x));
            }
            Map<String, IObjectData> finalOldDataMap = oldDataMap;
            List<IObjectData> dataToAdd = Lists.newArrayList();
            List<IObjectData> dbAvailableRangeData = Lists.newArrayList();
            rangeList.forEach(o -> {
                IObjectData oldAvailableAccountData = finalOldDataMap.get(o.get(idType, String.class));
                //指定的客户有在符合指定条件中的，则更新，否则新增
                if (oldAvailableAccountData != null) {
                    o.setId(oldAvailableAccountData.getId());
                    //符合指定条件拍平的name有重复的，需要重新赋值
                    o.setName(getDetailName());
                    modifyObjectDataByDbData.accept(o, oldAvailableAccountData);
                    ObjectDataExt.of(o).remove(ObjectDataExt.RELEVANT_TEAM);
                    detailsToUpdate.add(o);
                    dbAvailableRangeData.add(oldAvailableAccountData);
                } else {
                    dataToAdd.add(o);
                }
            });
            if (CollectionUtils.notEmpty(dataToAdd)) {
                modifyDetailObjectDataToAddWhenEdit.accept(objectData, detailObjectApiName, dataToAdd);
                addMasterDetailFieldIntoDetailDataList.accept(objectData.getId(), objectDescribes.get(detailObjectApiName), dataToAdd);
                detailsToAdd.addAll(dataToAdd);
            }
            tempAvailableRangeList.addAll(rangeList);
            return dbAvailableRangeData;
        }
        return Lists.newArrayList();
    }

    /**
     * 判断是否需要处理范围数据
     *
     * @param user         用户信息
     * @param objectData   对象数据
     * @param oldRangeInfo 旧范围信息
     * @param rangeInfo    新范围信息
     * @return 是否需要处理
     */
    private boolean isProcessRangeData(User user, IObjectData objectData, AvailableConstants.UseRangeInfo oldRangeInfo, AvailableConstants.UseRangeInfo rangeInfo) {
        // 如果从条件可售改为指定可售，需要处理
        if (Objects.equals(AvailableConstants.UseRangeType.CONDITION.toString(), oldRangeInfo.getType()) &&
                Objects.equals(AvailableConstants.UseRangeType.FIXED.toString(), rangeInfo.getType())) {
            return true;
        }
        // 检查是否为多级订单场景下的特殊处理
        boolean isDhtMultiLevelOrder = bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(user.getTenantId());
        boolean isPriceBookOrAvailableRange = SFAPreDefine.PriceBook.getApiName().equals(objectData.getDescribeApiName()) ||
                SFAPreDefine.AvailableRange.getApiName().equals(objectData.getDescribeApiName());
        // 多级订单场景下，价目表或可售范围从全部可售改为指定可售的情况也需要处理
        return isPriceBookOrAvailableRange &&
                isDhtMultiLevelOrder &&
                AvailableConstants.UseRangeType.ALL.toString().equals(oldRangeInfo.getType()) &&
                AvailableConstants.UseRangeType.FIXED.toString().equals(rangeInfo.getType());
    }

    public boolean domainQueryAvailableRangeEnable(StandardRelatedListController.Arg arg, boolean priceDomainFlag, DomainPluginParam priceDomainPluginParam, String tenantId, String field) {
        String detailObjectName = Optional.ofNullable(arg.getObjectData()).map(x -> x.toObjectData()).map(x -> x.getDescribeApiName()).orElse(null);
        if (!priceDomainFlag || StringUtils.isBlank(detailObjectName) || StringUtils.isBlank(arg.getRelatedListName())) {
            return false;
        }
        List<DomainPluginParam.DetailObj> details = priceDomainPluginParam.getDetails();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(details)) {
            return false;
        }
        DomainPluginParam.DetailObj detailObj = details.stream().filter(x -> Objects.equals(x.getObjectApiName(), detailObjectName)).findFirst().orElse(null);
        String fieldName = Optional.ofNullable(detailObj).map(x -> x.getFieldMapping()).map(x -> x.get(field)).orElse("");
        if (StringUtils.isBlank(fieldName)) {
            return false;
        }
        IObjectDescribe detailObject = serviceFacade.findObject(tenantId, detailObjectName);
        String targetRelatedListName = Optional.ofNullable(detailObject).map(x -> x.getFieldDescribe(fieldName)).map(x -> x.get("target_related_list_name", String.class)).orElse(null);
        return BooleanUtils.isTrue(Objects.equals(arg.getRelatedListName(), targetRelatedListName));
    }

    private String getDetailName() {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        LocalDateTime localDateTime = LocalDateTime.now();
        String prefix = localDateTime.format(dateTimeFormatter);
        return prefix + String.format("%04d", ThreadLocalRandom.current().nextInt(10000));
    }

    public Map<String, List<IObjectData>> processRemoveDetailProductAccountRangeData(User user, IObjectData dbMasterData, IObjectData objectData,
                                                                                     Map<String, List<IObjectData>> detailObjectData) {
        Map<String, List<IObjectData>> removeDetailMap = Maps.newHashMap();
        //处理可售客户
        Map<String, List<IObjectData>> removeAccountMap = processRemoveDetailRangeData(user, dbMasterData, objectData, detailObjectData,
                SFAPreDefine.AvailableAccount.getApiName(), AvailableConstants.UseRangeLabel.ACCOUNT_RANGE_LABEL,
                AvailableConstants.AvailableRangeField.ACCOUNT_RANGE);
        if (CollectionUtils.notEmpty(removeAccountMap)) {
            removeDetailMap.putAll(removeAccountMap);
        }
        //处理可售产品
        Map<String, List<IObjectData>> removeProductMap = processRemoveDetailRangeData(user, dbMasterData, objectData, detailObjectData,
                SFAPreDefine.AvailableProduct.getApiName(), AvailableConstants.UseRangeLabel.PRODUCT_RANGE_LABEL,
                AvailableConstants.AvailableRangeField.PRODUCT_RANGE);
        if (CollectionUtils.notEmpty(removeProductMap)) {
            removeDetailMap.putAll(removeProductMap);
        }
        return removeDetailMap;
    }

    public Map<String, List<IObjectData>> processRemoveDetailRangeData(User user, IObjectData dbMasterData, IObjectData objectData,
                                                                       Map<String, List<IObjectData>> detailObjectData,
                                                                       String detailObjectApiName,
                                                                       String rangeLabel,
                                                                       String rangeType) {
        Map<String, List<IObjectData>> removeDetailMap = Maps.newHashMap();
        String oldRange = dbMasterData.get(rangeType, String.class);
        if (StringUtils.isEmpty(oldRange)) {
            return removeDetailMap;
        }
        String range = objectData.get(rangeType, String.class);
        if (StringUtils.isEmpty(range)) {
            return removeDetailMap;
        }
        AvailableConstants.UseRangeInfo oldRangeInfo = ExceptionUtils.trySupplier(() -> JSON.parseObject(oldRange, AvailableConstants.UseRangeInfo.class), rangeLabel);
        AvailableConstants.UseRangeInfo rangeInfo = ExceptionUtils.trySupplier(() -> JSON.parseObject(range, AvailableConstants.UseRangeInfo.class), rangeLabel);
        boolean isProcess = isProcessRangeData(user, objectData, oldRangeInfo, rangeInfo);
        if (isProcess) {
            List<IObjectData> rangeList = detailObjectData.get(detailObjectApiName);
            if (CollectionUtils.empty(rangeList)) {
                throw new ValidateException(String.format(I18N.text(rangeLabel), I18N.text("available_range.fix.available_account.warn")));
            }
            removeDetailMap.put(detailObjectApiName, rangeList);
            detailObjectData.remove(detailObjectApiName);
        }
        return removeDetailMap;
    }

    public List<IObjectData> getPriceKeyboard(User user, String accountId, String partnerId, String productId, ObjectDataDocument objectData,
                                              Map<String, List<ObjectDataDocument>> details, String rangeType, Integer limit) {
        IObjectData product = serviceFacade.findObjectDataIgnoreAll(user, productId, Utils.PRODUCT_API_NAME);
        if (Objects.isNull(product)) {
            throw new ValidateException(I18N.text("sfa.price.book.product.not.exist"));
        }
        boolean isMultipleUnit = BooleanUtils.isTrue(product.get("is_multiple_unit", Boolean.class));
        Long businessDate = ValidDateUtils.getValidDate(user.getTenantId(), null, objectData);
        IObjectData masterData = Objects.isNull(objectData) ? null : objectData.toObjectData();
        IObjectData saleContractObjectData = findSaleContractInfo(user, masterData);
        List<IObjectData> priceBookList = getAvailablePriceBookList(user, accountId, partnerId, businessDate, masterData, details, rangeType, true, saleContractObjectData);
        boolean realPriceConstraintMode = isRealPriceConstraintMode(user, saleContractObjectData);
        if (CollectionUtils.empty(priceBookList) && !realPriceConstraintMode) {
            return getStandardPriceBookProduct(user, productId, isMultipleUnit);
        }
        List<IObjectData> priceBookProductList = getProductByPriceBookList(user, Lists.newArrayList(productId), priceBookList, Lists.newArrayList(), businessDate);
        if (CollectionUtils.empty(priceBookProductList) && !realPriceConstraintMode) {
            return getStandardPriceBookProduct(user, productId, isMultipleUnit);
        }
        unitCoreService.fillMultipleUnit(user, ObjectDataDocument.ofList(priceBookProductList));
        List<IObjectData> priorityPriceBookProductList = getPriorityPriceBookProduct(user, limit, priceBookProductList);
        return processMultipleUnit(user, productId, priorityPriceBookProductList, isMultipleUnit);
    }

    private List<IObjectData> getStandardPriceBookProduct(User user, String productId, boolean isMultipleUnit) {
        //返回标准价目表兜底
        String standardPriceBookId = priceBookCommonService.getStandardPriceBookId(user);
        if (StringUtils.isBlank(standardPriceBookId)) {
            return Lists.newArrayList();
        }
        List<IObjectData> standardPriceBookProductList = priceBookCommonService.getPriceBookProducts(user, standardPriceBookId, Lists.newArrayList(productId));
        if (CollectionUtils.notEmpty(standardPriceBookProductList)) {
            fillData(user, standardPriceBookProductList);
            unitCoreService.fillMultipleUnit(user, ObjectDataDocument.ofList(standardPriceBookProductList));
            standardPriceBookProductList = processMultipleUnit(user, productId, standardPriceBookProductList, isMultipleUnit);
        }
        return standardPriceBookProductList;
    }

    private List<IObjectData> getPriorityPriceBookProduct(User user, Integer limit, List<IObjectData> priceBookProductList) {
        //开优先级返回价目表优先级最高的价目表产品，不开价目表默认按价目表优先级排序返回指定价目表条数，默认5条
        Comparator<IObjectData> byPriority = Comparator.comparing(x -> x.get("price_book_priority", Integer.class), Comparator.nullsLast(Integer::compareTo));
        Comparator<IObjectData> byPriceBookLastModTimeDesc = Comparator.comparing((IObjectData x) -> x.get("last_modified_time", Long.class), Comparator.nullsLast(Long::compareTo)).reversed();
        priceBookProductList.sort(byPriority.thenComparing(byPriceBookLastModTimeDesc));
        boolean isEnforcePriority = bizConfigThreadLocalCacheService.isEnforcePriority(user.getTenantId());
        limit = isEnforcePriority ? Integer.valueOf(1) : limit == null ? Integer.valueOf(5) : limit;
        List<String> priceBookIds = priceBookProductList.stream().filter(m -> StringUtils.isNotEmpty(m.get("pricebook_id", String.class))).map(o -> o.get("pricebook_id", String.class)).distinct().limit(limit).collect(Collectors.toList());
        return priceBookProductList.stream().filter(o -> priceBookIds.contains(o.get("pricebook_id", String.class))).collect(Collectors.toList());
    }

    private List<IObjectData> processMultipleUnit(User user, String productId, List<IObjectData> priorityPriceBookProductList, boolean isMultipleUnit) {
        if (!isMultipleUnit) {
            return priorityPriceBookProductList;
        }
        List<IObjectData> multiUnitInfo = multiUnitService.getMultiUnitDataByProductIds(user, Lists.newArrayList(productId));
        if (CollectionUtils.empty(multiUnitInfo)) {
            return priorityPriceBookProductList;
        }
        List<String> unitIds = multiUnitInfo.stream().filter(o -> StringUtils.isNotBlank(o.get(MultiUnitRelatedConstants.UNIT_ID, String.class))).map(m -> m.get(MultiUnitRelatedConstants.UNIT_ID, String.class)).collect(Collectors.toList());
        List<IObjectData> unitInfo = serviceFacade.findObjectDataByIds(user.getTenantId(), unitIds, Utils.UNIT_INFO_API_NAME);
        if (CollectionUtils.empty(unitInfo)) {
            return priorityPriceBookProductList;
        }
        Map<String, String> unitIdAndNameMaps = unitInfo.stream().collect(Collectors.toMap(DBRecord::getId, IObjectData::getName, (oV, nV) -> nV));
        String pricingUnit = StringUtils.EMPTY;
        BigDecimal isPriceConversionRatio = BigDecimal.ONE;
        Optional<IObjectData> isPriceRelateData = multiUnitInfo.stream()
                .filter(o -> Objects.equals(o.get("is_pricing", Boolean.class), Boolean.TRUE))
                .collect(Collectors.toList()).stream().findFirst();
        if (isPriceRelateData.isPresent()) {
            pricingUnit = isPriceRelateData.get().get(MultiUnitRelatedConstants.UNIT_ID, String.class);
            isPriceConversionRatio = isPriceRelateData.get().get("conversion_ratio", BigDecimal.class);
        }
        boolean multiUnitPriceBook = bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(user.getTenantId());
        List<IObjectData> allPriceBookProductList = Lists.newArrayList();
        if (!multiUnitPriceBook) {
            multiUnitConvert(user, priorityPriceBookProductList, multiUnitInfo, unitIdAndNameMaps, pricingUnit, isPriceConversionRatio, allPriceBookProductList);
        } else {
            multiUnitPriceBookConvert(user, priorityPriceBookProductList, multiUnitInfo, unitIdAndNameMaps, pricingUnit, isPriceConversionRatio, allPriceBookProductList);
        }
        return allPriceBookProductList;
    }

    private void fillData(User user, List<IObjectData> dataList) {
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), Utils.PRICE_BOOK_PRODUCT_API_NAME);
        infraServiceFacade.fillQuoteFieldValue(user, dataList, describe, false);
        serviceFacade.fillObjectDataWithRefObject(describe, dataList, user);
    }

    private void multiUnitConvert(User user, List<IObjectData> priorityPriceBookProductList, List<IObjectData> multiUnitInfo, Map<String, String> unitIdAndNameMaps, String pricingUnit, BigDecimal isPriceConversionRatio, List<IObjectData> allPriceBookProductList) {
        for (IObjectData priceBookProductData : priorityPriceBookProductList) {
            for (IObjectData multiUnitData : multiUnitInfo) {
                String unitId = multiUnitData.get(MultiUnitRelatedConstants.UNIT_ID, String.class);
                String unit = unitIdAndNameMaps.get(unitId);
                IObjectData newPriceBookProductData = ObjectDataExt.of(priceBookProductData).copy();
                if (!Objects.equals(unitId, pricingUnit)) {
                    BigDecimal conversionRatio = multiUnitData.get(MultiUnitRelatedConstants.CONVERSION_RATIO, BigDecimal.class);
                    calcPriceAndUnit(user, newPriceBookProductData, conversionRatio, isPriceConversionRatio, unit, unitId);
                }
                allPriceBookProductList.add(newPriceBookProductData);
            }
        }
    }

    private void multiUnitPriceBookConvert(User user, List<IObjectData> priorityPriceBookProductList, List<IObjectData> multiUnitInfo, Map<String, String> unitIdAndNameMaps, String pricingUnit, BigDecimal isPriceConversionRatio, List<IObjectData> allPriceBookProductList) {
        Map<String, List<IObjectData>> priceBookProductMap = priorityPriceBookProductList.stream().collect(Collectors.groupingBy(o -> o.get("pricebook_id", String.class)));
        for (Map.Entry<String, List<IObjectData>> entry : priceBookProductMap.entrySet()) {
            List<IObjectData> tempPriceBookProductList = entry.getValue();
            List<String> priceBookProductUnitList = tempPriceBookProductList.stream().filter(o -> StringUtils.isNotBlank(o.get(MultiUnitConstants.DetailField.ACTUAL_UNIT, String.class))).map(p -> p.get(MultiUnitConstants.DetailField.ACTUAL_UNIT, String.class)).distinct().collect(Collectors.toList());
            List<IObjectData> isPriceUnitList = tempPriceBookProductList.stream().filter(o -> Objects.equals(pricingUnit, o.get(MultiUnitConstants.DetailField.ACTUAL_UNIT, String.class))).collect(Collectors.toList());
            for (IObjectData multiUnitData : multiUnitInfo) {
                String unitId = multiUnitData.get(MultiUnitRelatedConstants.UNIT_ID, String.class);
                String unit = unitIdAndNameMaps.get(unitId);
                if (!priceBookProductUnitList.contains(unitId) && CollectionUtils.notEmpty(isPriceUnitList)) {
                    IObjectData newPriceBookProductData = ObjectDataExt.of(isPriceUnitList.get(0)).copy();
                    BigDecimal conversionRatio = multiUnitData.get(MultiUnitRelatedConstants.CONVERSION_RATIO, BigDecimal.class);
                    calcPriceAndUnit(user, newPriceBookProductData, conversionRatio, isPriceConversionRatio, unit, unitId);
                    newPriceBookProductData.set(MultiUnitConstants.DetailField.ACTUAL_UNIT, unitId);
                    tempPriceBookProductList.add(newPriceBookProductData);
                }
            }
            allPriceBookProductList.addAll(tempPriceBookProductList);
        }
        if (CollectionUtils.notEmpty(allPriceBookProductList)) {
            allPriceBookProductList.forEach(o -> {
                String unitId = o.get(MultiUnitConstants.DetailField.ACTUAL_UNIT, String.class);
                String unitName = unitIdAndNameMaps.get(unitId);
                o.set("unit", unitName);
                o.set("unit__r", unitName);
                o.set("unit__v", unitId);
            });
        }
    }

    private void calcPriceAndUnit(User user, IObjectData priceBookProductData, BigDecimal conversionRatio, BigDecimal isPriceConversionRatio, String unit, String unitId) {
        //单位换算，换算价目表售价，价目表价格，浮动上下限
        convertPrice(user, "pricebook_sellingprice", priceBookProductData, conversionRatio, isPriceConversionRatio);
        convertPrice(user, "pricebook_price", priceBookProductData, conversionRatio, isPriceConversionRatio);
        convertPrice(user, "ceiling_price", priceBookProductData, conversionRatio, isPriceConversionRatio);
        convertPrice(user, "floor_price", priceBookProductData, conversionRatio, isPriceConversionRatio);
        priceBookProductData.set("unit", unit);
        priceBookProductData.set("unit__r", unit);
        priceBookProductData.set("unit__v", unitId);
        //是多单位转换数据加一个标识
        priceBookProductData.set("virtual_is_unit_convert", true);
    }

    private void convertPrice(User user, String fieldName, IObjectData data, BigDecimal conversionRatio, BigDecimal isPriceConversionRatio) {
        RoundingMode roundingMode = RoundingMode.HALF_UP;
        if (GrayUtil.isMultiUnitDecimal(user.getTenantId())) {
            roundingMode = RoundingMode.UP;
        }
        BigDecimal price = data.get(fieldName, BigDecimal.class);
        if (price != null) {
            String buyPrice = price.multiply(conversionRatio).divide(isPriceConversionRatio, getNumberOfDecimalPlace(price), roundingMode).toString();
            data.set(fieldName, buyPrice);
        }
    }

    public static int getNumberOfDecimalPlace(BigDecimal value) {
        String s = value.toPlainString();
        final int index = s.indexOf('.');
        if (index < 0) {
            return 0;
        }
        return s.length() - 1 - index;
    }

    public void mcCurrencyConvert(List<IObjectData> resultObject, String mcCurrency, User user, List<String> currencyConvertField) {
        boolean isCurrencyEnabled = bizConfigThreadLocalCacheService.isCurrencyEnabled(user.getTenantId());
        if (!isCurrencyEnabled || StringUtils.isEmpty(mcCurrency)) {
            return;
        }
        Map<String, String> exchangeRateMap = MtCurrentUtil.getExchangeRateMap(user, mcCurrency);
        for (IObjectData objectData : resultObject) {
            String dataMcCurrency = objectData.get(PriceBookConstants.ProductField.MC_CURRENCY.getApiName(), String.class);
            if (StringUtils.isEmpty(dataMcCurrency)) {
                continue;
            }
            currencyConvertField.forEach(fieldName -> {
                String fieldValue = objectData.get(fieldName, String.class);
                if (StringUtils.isNotEmpty(fieldValue)) {
                    BigDecimal convertedSalesPrice = MtCurrentUtil.changePriceToCurrency(mcCurrency,
                            new BigDecimal(fieldValue), dataMcCurrency, exchangeRateMap);
                    objectData.set(fieldName, convertedSalesPrice.toPlainString());
                }
            });
        }
    }

    public SearchTemplateQuery buildPriceBookRangeSearchQuery(User user, IObjectData objectData,
                                                              Map<String, List<ObjectDataDocument>> details,
                                                              String rangeType) {
        return buildPriceBookRangeSearchQuery(user, objectData, details, rangeType, false);
    }

    public SearchTemplateQuery buildPriceBookRangeSearchQuery(User user, IObjectData objectData,
                                                              Map<String, List<ObjectDataDocument>> details,
                                                              String rangeType, boolean isActCurrentDetailRow) {
        StopWatch stopWatch = StopWatch.create("buildPriceBookRangeSearchQuery");
        //根据类型查预设主对象或从对象的价目表数据范围
        if (StringUtils.isBlank(rangeType)) {
            return null;
        }
        IObjectDescribe describe;
        String functionObjectApiName;
        Map<String, List<IObjectData>> detailsMap = null;
        IObjectData currentData = new ObjectData();
        if (PriceBookConstants.PriceBookRangeType.MASTER.getRangeType().equals(rangeType)) {
            if (objectData == null || StringUtils.isBlank(objectData.getDescribeApiName())) {
                log.info("build price book range, objectData or objectData describeApiName is null, tenantId:{}", user.getTenantId());
                return null;
            }
            functionObjectApiName = objectData.getDescribeApiName();
            if (StringUtils.isEmpty(functionObjectApiName)) {
                return null;
            }
            describe = serviceFacade.findObject(user.getTenantId(), functionObjectApiName);
            currentData = objectData;
        } else {
            if (CollectionUtils.empty(details)) {
                log.info("build price book range, details is null, tenantId:{}", user.getTenantId());
                return null;
            }
            Optional<String> optional = details.keySet().stream().findFirst();
            if (!optional.isPresent()) {
                return null;
            }
            functionObjectApiName = optional.get();
            describe = serviceFacade.findObject(user.getTenantId(), functionObjectApiName);
            //isActCurrentDetailRow是否作用在当前明细行，如果作用在当前明细行，则需要处理数据范围基于字段且配置为本对象下字段，当前行数据为details中的第一条
            if (isActCurrentDetailRow) {
                for (Map.Entry<String, List<ObjectDataDocument>> entry : details.entrySet()) {
                    if (CollectionUtils.notEmpty(entry.getValue())) {
                        currentData = entry.getValue().get(0).toObjectData();
                        break;
                    }
                }
            }
            if (describe == null) {
                return null;
            }
            //兼容从对象价目表默认多币种条件
            ObjectDataExt objectDataExt = ObjectDataExt.of(currentData);
            if (objectData != null && objectDataExt.getCurrency() == null && ObjectDescribeExt.of(describe).containsMultiCurrencyField()) {
                objectDataExt.setCurrency(ObjectDataExt.of(objectData).getCurrency());
            }
        }
        if (describe == null) {
            log.info("describe is null, tenantId:{}", user.getTenantId());
            return null;
        }
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(PriceBookConstants.Field.PRICE_BOOK_ID.getApiName());
        if (fieldDescribe == null || StringUtils.isEmpty(fieldDescribe.get(PriceBookConstants.Field.WHERES.getApiName(), String.class))) {
            return null;
        }
        List<Wheres> wheresList = JSON.parseArray(fieldDescribe.get(PriceBookConstants.Field.WHERES.getApiName(), String.class), Wheres.class);
        if (CollectionUtils.empty(wheresList)) {
            return null;
        }
        SearchTemplateQuery priceBookRangeQuery = SoCommonUtils.buildSearchTemplateQuery(0);
        priceBookRangeQuery.setWheres(wheresList);
        if (CollectionUtils.notEmpty(details)) {
            detailsMap = details.entrySet().stream().filter(o -> o.getValue() != null).collect(Collectors.toMap(Map.Entry::getKey,
                    e -> e.getValue().stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList())));
        }
        //判断是否有函数，如果有执行函数拿到数据范围
        functionUtils.fillFunctionFilter(user, priceBookRangeQuery, objectData, detailsMap, functionObjectApiName);
        stopWatch.lap("buildPriceBookRangeSearchQuery-fillFunctionFilter");
        //处理数据范围基于条件且选择本对象下字段和主对象下字段的数据条件
        handleFilters(user, ObjectDescribeExt.of(describe), priceBookRangeQuery, currentData, objectData);
        stopWatch.lap("buildPriceBookRangeSearchQuery-handleFilters");
        stopWatch.logSlow(200);
        return priceBookRangeQuery;
    }

    public Map<String, List<ObjectDataDocument>> filterPresetObjectData(Map<String, List<ObjectDataDocument>> detailDataList,
                                                                        ObjectDataDocument objectDataDocument) {
        Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();
        IObjectData objectData = objectDataDocument == null ? null : objectDataDocument.toObjectData();
        if (objectData == null) {
            return details;
        }
        if (CollectionHelper.isNotEmpty(detailDataList)) {
            for (Map.Entry<String, List<ObjectDataDocument>> entry : detailDataList.entrySet()) {
                if (Objects.equals(entry.getKey(), objectData.getDescribeApiName())) {
                    details.put(entry.getKey(), entry.getValue());
                    break;
                }
            }
        }
        return details;
    }

    public Map<String, List<ObjectDataDocument>> convertIObjectDataToDocument(Map<String, List<IObjectData>> detailDataList) {
        Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();
        if (CollectionUtils.notEmpty(detailDataList)) {
            detailDataList.forEach((key, value) -> {
                List<ObjectDataDocument> objectDataDocuments = Lists.newArrayList();
                if (CollectionUtils.notEmpty(value)) {
                    objectDataDocuments = ObjectDataDocument.ofList(value);
                }
                details.put(key, objectDataDocuments);
            });
        }
        return details;
    }

    public List<String> processDataRange(User user, IObjectData masterData, String detailObjectApiName, List<IObjectData> detailData,
                                         List<String> availablePriceBookIds, List<String> standardPriceBookId) {
        if (findHasNativeObjectVariable(user, detailObjectApiName)) {
            return Collections.emptyList();
        }
        if (CollectionUtils.empty(availablePriceBookIds)) {
            return availablePriceBookIds;
        }
        Map<String, List<IObjectData>> detailDataList = Maps.newHashMap();
        detailDataList.put(detailObjectApiName, detailData);
        Map<String, List<ObjectDataDocument>> details = convertIObjectDataToDocument(detailDataList);
        SearchTemplateQuery priceBookRangeQuery = buildPriceBookRangeSearchQuery(user, masterData, details, PriceBookConstants.PriceBookRangeType.DETAIL.getRangeType());
        if (priceBookRangeQuery == null) {
            return availablePriceBookIds;
        }
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, DBRecord.ID, availablePriceBookIds);
        priceBookRangeQuery.setFilters(filters);
        List<IObjectData> priceBookDataList = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, Utils.PRICE_BOOK_API_NAME, priceBookRangeQuery).getData();
        if (CollectionUtils.empty(priceBookDataList)) {
            return standardPriceBookId;
        }
        return priceBookDataList.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    private boolean findHasNativeObjectVariable(User user, String detailObjectApiName) {
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), detailObjectApiName);
        if (describe == null) {
            return false;
        }
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(PriceBookConstants.Field.PRICE_BOOK_ID.getApiName());
        if (fieldDescribe == null || StringUtils.isEmpty(fieldDescribe.get(PriceBookConstants.Field.WHERES.getApiName(), String.class))) {
            return false;
        }
        List<Wheres> wheresList = JSON.parseArray(fieldDescribe.get(PriceBookConstants.Field.WHERES.getApiName(), String.class), Wheres.class);
        if (CollectionUtils.empty(wheresList)) {
            return false;
        }
        return wheresList.stream()
                .filter(wheres -> CollectionUtils.notEmpty(wheres.getFilters()))
                .flatMap(wheres -> wheres.getFilters().stream())
                .anyMatch(filter -> FilterExt.of(filter).hasNativeObjectVariable());
    }

    public void addStandardPriceBook(User user, List<IObjectData> priceBookDataList) {
        if (CollectionUtils.empty(priceBookDataList)) {
            return;
        }
        if (priceBookDataList.stream().noneMatch(x -> x.get("is_standard", Boolean.class, false))) {
            IObjectData standardPriceBook = priceBookCommonService.getStandardPriceBook(user);
            if (standardPriceBook != null && PriceBookConstants.ActiveStatus.ON.getStatus().equals(standardPriceBook.get(PriceBookConstants.Field.ACTIVESTATUS.getApiName()))) {
                priceBookDataList.add(standardPriceBook);
            }
        }
    }

    public void handleFilters(User user, ObjectDescribeExt objectDescribeExt, SearchTemplateQuery query, IObjectData objectData, IObjectData masterData) {
        if (Objects.isNull(query)) {
            return;
        }
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_660) && SearchTemplateQueryExt.of(query).isRectangleOrPentagonRelation()) {
            log.warn("mobile request before version {}, queryInfo=>{}", RequestUtil.VERSION_660, JsonUtil.toJson(query));
            throw new ValidateException(I18N.text(I18NKey.API_UPGRADE));
        }
        ObjectDataExt data = Objects.isNull(objectData) ? ObjectDataExt.of(new ObjectData()) : ObjectDataExt.of(objectData);
        IObjectDescribe masterDescribe = null;
        if (Objects.nonNull(objectDescribeExt) && objectDescribeExt.isSlaveObject()) {
            masterDescribe = objectDescribeExt.getMasterDetailFieldDescribe().map(field -> serviceFacade.findObject(user.getTenantId(), field.getTargetApiName())).orElse(null);
        }
        // 主从新建处理主对象，本对象作为变量的筛选条件
        SearchTemplateQueryExt.of(query).handleWheresFilterByObjVariable(data, masterData, masterDescribe, Utils.PRICE_BOOK_API_NAME);
        // 主从同时新建的从对象处理四角、五角关系
        if (Objects.nonNull(objectDescribeExt) && objectDescribeExt.isCreateWithMaster()) {
            Optional<MasterDetailFieldDescribe> masterDetailFieldDescribe = objectDescribeExt.getMasterDetailFieldDescribe();
            if (masterDetailFieldDescribe.isPresent()) {
                MasterDetailFieldDescribe field = masterDetailFieldDescribe.get();
                if (Objects.isNull(masterDescribe)) {
                    masterDescribe = serviceFacade.findObject(user.getTenantId(),
                            field.getTargetApiName());
                }
                SearchTemplateQueryExt.of(query).handleWheresFilter(data, masterData, masterDescribe);
                infraServiceFacade.handleChainCycle(user, query, masterData, masterDescribe);
            }
        } else {
            SearchTemplateQueryExt.of(query).handleWheresFilter(data);
        }
    }

    public void buildSearchQueryForAvailableRange(SearchTemplateQuery query, String relatedListName, String accountId) {
        if (!SALE_CONTRACT_AVAILABLE_RANGE_LIST.equals(relatedListName) || StringUtils.isBlank(accountId)) {
            return;
        }
        List<String> accountList = Lists.newArrayList(accountId, AvailableConstants.PublicConstants.RANGE_ALL);
        List<String> availableRangeIds = buildQuerySubQuery(accountList, SFAPreDefine.AvailableAccount.getApiName(), AvailableConstants.AccountResultField.AVAILABLE_RANGE_ID);
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, DBRecord.ID, availableRangeIds, 10);
    }

    public void buildSearchQueryForPriceBook(User user, SearchTemplateQuery query, String relatedListName, String accountId) {
        if (!SALE_CONTRACT_PRICEBOOK_LIST.equals(relatedListName) || StringUtils.isBlank(accountId) || !GrayUtil.isPriceBookReform(user.getTenantId())) {
            return;
        }
        List<String> accountList = Lists.newArrayList(accountId, AvailableConstants.PublicConstants.RANGE_ALL);
        List<String> priceBookIds = buildQuerySubQuery(accountList, SFAPreDefine.PriceBookAccount.getApiName(), PriceBookConstants.Field.PRICE_BOOK_ID.getApiName());
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, DBRecord.ID, priceBookIds, 10);
    }

    private List<String> buildQuerySubQuery(List<String> accountList, String objectApiName, String fieldApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(0);
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, PriceBookConstants.ACCOUNT_ID, accountList);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, DBRecord.IS_DELETED, Lists.newArrayList("0"));
        return Lists.newArrayList(JSON.toJSONString(query), objectApiName, fieldApiName);
    }

    public List<Map> findAvailableRangeInfo(String tenantId, String accountId, List<String> availableRangeIds) {
        List<String> accountList = Lists.newArrayList(accountId, AvailableConstants.PublicConstants.RANGE_ALL);
        String sql = ConcatenateSqlUtils.queryAvailableRangeSql(tenantId, accountList, availableRangeIds);
        try {
            return objectDataService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("findAvailableRangeId metadata findBySql error. sql:{} ", sql, e);
            throw new APPException("system error!");
        }
    }

    public IObjectData findSaleContractInfo(User user, IObjectData objectData) {
        if (!bizConfigThreadLocalCacheService.isOpenContractConstraintMode(user.getTenantId())
                || Objects.isNull(objectData) || StringUtils.isEmpty(objectData.get(SalesOrderConstants.SalesOrderField.SALE_CONTRACT_ID.getApiName(), String.class))
                || !SFAPreDefine.SalesOrder.getApiName().equals(objectData.get(SystemConstants.ObjectDescribeApiName, String.class))) {
            return null;
        }
        String saleContractId = objectData.get(SalesOrderConstants.SalesOrderField.SALE_CONTRACT_ID.getApiName(), String.class);
        IObjectData saleContractObjectData = serviceFacade.findObjectDataIgnoreAll(user, saleContractId, SFAPreDefine.SaleContract.getApiName());
        if (saleContractObjectData == null) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_DATA_NOT_NOT_EXIT, I18N.text("SaleContractObj.attribute.self.display_name")));
        }
        return saleContractObjectData;
    }

    public List<String> handleRealPriceConstraintQuery(User user, IObjectData saleContractObjectData, String objectApiName, String fieldApiName) {
        List<String> ids = Lists.newArrayList();
        if (!bizConfigThreadLocalCacheService.isOpenContractConstraintPricebookAvailableRange(user.getTenantId())
                || Objects.isNull(saleContractObjectData)) {
            return ids;
        }
        String constraintMode = saleContractObjectData.get(SaleContractConstants.SaleContractField.CONSTRAINT_MODE.getApiName(), String.class);
        if (!SaleContractConstants.ConstraintMode.REAL_PRICE.getApiName().equals(constraintMode)) {
            return ids;
        }
        SearchTemplateQuery searchQuery = SoCommonUtils.buildSearchTemplateQuery(0);
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, SaleContractAvailableRangeConstants.Field.SALE_CONTRACT_ID.getApiName(), saleContractObjectData.getId());
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, DBRecord.IS_DELETED, Lists.newArrayList("0"));
        QueryResult<IObjectData> queryResult = metaDataFindServiceExt.findBySearchQueryWithFields(user,
                objectApiName, searchQuery, Lists.newArrayList(fieldApiName), true);
        if (CollectionUtils.empty(queryResult.getData())) {
            ids.add("-99");
            return ids;
        }
        ids.addAll(queryResult.getData().stream().
                filter(o -> StringUtils.isNotBlank(o.get(fieldApiName, String.class))).
                map(o -> o.get(fieldApiName, String.class)).collect(Collectors.toList()));
        return ids;
    }

    public boolean getSaleContractLineIdList(User user, IObjectData masterData, List<String> productIdList, IObjectData saleContractObjectData, List<String> saleContractProductIds) {
        if (Objects.isNull(masterData)
                || !SFAPreDefine.SalesOrder.getApiName().equals(masterData.get(SystemConstants.ObjectDescribeApiName))
                || !isDetailConstraintMode(user, saleContractObjectData)) {
            return false;
        }
        String saleContractId = masterData.get(SalesOrderConstants.SalesOrderField.SALE_CONTRACT_ID.getApiName(), String.class);
        SearchTemplateQuery query = SoCommonUtils.buildSearchTemplateQuery(0);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, SalesOrderConstants.SalesOrderField.SALE_CONTRACT_ID.getApiName(), saleContractId);
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, SaleContractConstants.SaleContractLineField.PRODUCT_ID.getApiName(), productIdList);
        List<IObjectData> saleContractLineDataList = metaDataFindServiceExt.findBySearchQueryWithFields(user, SFAPreDefine.SaleContractLine.getApiName(),
                query, Lists.newArrayList(DBRecord.ID, SaleContractConstants.SaleContractLineField.PRODUCT_ID.getApiName()), true).getData();
        saleContractProductIds.addAll(saleContractLineDataList.stream().map(o -> o.get(SaleContractConstants.SaleContractLineField.PRODUCT_ID.getApiName(), String.class)).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
        return true;
    }

    public boolean isRealPriceConstraintMode(User user, IObjectData saleContractObjectData) {
        if (!bizConfigThreadLocalCacheService.isOpenContractConstraintPricebookAvailableRange(user.getTenantId())
                || Objects.isNull(saleContractObjectData)) {
            return false;
        }
        String constraintMode = saleContractObjectData.get(SaleContractConstants.SaleContractField.CONSTRAINT_MODE.getApiName(), String.class);
        return SaleContractConstants.ConstraintMode.REAL_PRICE.getApiName().equals(constraintMode);
    }

    public boolean isDetailConstraintMode(User user, IObjectData saleContractObjectData) {
        if (!bizConfigThreadLocalCacheService.isOpenContractConstraintMode(user.getTenantId())
                || Objects.isNull(saleContractObjectData)) {
            return false;
        }
        String constraintMode = saleContractObjectData.get(SaleContractConstants.SaleContractField.CONSTRAINT_MODE.getApiName(), String.class);
        return StringUtils.isEmpty(constraintMode) || SaleContractConstants.ConstraintMode.DETAIL.getApiName().equals(constraintMode);
    }

    public void buildRangeForMerchantRange(User user, String partnerId, String applyPartnerRange, SearchTemplateQuery query, String recordType, String apiName) {
        IObjectData objectData = new ObjectData();
        objectData.setRecordType(recordType);
        objectData.setDescribeApiName(apiName);
        objectData.set("apply_partner_range", applyPartnerRange);
        buildRangeForMerchantRange(user, partnerId, query, objectData, false);
    }

    public void buildRangeForMerchantRange(User user, String partnerId, SearchTemplateQuery query, IObjectData sourceData, boolean fromChannelMyProduct) {
        if (!bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(user.getTenantId())) {
            return;
        }
        if (Objects.isNull(sourceData)
                || !MultiLevelOrderConstants.RECORD_TYPE.equals(sourceData.getRecordType())
                || (!MERCHANT_RANGE_FILTER_API_NAME.contains(sourceData.getDescribeApiName()) && !fromChannelMyProduct)) {
            return;
        }
        if (Lists.newArrayList(Utils.AVAILABLE_RANGE_OBJ_API_NAME, Utils.PRICE_BOOK_API_NAME).contains(sourceData.getDescribeApiName())) {
            partnerId = getPartnerIdByPartnerRange(sourceData);
        }
        if (StringUtils.isEmpty(partnerId)) {
            return;
        }
        query.setPermissionType(0);
        IObjectData partnerData;
        try {
            IActionContext actionContext = ActionContextExt.of(user).skipRelevantTeam().getContext();
            partnerData = objectDataService.findById(partnerId, user.getTenantId(), actionContext, SFAPreDefine.Partner.getApiName());
        } catch (MetadataServiceException e) {
            log.error("findById error:", e);
            return;
        }
        if (Objects.isNull(partnerData) || !Boolean.TRUE.equals(partnerData.get("product_range_control", Boolean.class))) {
            return;
        }
        SearchTemplateQuery subQuery = new SearchTemplateQuery();
        subQuery.setLimit(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "is_deleted", "0");
        SearchUtil.fillFilterEq(filters, "merchant_product_range_id.partner_id", partnerId, true);
        SearchUtil.fillFilterEq(filters, "merchant_product_range_id.is_deleted", "0", true);
        subQuery.setFilters(filters);
        List<String> subQueryList = Lists.newArrayList(JSON.toJSONString(subQuery), "MerchantProductLinesObj", AvailableConstants.ProductResultField.PRODUCT_ID);
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, DBRecord.ID, subQueryList, 10);
    }

    public String getPartnerIdByPartnerRange(IObjectData sourceData) {
        String partnerId = null;
        String partnerRangeApiName;
        String userRangeLabel;
        if (Utils.AVAILABLE_RANGE_OBJ_API_NAME.equals(sourceData.getDescribeApiName())) {
            partnerRangeApiName = AvailableConstants.AvailableRangeField.PARTNER_RANGE;
            userRangeLabel = AvailableConstants.UseRangeLabel.PARTNER_RANGE_LABEL;
        } else {
            partnerRangeApiName = PriceBookConstants.Field.APPLY_PARTNER_RANGE.getApiName();
            userRangeLabel = PriceBookConstants.UseRangeLabel.APPLY_PARTNER_RANGE_LABEL;
        }
        String partnerRange = sourceData.get(partnerRangeApiName, String.class);
        if (StringUtils.isNotEmpty(partnerRange)) {
            CommonConstants.UseRangeInfo partnerRangeInfo = ExceptionUtils.trySupplier(() -> JSON.parseObject(partnerRange, CommonConstants.UseRangeInfo.class), userRangeLabel);
            if (AvailableConstants.UseRangeType.FIXED.toString().equals(partnerRangeInfo.getType())) {
                List<String> partnerIds = JSON.parseArray(partnerRangeInfo.getValue(), String.class);
                if (CollectionUtils.notEmpty(partnerIds)) {
                    partnerId = partnerIds.get(0);
                }
            }
        }
        return partnerId;
    }

    public QueryResult<IObjectData> getAvailableRangeByPartner(User user, GetAvailableRangeByPartnerModel.Arg arg) {
        if (StringUtils.isEmpty(arg.getPartnerId())
                || !bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(user.getTenantId())) {
            return new QueryResult<>();
        }
        SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());
        String sql = ConcatenateSqlUtils.getAvailableRangeByPartner(user.getTenantId(), Lists.newArrayList(arg.getPartnerId()));
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterBySql(filters, DBRecord.ID, Operator.IN, sql);
        SearchTemplateQueryExt.of(query).addFilters(filters);
        query.setPermissionType(0);
        return serviceFacade.findBySearchQuery(user, SFAPreDefine.AvailableRange.getApiName(), query);
    }

    public void handlePartnerRange(User user, IObjectData objectData) {
        if (!bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(user.getTenantId())) {
            return;
        }
        if (!MultiLevelOrderConstants.RECORD_TYPE.equals(objectData.getRecordType())) {
            return;
        }
        String partnerRangeApiName;
        String userRangeLabel;
        if (Utils.AVAILABLE_RANGE_OBJ_API_NAME.equals(objectData.getDescribeApiName())) {
            partnerRangeApiName = AvailableConstants.AvailableRangeField.PARTNER_RANGE;
            userRangeLabel = AvailableConstants.UseRangeLabel.PARTNER_RANGE_LABEL;
        } else {
            partnerRangeApiName = PriceBookConstants.Field.APPLY_PARTNER_RANGE.getApiName();
            userRangeLabel = PriceBookConstants.UseRangeLabel.APPLY_PARTNER_RANGE_LABEL;
        }
        String partnerRange = objectData.get(partnerRangeApiName, String.class);
        if (StringUtils.isNotEmpty(partnerRange)) {
            CommonConstants.UseRangeInfo partnerRangeInfo = ExceptionUtils.trySupplier(() -> JSON.parseObject(partnerRange, CommonConstants.UseRangeInfo.class), userRangeLabel);
            if (AvailableConstants.UseRangeType.FIXED.toString().equals(partnerRangeInfo.getType())) {
                List<String> partnerIds = JSON.parseArray(partnerRangeInfo.getValue(), String.class);
                if (CollectionUtils.empty(partnerIds)) {
                    return;
                }
                List<IObjectData> result = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), partnerIds, Utils.PARTNER_API_NAME);
                if (CollectionUtils.empty(result)) {
                    return;
                }
                List<String> partnerNames = result.stream().map(data -> StringUtils.isNotEmpty(data.get("name__r", String.class)) ? data.get("name__r", String.class) : data.getName()).collect(Collectors.toList());
                partnerRangeInfo.setValue__r(JSON.toJSONString(partnerNames));
                objectData.set(partnerRangeApiName, JSON.toJSONString(partnerRangeInfo));
            }
        }
    }

    public void buildPriceBookRangeSearchByPartner(User user, SearchTemplateQuery query, String partnerId, String fieldName) {
        if (StringUtils.isEmpty(partnerId)) {
            log.warn("partnerId is null, tenantId:{}", user.getTenantId());
            return;
        }
        if (!bizConfigThreadLocalCacheService.isPriceBookEnabled(user.getTenantId())) {
            //没开价目表，查不出任何数据
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, fieldName, "-99");
            return;
        }
        //不走数据权限
        query.setPermissionType(0);
        List<String> priceBookIdList = getPriceBookListByPartner(user, partnerId);
        if (CollectionUtils.empty(priceBookIdList)) {
            //给一个不存在的值,保证查不出任何数据
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, fieldName, "-99");
            return;
        }
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, fieldName, Lists.newArrayList(priceBookIdList));
    }

    private List<String> getPriceBookListByPartner(User user, String partnerId) {
        List<String> availablePriceBookIdList = Lists.newArrayList();
        if (bizConfigThreadLocalCacheService.isOpenAvailableRange(user.getTenantId())
                && bizConfigThreadLocalCacheService.isOpenAvailablePriceBook(user.getTenantId())) {
            //根据合作伙伴过滤可售范围
            List<String> availableRangeIdList = getAvailableRangeListByPartner(user, partnerId);
            //根据可售范围获取可售价目表
            List<IObjectData> availablePriceBookList = getAvailableRangePriceBookList(user, availableRangeIdList);
            if (CollectionUtils.empty(availablePriceBookList)) {
                return Lists.newArrayList();
            }
            availablePriceBookIdList = availablePriceBookList.stream().map(o -> o.get(AvailableConstants.PriceBookField.PRICE_BOOK_ID, String.class)).collect(Collectors.toList());
        }
        return priceBookCommonService.getPriceBookListByPartner(user, partnerId, availablePriceBookIdList);
    }

    private List<String> getAvailableRangeListByPartner(User user, String partnerId) {
        if (StringUtils.isBlank(partnerId)) {
            return Lists.newArrayList();
        }
        List<String> partnerIdList = Lists.newArrayList(partnerId, "ALL");
        List<Map> queryResult = findAvailableRangeByPartner(user.getTenantId(), partnerIdList);
        if (CollectionUtils.empty(queryResult)) {
            return Lists.newArrayList();
        }
        return queryResult.stream().map(availableRange -> availableRange.get(DBRecord.ID).toString()).collect(Collectors.toList());
    }

    private List<Map> findAvailableRangeByPartner(String tenantId, List<String> partnerIdList) {
        String sql = ConcatenateSqlUtils.getAvailableRangeByPartnerSql(tenantId, partnerIdList);
        try {
            return objectDataService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("findAvailableRangeByPartner findBySql error. sql:{} ", sql, e);
            throw new APPException("system error!");
        }
    }

    public Integer getPriceBookProductCountByPartner(User user, String partnerId, String priceBookId) {
        if (StringUtils.isEmpty(partnerId) || StringUtils.isEmpty(priceBookId)) {
            log.warn("partnerId is null or priceBookId is null, tenantId:{}", user.getTenantId());
            return 0;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, PriceBookConstants.ProductField.PRICEBOOKID.getApiName(), priceBookId);
        buildProductRangeSearchByPartner(user, query, partnerId);
        QueryResult<IObjectData> result = serviceFacade.findBySearchQuery(user, SFAPreDefine.PriceBookProduct.getApiName(), query);
        return result.getTotalNumber();
    }

    public void buildProductRangeSearchByPartner(User user, SearchTemplateQuery query, String partnerId) {
        if (StringUtils.isEmpty(partnerId)) {
            log.warn("partnerId is null, tenantId:{}", user.getTenantId());
            return;
        }
        query.setPermissionType(0);
        List<String> availableProducts = getAvailableProductListByPartner(user, partnerId);
        if (CollectionUtils.empty(availableProducts)) {
            //给一个不存在的值,保证查不出任何数据
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, PriceBookConstants.Field.PRODUCT_ID.getApiName(), "-99");
            return;
        }
        if (availableProducts.contains(AvailableConstants.PublicConstants.RANGE_ALL)) {
            return;
        }
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, PriceBookConstants.Field.PRODUCT_ID.getApiName(), availableProducts, 10);
    }

    private List<String> getAvailableProductListByPartner(User user, String partnerId) {
        if (!bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId())) {
            return Lists.newArrayList(AvailableConstants.PublicConstants.RANGE_ALL);
        }
        //根据合作伙伴过滤可售范围
        List<String> availableRangeIdList = getAvailableRangeListByPartner(user, partnerId);
        if (CollectionUtils.empty(availableRangeIdList)) {
            return Lists.newArrayList();
        }
        if (isAllProductAvailable(user.getTenantId(), availableRangeIdList)) {
            return Lists.newArrayList(AvailableConstants.PublicConstants.RANGE_ALL);
        }
        return buildSubQuery(availableRangeIdList);
    }

    /**
     * 根据多商城配置过滤可售范围
     *
     * @param user      用户信息
     * @param queryList 可售范围列表
     */
    private void filterAvailableRangeMultiShoppingIds(User user, List<IObjectData> queryList) {
        if (CollectionUtils.empty(queryList) ||
                !user.isOutUser() ||
                !bizConfigThreadLocalCacheService.isMultiShoppingMallEnabled(user.getTenantId())) {
            return;
        }
        // 获取多商城配置
        String appId = RequestContextManager.getContext().getAppId();
        MultiShoppingMallModel.Arg arg = MultiShoppingMallModel.Arg.builder()
                .linkAppId(appId)
                .build();
        MultiShoppingMallModel.MultiShoppingMallResult result = multiShoppingMallProxy.getByLinkAppId(
                arg, ProxyUtils.getCrmHeader(user.getTenantId(), user));
        // 根据多商城配置的可售范围ID过滤
        if (result.getResult() != null && CollectionUtils.notEmpty(result.getResult().getAvailableRangeIds())) {
            List<String> availableRangeIds = result.getResult().getAvailableRangeIds();
            queryList.removeIf(o -> !availableRangeIds.contains(o.getId()));
        }
    }
}