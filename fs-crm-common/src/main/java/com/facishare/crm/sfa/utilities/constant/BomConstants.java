package com.facishare.crm.sfa.utilities.constant;

import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Set;

/**
 * Bom 常量类
 *
 * <AUTHOR>
 */
public class BomConstants {

    public static final String DESC_API_NAME = "BOMObj";

    public static final String DESC_BOM_PATH_API_NAME = "BOMPathObj";

    public static final String FIELD_PRODUCT_ID = "product_id";

    public static final String FIELD_BOM_ID = "bom_id";

    public static final String FIELD_PARENT_PRODUCT_ID = "parent_product_id";

    public static final String FIELD_PARENT_BOM_ID = "parent_bom_id";

    public static final String FIELD_ROOT_PRODUCT_ID = "root_product_id";

    public static final String FIELD_PRODUCT_PATH = "product_path";

    public static final String FIELD_PRODUCT_GROUP_ID = "product_group_id";

    public static final String DESC_BOM_API_NAME = "BOMObj";

    public static final String FIELD_BOM_PATH = "bom_path";
    public static final String FIELD_BOM_CRUMB_BREAD = "crumb_bread";
    public static final String FIELD_BOM_CRUMB_BREAD_NAME = "crumb_bread_name";
    public static final String FIELD_PRODUCT_NAME_PATH = "product_bom_path";
    public static final String FIELD_PRODUCT_ID_PATH = "product_id_path";
    public static final String FIELD_ONLY_HAS_GROUP_CHILD = "only_has_group_child";
    public static final String FIELD_HAS_CHILD = "has_child";

    public static final String FIELD_ROOT_ID = "root_id";

    public static final String FIELD_PRICE_MODE = "price_mode";
    public static final String FIELD_IS_REQUIRED = "is_required";
    public static final String FIELD_SELECTED_BY_DEFAULT = "selected_by_default";

    public static final String FIELD_ADJUST_PRICE = "adjust_price";
    public static final String FIELD_ENABLED_STATUS = "enabled_status";
    public static final String FIELD_PRICE_EDITABLE = "price_editable";
    public static final String FIELD_AMOUNT_EDITABLE = "amount_editable";
    public static final String FIELD_INCREMENT = "increment";
    public static final String FIELD_AMOUNT = "amount";

    public static final String SHARE_RATE = "share_rate";

    public static final String FIELD_NODE_TYPE = "node_type";
    public static final String FIELD_TEMP_NODE_GROUP_ID = "temp_node_group_id";

    public static final String FIELD_TEMP_NODE_BOM_ID = "temp_node_bom_id";

    public static final String FIELD_UNIT_ID = "unit_id";

    public static final String FIELD_PROD_PKG_KEY = "prod_pkg_key";
    public static final String FIELD_PARENT_PROD_PKG_KEY = "parent_prod_pkg_key";
    public static final String FIELD_ROOT_PROD_PKG_KEY = "root_prod_pkg_key";

    public static final String  FIELD_OWNER = "owner";
    public static final String  FIELD_DATA_OWN_DEPARTMENT = "data_own_department";
    public static final String  FIELD_PRINT_HIERARCHY = "print_hierarchy";
    public static final String  FIELD_MIN_AMOUNT = "min_amount";
    public static final String  FIELD_MAX_AMOUNT = "max_amount";
    public static final String  FIELD_ORDER_FIELD = "order_field";
    public static final String  FIELD_PRICE_BOOK_ID = "price_book_id";
    public static final String  FIELD_MODIFIED_ADJUST_PRICE = "modified_adjust_price";
    public static final String  FIELD_PRODUCT_STATUS__V = "product_status__v";
    public static final String  FIELD_PRODUCT_STATUS = "product_status";
    public static final String  FIELD_MAX_PROD_COUNT = "max_prod_count";
    public static final String  FIELD_CORE_ID = "core_id";
    public static final String  FIELD_PARENT_BOM_NAME = "parent_bom_name";
    public static final String  FIELD_NODE_BOM_CORE_TYPE = "node_bom_core_type";
    public static final String  FIELD_RELATED_CORE_ID = "related_core_id";
    public static final String  FIELD_RELATED_CORE_PATH = "related_core_path";
    public static final String  FIELD_NODE_BOM_CORE_VERSION = "node_bom_core_version";
    public static final String  FIELD_SFA_RELATED_PATH_MARK = "sfa_related_path_mark";

    public static final String VIRTUAL_FIELD_NEW_BOM_PATH = "new_bom_path";
    public static final String VIRTUAL_FIELD_CURRENT_ROOT_NEW_PATH = "current_root_new_path";
    public static final String VIRTUAL_FIELD_NEW_BOM_ID = "new_bom_id";
    public static final String FIELD_BOM_CORE_ID = "bom_core_id";
    public static final String FIELD_NODE_NO = "node_no";
    public static final String FIELD_AMOUNT_ANY = "amount_any";
    public static final String FIELD_IS_PACKAGE = "is_package";

    public static final String VIRTUAL_FIELD_SELECTED_BY_QUOTER = "selected_by_quoter";

    public static final String VIRTUAL_FIELD_NEED_HANDLE_SELECTED = "need_handle_selected";
    public static final String VIRTUAL_FIELD_QUANTITY = "quantity";

    public static final String ROOT_BOM_QUANTITY = "root_bom_quantity";
    public static final String ROOT_BOM_QUANTITY_PLACES = "root_bom_quantity_places";

    public static final String MIN_AMOUNT_COUNT = "min_amount_count";
    public static final String MAX_AMOUNT_COUNT = "max_amount_count";

    public static final String PRICING_PERIOD = "pricing_period";

    public static final String BOM_NODE_LEVEL = "bom_node_level";

    private BomConstants(){

    }


    public static final List<String>  FILTER_THREE_FIELD = Lists.newArrayList(FIELD_ROOT_ID, FIELD_PARENT_BOM_ID, FIELD_BOM_PATH);

    public static final List<String>  FILTER_FIELD = Lists.newArrayList(IObjectData.ID,FIELD_ROOT_ID, FIELD_PARENT_BOM_ID, FIELD_BOM_PATH);

    public static final Set<String> DESCRIBE_FILTER_LAYOUT_FIELD = Sets.newHashSet(FIELD_OWNER, FIELD_DATA_OWN_DEPARTMENT);

    public static final Set<String> ORDER_QUOTE_REMOVE_BOM_FIELDS =  Sets.newHashSet(FIELD_PRINT_HIERARCHY);
    public static final Set<String> ORDER_QUOTE_READONLY_BOM_FIELDS =  Sets.newHashSet(FIELD_PROD_PKG_KEY, FIELD_ROOT_PROD_PKG_KEY, FIELD_PARENT_PROD_PKG_KEY);

    public static final List<String> SYNC_BOM_FIELD_FOR_UPDATE_IMPORT = Lists.newArrayList(FIELD_INCREMENT, FIELD_PRICE_EDITABLE, FIELD_ADJUST_PRICE,
                                                                                           FIELD_IS_REQUIRED, FIELD_PRICE_MODE, FIELD_AMOUNT, FIELD_BOM_PATH, FIELD_ROOT_ID, FIELD_PARENT_BOM_ID, FIELD_MIN_AMOUNT, FIELD_PRODUCT_ID, SHARE_RATE,
                                                                                           FIELD_ENABLED_STATUS, FIELD_ORDER_FIELD, FIELD_AMOUNT_EDITABLE, FIELD_PRODUCT_GROUP_ID, FIELD_MAX_AMOUNT, FIELD_SELECTED_BY_DEFAULT,FIELD_CORE_ID,FIELD_RELATED_CORE_ID,FIELD_NODE_BOM_CORE_TYPE,FIELD_NODE_BOM_CORE_VERSION,FIELD_AMOUNT_ANY,FIELD_IS_PACKAGE);

    public static final List<String> REMOVE_FIELD_FOR_UPDATE_IMPORT = Lists.newArrayList(FIELD_PARENT_BOM_ID, FIELD_ROOT_ID, FIELD_BOM_PATH,FIELD_NODE_BOM_CORE_TYPE,FIELD_NODE_BOM_CORE_VERSION);

    public static final List<String> FILTER_QUERY_FIELD = Lists.newArrayList(FIELD_CORE_ID,FIELD_PRODUCT_ID,FIELD_RELATED_CORE_ID,FIELD_NODE_BOM_CORE_TYPE,"object_describe_api_name");

    public static final List<String> FILTER_READ_ONlY_FIELD = Lists.newArrayList("bom_instance_tree_id","bom_id","temp_node_group_id","node_no","temp_node_bom_id","bom_core_id","related_core_id","new_bom_path");

    public static final List<String> REMOVE_FIELD_FOR_BOM_CORE_UPDATE_IMPORT = Lists.newArrayList(FIELD_PRODUCT_ID, "category","core_version",BomCoreConstants.SALE_STRATEGY);

}