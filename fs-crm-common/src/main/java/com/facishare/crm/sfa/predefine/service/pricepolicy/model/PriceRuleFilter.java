package com.facishare.crm.sfa.predefine.service.pricepolicy.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.impl.search.Filter;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PriceRuleFilter extends Filter {
    @JSONField(name = "field_name_type")
    @JsonProperty("field_name_type")
    private String fieldNameType;

    @JSONField(name = "object_api_name")
    @JsonProperty("object_api_name")
    private String objectApiName;

    @JSONField(name = "field_name__s")
    @JsonProperty("field_name__s")
    private String fieldNameS;

    @JSONField(name = "object_api_name__s")
    @JsonProperty("object_api_name__s")
    private String objectApiNameS;

    @JSONField(name = "type")
    @JsonProperty("type")
    private String fieldType;

    @JSONField(name = "agg_value_type")
    @JsonProperty("agg_value_type")
    private String aggValueType;

    @JSONField(name = "filter_type")
    @JsonProperty("filter_type")
    private String filterType;

    @JSONField(name = "progressive_flag")
    @JsonProperty("progressive_flag")
    private Boolean progressiveFlag;

    /**
     * 右值支持变量
     * 变量 variable
     * 常量 空或
     */
    @JSONField(name = "right_value_type")
    @JsonProperty("right_value_type")
    private String rightValueType;

    /**
     * 右值类型：
     * 普通字段 field
     * 查找关联 object_reference
     */
    @JSONField(name = "right_field_value_type")
    @JsonProperty("right_field_value_type")
    private String rightFieldValueType;

    /**
     * 右值类型：事件/指标
     * 指标：metric 暂时只支持指标
     */
    @JSONField(name = "right_field_name_type")
    @JsonProperty("right_field_name_type")
    private String rightFieldNameType;
}