package com.facishare.crm.platform.signature.algorithm;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-19
 * ============================================================
 */
public interface SignAlgorithm {
    String name();

    String sign(String message, String secretKey) throws Exception;

    boolean verify(String message, String secretKey, String signature) throws Exception;
}
