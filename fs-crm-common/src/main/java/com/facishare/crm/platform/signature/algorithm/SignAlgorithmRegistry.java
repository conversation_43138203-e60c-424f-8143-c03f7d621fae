package com.facishare.crm.platform.signature.algorithm;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-19
 * ============================================================
 */
@Component
public class SignAlgorithmRegistry {
    private final Map<String, SignAlgorithm> registry = new ConcurrentHashMap<>();

    @Autowired
    public SignAlgorithmRegistry(List<SignAlgorithm> algorithms) {
        for (SignAlgorithm algo : algorithms) {
            registry.put(algo.name(), algo);
        }
    }

    public Optional<SignAlgorithm> get(String name) {
        return Optional.ofNullable(registry.get(name));
    }
}
