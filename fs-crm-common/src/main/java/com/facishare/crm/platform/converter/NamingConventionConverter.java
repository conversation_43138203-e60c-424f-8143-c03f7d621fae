package com.facishare.crm.platform.converter;

/**
 * Created by Sundy on 2024/10/9 20:19
 */
public class NamingConventionConverter {
    public static String camelToSnake(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return camelCase;
        }
        StringBuilder snakeCase = new StringBuilder();
        for (int i = 0; i < camelCase.length(); i++) {
            char currentChar = camelCase.charAt(i);
            // 如果是大写字母并且不是第一个字符，添加下划线
            if (Character.isUpperCase(currentChar) && i > 0) {
                snakeCase.append('_');
            }
            snakeCase.append(Character.toLowerCase(currentChar)); // 添加小写字母
        }
        return snakeCase.toString();
    }
    public static String capitalize(String fieldName) {
        if (fieldName == null || fieldName.isEmpty()) {
            return fieldName;
        }
        return fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
    }

    public static void main(String[] args) {
        System.out.println(capitalize("Hello"));
        System.out.println(capitalize("HelloWorld"));
        System.out.println(capitalize("helloWorld"));
        System.out.println(capitalize("hello"));
    }
}
