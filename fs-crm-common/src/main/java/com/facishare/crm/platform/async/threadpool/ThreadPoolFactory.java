package com.facishare.crm.platform.async.threadpool;

import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-16
 * ============================================================
 */
@Component
public class ThreadPoolFactory {
    private final Map<String, ThreadPoolExecutor> poolMap = new ConcurrentHashMap<>();

    public ThreadPoolExecutor createPool(String name, double cpuFactor, int queueCapacity) {
        int cpuCores = Runtime.getRuntime().availableProcessors();
        int poolSize = Math.max(2, (int) (cpuCores * cpuFactor));

        ThreadFactory threadFactory = new NamedThreadFactory(name);
        BlockingQueue<Runnable> queue = new LinkedBlockingQueue<>(queueCapacity);

        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                poolSize,
                poolSize * 2,
                60, TimeUnit.SECONDS,
                queue,
                threadFactory,
                new ThreadPoolExecutor.AbortPolicy()
        );

        poolMap.put(name, executor);
        return executor;
    }

    public ThreadPoolExecutor getPool(String name) {
        return poolMap.get(name);
    }

    public Map<String, ThreadPoolExecutor> getAllPools() {
        return poolMap;
    }

    public ThreadPoolStats getPoolStats(String name) {
        ThreadPoolExecutor executor = poolMap.get(name);
        if (executor == null) return null;

        return new ThreadPoolStats(
                name,
                executor.getPoolSize(),
                executor.getActiveCount(),
                executor.getQueue().size(),
                executor.getCompletedTaskCount(),
                executor.getCorePoolSize(),
                executor.getMaximumPoolSize()
        );
    }

    public void updatePoolSize(String name, int coreSize, int maxSize) {
        ThreadPoolExecutor executor = poolMap.get(name);
        if (executor != null) {
            executor.setCorePoolSize(coreSize);
            executor.setMaximumPoolSize(maxSize);
        }
    }

    @PreDestroy
    public void shutdownAll() {
        for (ThreadPoolExecutor executor : poolMap.values()) {
            executor.shutdown();
        }
    }

    private static class NamedThreadFactory implements ThreadFactory {
        private final String prefix;
        private final AtomicInteger count = new AtomicInteger(1);

        public NamedThreadFactory(String prefix) {
            this.prefix = prefix;
        }

        @Override
        public Thread newThread(Runnable r) {
            return new Thread(r, prefix + "-thread-" + count.getAndIncrement());
        }
    }

    public static class ThreadPoolStats {
        public final String name;
        public final int poolSize;
        public final int activeCount;
        public final int queueSize;
        public final long completedTaskCount;
        public final int corePoolSize;
        public final int maxPoolSize;

        public ThreadPoolStats(String name, int poolSize, int activeCount, int queueSize,
                               long completedTaskCount, int corePoolSize, int maxPoolSize) {
            this.name = name;
            this.poolSize = poolSize;
            this.activeCount = activeCount;
            this.queueSize = queueSize;
            this.completedTaskCount = completedTaskCount;
            this.corePoolSize = corePoolSize;
            this.maxPoolSize = maxPoolSize;
        }

        @Override
        public String toString() {
            return String.format(
                    "[%s] poolSize=%d, active=%d, queue=%d, done=%d, core=%d, max=%d",
                    name, poolSize, activeCount, queueSize, completedTaskCount, corePoolSize, maxPoolSize
            );
        }
    }
}
