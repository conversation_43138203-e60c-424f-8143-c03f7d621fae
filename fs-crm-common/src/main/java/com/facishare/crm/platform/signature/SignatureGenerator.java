package com.facishare.crm.platform.signature;

import com.facishare.crm.platform.signature.algorithm.SignAlgorithm;
import com.facishare.crm.platform.signature.algorithm.SignAlgorithmRegistry;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-19
 * ============================================================
 */
@Service
public class SignatureGenerator {
    @Resource
    private SignAlgorithmRegistry signAlgorithmRegistry;

    public FluentSignBuilder algorithm(String algorithm) {
        return new FluentSignBuilder(signAlgorithmRegistry.get(algorithm)
                .orElseThrow(() -> new IllegalArgumentException("Unsupported algorithm: " + algorithm)));
    }

    public static class FluentSignBuilder {
        private final SignAlgorithm algorithm;
        private String message;
        private String secret;
        private String signatureToVerify;

        public FluentSignBuilder(SignAlgorithm algorithm) {
            this.algorithm = algorithm;
        }

        public FluentSignBuilder message(String message) {
            this.message = message;
            return this;
        }

        public FluentSignBuilder secret(String secret) {
            this.secret = secret;
            return this;
        }

        public FluentSignBuilder signature(String signature) {
            this.signatureToVerify = signature;
            return this;
        }

        public String sign() {
            if (message == null || secret == null) throw new IllegalStateException("message or secret missing");
            try {
                return algorithm.sign(message, secret);
            } catch (Exception e) {
                throw new RuntimeException("sign error", e);
            }
        }

        public boolean verify() {
            if (message == null || secret == null || signatureToVerify == null)
                throw new IllegalStateException("message, secret or signature missing");
            try {
                return algorithm.verify(message, secret, signatureToVerify);
            } catch (Exception e) {
                throw new RuntimeException("verify error", e);
            }
        }
    }
}
