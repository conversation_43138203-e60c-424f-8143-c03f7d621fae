package com.facishare.crm.platform.async;

import com.facishare.crm.platform.async.threadpool.ThreadPoolFactory;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-16
 * ============================================================
 */
@Component
@ServiceModule("thread-pools")
public class ThreadPoolMonitorController {
    @Resource
    private ThreadPoolFactory threadPoolFactory;

    /**
     * 获取所有线程池状态
     */
    @ServiceMethod("list")
    public List<ThreadPoolFactory.ThreadPoolStats> listAll(ServiceContext serviceContext) {
        return threadPoolFactory.getAllPools().keySet().stream()
                .map(threadPoolFactory::getPoolStats)
                .collect(Collectors.toList());
    }

    /**
     * 获取指定线程池状态
     */
    @ServiceMethod("query")
    public ThreadPoolFactory.ThreadPoolStats get(ServiceContext serviceContext, String name) {
        ThreadPoolFactory.ThreadPoolStats stats = threadPoolFactory.getPoolStats(name);
        if (stats == null) {
            throw new IllegalArgumentException("No such thread pool: " + name);
        }
        return stats;
    }
}
