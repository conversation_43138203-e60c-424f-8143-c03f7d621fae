package com.facishare.crm.platform.async.executor;

import com.facishare.crm.platform.async.TaskCallback;
import com.facishare.crm.platform.async.TaskResult;
import com.facishare.crm.platform.async.threadpool.ThreadPoolFactory;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_GET_WARN_INFO;
import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.ASYNC_CHECK_FAILED;
import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_CATEGORY_ASYNC_TIMEOUT;

/**
 * <AUTHOR>
 * @date 2021/11/30 11:23
 */
@Slf4j
@Service
public class AsyncBootstrap {

    private final ThreadPoolExecutor renderPool;

    public AsyncBootstrap(ThreadPoolFactory factory) {
        this.renderPool = factory.createPool("async-bootstrap", 1.5, 300);
    }

    /**
     * 任一任务抛异常则完成
     *
     * @param futures 任务
     * @return CompletableFuture
     */
    public CompletableFuture<?> composed(CompletableFuture<?>... futures) {
        CompletableFuture<?> allComplete = CompletableFuture.allOf(futures);
        CompletableFuture<?> anyException = new CompletableFuture<>();
        for (CompletableFuture<?> completableFuture : futures) {
            completableFuture.exceptionally((t) -> {
                anyException.completeExceptionally(t);
                return null;
            });
        }
        return CompletableFuture.anyOf(allComplete, anyException);
    }

    public void composed(User user, CompletableFuture<?>... futures) {
        composed(user, 10L, futures);
    }

    public void composed(User user, long timeout, CompletableFuture<?>... futures) {
        if (futures == null || futures.length == 0) {
            throw new NullPointerException("futures is null");
        }
        AtomicReference<String> error = new AtomicReference<>("");
        AtomicReference<Throwable> finalThrowable = new AtomicReference<>();
        try {
            composed(futures).exceptionally(throwable -> {
                if (throwable != null) {
                    finalThrowable.set(throwable);
                    error.set(throwable.getMessage());
                }
                return null;
            }).get(timeout, TimeUnit.SECONDS);
        } catch (InterruptedException ie) {
            // Restore the interrupted status
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("composed maybe timeout, tenant:{}", user.getTenantId(), e);
            throw new ValidateException(I18N.text(SO_CATEGORY_ASYNC_TIMEOUT));
        }
        String errorMsg = error.get();
        if (StringUtils.isBlank(errorMsg)) {
            return;
        }
        String newErrorMsg = formattingErrorMsg(errorMsg);
        if (newErrorMsg == null) {
            log.error("is not validate exception, tenant:{}", user.getTenantId(), finalThrowable.get());
            throw new ValidateException(I18N.text(ASYNC_CHECK_FAILED));
        }
        log.info("getComposedValidateMessage#validateException failed, tenant:{}", user.getTenantId(), finalThrowable.get());
        throw new ValidateException(newErrorMsg);
    }

    private String formattingErrorMsg(@NotNull String errorMsg) {
        boolean objectNotFind = errorMsg.contains("ObjectDataNotFoundException: ");
        boolean contains = errorMsg.contains("ValidateException: ");
        if (!contains && !objectNotFind) {
            return null;
        }
        String substring = errorMsg.substring(errorMsg.lastIndexOf(":") + 1);
        String trim = substring.trim();
        if (StringUtils.isBlank(trim)) {
            trim = I18N.text(SFA_GET_WARN_INFO);
        }
        return trim;
    }

    /**
     * 该方法封装了设置 context 的逻辑，保证 traceId 全局一致
     *
     * @param runnable 任务
     */
    public static void runAsyncTask(Runnable runnable) {
        RequestContext context = RequestContextManager.getContext();
        try {
            // 设置线程内 context，保证 traceId 全局一致
            RequestContextManager.setContext(context);
            ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
            task.submit(runnable);
            task.run();
        } catch (Exception ex) {
            log.error("runAsyncTask error, tenant:{}", context.getTenantId(), ex);
        }
    }

    public void submitTasks(
            Map<String, Supplier<Object>> tasks,
            long timeoutMillis,
            TaskCallback callback
    ) throws TimeoutException, InterruptedException, ExecutionException {

        Map<String, CompletableFuture<Object>> futures = new HashMap<>();

        for (Map.Entry<String, Supplier<Object>> entry : tasks.entrySet()) {
            futures.put(entry.getKey(), CompletableFuture.supplyAsync(entry.getValue(), renderPool));
        }

        CompletableFuture<Void> all = CompletableFuture.allOf(futures.values().toArray(new CompletableFuture[0]));

        try {
            all.get(timeoutMillis, TimeUnit.MILLISECONDS);

            TaskResult result = new TaskResult();
            for (Map.Entry<String, CompletableFuture<Object>> entry : futures.entrySet()) {
                result.put(entry.getKey(), entry.getValue().get());
            }

            callback.onSuccess(result);
        } catch (TimeoutException e) {
            // 尽量取消所有任务
            futures.values().forEach(f -> f.cancel(true));
            throw new TimeoutException("任务超时未完成");
        }
    }

}