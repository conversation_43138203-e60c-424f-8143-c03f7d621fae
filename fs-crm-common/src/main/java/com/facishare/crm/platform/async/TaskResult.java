package com.facishare.crm.platform.async;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-17
 * ============================================================
 */
public class TaskResult {
    private final Map<String, Object> resultMap = new HashMap<>();

    public void put(String key, Object value) {
        resultMap.put(key, value);
    }

    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> clazz) {
        return (T) resultMap.get(key);
    }

    public Map<String, Object> getAll() {
        return Collections.unmodifiableMap(resultMap);
    }
}
