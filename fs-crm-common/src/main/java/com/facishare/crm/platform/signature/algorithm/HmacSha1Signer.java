package com.facishare.crm.platform.signature.algorithm;

import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-19
 * ============================================================
 */
@Component
public class HmacSha1Signer implements SignAlgorithm {
    private static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";

    @Override
    public String name() {
        return HMAC_SHA1_ALGORITHM;
    }

    @Override
    public String sign(String message, String secretKey) throws Exception {
        Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
        mac.init(new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), HMAC_SHA1_ALGORITHM));
        byte[] rawHmac = mac.doFinal(message.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(rawHmac);
    }

    @Override
    public boolean verify(String message, String secretKey, String signature) throws Exception {
        return sign(message, secretKey).equals(signature);
    }
}
