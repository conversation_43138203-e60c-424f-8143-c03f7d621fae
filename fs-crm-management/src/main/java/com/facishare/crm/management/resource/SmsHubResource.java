package com.facishare.crm.management.resource;

import com.facishare.crm.enums.SmsTemplateStatusEnums;
import com.facishare.crm.management.proxy.model.SmsHubView;
import com.facishare.crm.platform.converter.Converter;
import com.facishare.crm.sfa.predefine.service.SmsHubService;
import com.facishare.crm.sfa.prm.platform.model.ApiResponse;
import com.facishare.crm.sfa.utilities.proxy.model.SmsHubModel;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.List;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

@Slf4j
@Component
@ServiceModule("sms_hub")
public class SmsHubResource {
    @Resource
    private SmsHubService smsHubService;
    @Resource(name = "objectConverter")
    private Converter converter;

    @ServiceMethod("query_sms_status")
    public ApiResponse<SmsHubView.SmsStatus> querySmsStatus(ServiceContext serviceContext) {
        SmsHubModel.SmsStatus smsStatus = smsHubService.checkSmsStatus(serviceContext.getUser());
        if (smsStatus == null) {
            throw new ValidateException(I18N.text(SFA_SMS_SERVICE_ERROR));
        }
        SmsHubView.SmsStatus smsStatusView = converter.convertDTO(smsStatus, SmsHubView.SmsStatus.class);
        smsStatusView.setOpen(smsStatus.getStatus() == 0);
        return ApiResponse.success(smsStatusView);
    }

    @ServiceMethod("query_templates")
    public ApiResponse<List<SmsHubView.SmsTemplate>> queryTemplates(ServiceContext serviceContext) {
        List<SmsHubModel.SmsTemplate> smsTemplates = smsHubService.querySmsTemplates(serviceContext.getUser());
        List<SmsHubView.SmsTemplate> smsTemplatesList = converter.convertDTOList(smsTemplates, SmsHubView.SmsTemplate.class);
        return ApiResponse.success(smsTemplatesList);
    }

    @ServiceMethod("query_sms_providers")
    public ApiResponse<List<SmsHubView.SmsProvider>> querySmsProviders(ServiceContext serviceContext) {
        List<SmsHubModel.SmsProvider> smsProviders = smsHubService.listSmsProvider(serviceContext.getUser());
        List<SmsHubView.SmsProvider> smsProvidersList = converter.convertDTOList(smsProviders, SmsHubView.SmsProvider.class);
        return ApiResponse.success(smsProvidersList);
    }

    @ServiceMethod("query_template_detail")
    public ApiResponse<SmsHubView.SmsTemplateDetail> queryTemplateDetail(ServiceContext serviceContext, SmsHubView.TemplateDetailArg arg) {
        if (StringUtils.isBlank(arg.getTemplateId())) {
            throw new ValidateException(I18N.text(SFA_SMS_TEMPLATE_DETAIL_NOT_FOUND));
        }
        SmsHubModel.SmsTemplateDetail smsTemplateDetail = smsHubService.querySmsTemplateDetail(serviceContext.getUser(), arg.getTemplateId());
        if (smsTemplateDetail == null) {
            throw new ValidateException(I18N.text(SFA_SMS_TEMPLATE_DETAIL_NOT_FOUND));
        }
        SmsTemplateStatusEnums smsTemplateStatus = SmsTemplateStatusEnums.of(smsTemplateDetail.getStatus());
        if (smsTemplateStatus == null) {
            throw new ValidateException(I18N.text(SFA_SMS_TEMPLATE_STATUS_EMPTY));
        }
        if (smsTemplateStatus != SmsTemplateStatusEnums.APPROVED) {
            throw new ValidateException(I18N.text(SFA_SMS_TEMPLATE_STATUS_AUDITING, smsTemplateStatus.getStatusLabel()));
        }
        SmsHubView.SmsTemplateDetail smsTemplateDetailView = converter.convertDTO(smsTemplateDetail, SmsHubView.SmsTemplateDetail.class);
        return ApiResponse.success(smsTemplateDetailView);
    }
}
