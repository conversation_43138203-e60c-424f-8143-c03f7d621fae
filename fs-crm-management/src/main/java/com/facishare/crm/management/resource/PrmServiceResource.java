package com.facishare.crm.management.resource;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.management.model.dto.PrmRequest;
import com.facishare.crm.management.service.OpenPartnerSubsidiaryConfigProvider;
import com.facishare.crm.management.service.PrmServiceProvider;
import com.facishare.crm.management.service.config.DescUtil;
import com.facishare.crm.management.service.config.OpenPartnerAddressConfigProvider;
import com.facishare.crm.management.service.config.OpenPartnerBizConfigProvider;
import com.facishare.crm.management.utils.PrmUtils;
import com.facishare.crm.sfa.prm.platform.model.ApiResponse;
import com.facishare.crm.model.BatchAddObjectToPRMModel;
import com.facishare.crm.rest.dto.PrmEnterpriseModel;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.PartnerInitService;
import com.facishare.crm.sfa.predefine.service.SFALicenseService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.service.IPrmService;
import com.facishare.crm.rest.InitObjectsPermissionsAndLayoutProxy;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.privilege.dto.SupportEnterpriseRelationResult;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.fxiaoke.enterpriserelation2.arg.BatchAddEaAssociationObjectsArg;
import com.fxiaoke.enterpriserelation2.arg.ListAppAssociationObjectsArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.service.AppFuncationPermissionService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * @description:
 * @author: guom
 * @date: Created in 2020/7/17 16:15
 */
@Slf4j
@Component
@ServiceModule("prm_service")
public class PrmServiceResource implements IPrmService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private DescUtil descUtil;
    @Autowired
    private PartnerInitService partnerInitService;
    @Autowired
    private AppFuncationPermissionService appFuncationPermissionService;
    @Autowired
    private BizConfigThreadLocalCacheService configCacheService;
    @Autowired
    private SFALicenseService sfaLicenseService;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private InitObjectsPermissionsAndLayoutProxy initObjectsPermissionsAndLayoutProxy;
    @Autowired
    private OpenPartnerBizConfigProvider openPartnerBizConfigProvider;
    @Autowired
    private OpenPartnerAddressConfigProvider openPartnerAddressConfigProvider;
    @Autowired
    private OpenPartnerSubsidiaryConfigProvider openPartnerSubsidiaryConfigProvider;
    @Resource
    private EnterpriseInitService enterpriseInitService;
    @Resource
    private PrmServiceProvider prmServiceProvider;

    @Override
    @ServiceMethod("batch_add_object_to_prm")
    public Boolean batchAddObjectToPRM(ServiceContext serviceContext, BatchAddObjectToPRMModel.Arg arg) {
        if (CollectionUtils.isEmpty(arg.getApiNames())) {
            log.warn("Params is empty, arg={}", arg);
            return Boolean.FALSE;
        }
        if (!configCacheService.isPartnerEnabled(serviceContext.getTenantId())) {
            return Boolean.FALSE;
        }
        boolean simplifiedChinese = true;
        RequestContext requestContext = RequestContextManager.getContext();
        if (!Objects.isNull(requestContext) && !Objects.isNull(requestContext.getLang())) {
            Lang lang = requestContext.getLang();
            simplifiedChinese = lang.equals(Lang.zh_CN);
        }

        // 获取对象描述
        Map<String, IObjectDescribe> describeMap;
        if (simplifiedChinese) {
            describeMap = serviceFacade.findObjects(serviceContext.getUser().getTenantId(), arg.getApiNames());
        } else {
            IActionContext actionContext = ActionContextExt.of(new User(serviceContext.getTenantId(), User.SUPPER_ADMIN_USER_ID)).getContext();
            actionContext.setLang(Lang.zh_CN.getValue());
            describeMap = Maps.newHashMap();
            try {
                List<IObjectDescribe> objectDescribeList = objectDescribeService.findDescribeListByApiNames(serviceContext.getTenantId(), arg.getApiNames(), actionContext);
                if (CollectionUtils.isNotEmpty(objectDescribeList)) {
                    objectDescribeList.forEach(item -> describeMap.put(item.getApiName(), item));
                }
            } catch (MetadataServiceException e) {
                log.warn("find describe error, tenantId={}, apiName={}", serviceContext.getTenantId(), arg.getApiNames());
            }
        }

        if (MapUtils.isEmpty(describeMap)) {
            log.warn("Fail to Found describe, apinames={}", arg.getApiNames());
            return Boolean.FALSE;
        }
        // 刷字段
        List<String> toAddFuncNames = Lists.newArrayList();
        SupportEnterpriseRelationResult supportEnterpriseRelation = infraServiceFacade.isSupportEnterpriseRelation(serviceContext.getTenantId());
        describeMap.forEach((k, v) -> {
            if (!ObjectDescribeExt.of(v).isSlaveObject()) {
                descUtil.changeDescribeAndLayout(serviceContext.getUser(), describeMap, k, "partner_id", "合作伙伴", supportEnterpriseRelation.isSupportInterconnectBaseAppLicense());//ignoreI18n
                toAddFuncNames.add(k);
            }
        });
        log.info("partner_init changeDescribe success, toAddFuncNames={}", toAddFuncNames);
        // 给对象刷功能权限，更换合作伙伴、移除合作伙伴、更换外部负责人
        String result = partnerInitService.batchInitPartnerRelateAction(serviceContext.getUser(), toAddFuncNames);
        log.info("batchInitPartnerRelateAction success, result={}", result);

        List<String> addPrmMenuObjects = toAddFuncNames.stream().filter(PrmUtils.prmAppObjects::contains).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(addPrmMenuObjects)) {
            return Boolean.TRUE;
        }
        BatchAddObjectToPRMModel.Arg prmArg = new BatchAddObjectToPRMModel.Arg();
        prmArg.setApiNames(Lists.newArrayList(addPrmMenuObjects));
        prmArg.setAllowRemove(false);
        addObjectRest(serviceContext.getTenantId(), prmArg);
        return Boolean.TRUE;
    }

    @ServiceMethod("add_objects")
    public Boolean addObjects(ServiceContext serviceContext, BatchAddObjectToPRMModel.Arg arg) {
        addObjectRest(serviceContext.getTenantId(), arg);
        return true;
    }

    public boolean addObjectRest(String tenantId, BatchAddObjectToPRMModel.Arg arg) {
        try {
            boolean licenseExist = sfaLicenseService.checkModuleLicenseExist(tenantId, "prm_app");
            if (!licenseExist) {
                return true;
            }
            BatchAddEaAssociationObjectsArg objectsArg = new BatchAddEaAssociationObjectsArg();
            String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
            objectsArg.setEnterpriseAccount(ea);
            objectsArg.setAllowRemove(arg.getAllowRemove());
            objectsArg.setAppId(AppIdMapping.appIdMapping.get("prm"));
            objectsArg.setObjectApiNames(arg.getApiNames());
            HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(tenantId));
            appFuncationPermissionService.batchAddEaAssociationObjects(headerObj, objectsArg);
        } catch (Exception e) {
            log.error("add prm app objects error, tenant{}", tenantId, e);
            return false;
        }
        return true;
    }

    @Override
    @ServiceMethod("prm_open_add_objects")
    public Boolean prmOpenAddObjects(String tenantId) {
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(tenantId, PrmUtils.prmAppObjects);
        List<String> apiNames = PrmUtils.prmAppObjects.stream().filter(o -> describeMap.get(o) != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(apiNames)) {
            return false;
        }
        BatchAddObjectToPRMModel.Arg arg = new BatchAddObjectToPRMModel.Arg();
        arg.setAllowRemove(false);
        arg.setApiNames(apiNames);
        addObjectRest(tenantId, arg);
        return true;
    }

    @Override
    @ServiceMethod("brushEnterpriseRelation")
    public Boolean brushEnterpriseRelation(ServiceContext serviceContext) throws MetadataServiceException {
        String tenantId = serviceContext.getTenantId();
        SupportEnterpriseRelationResult supportEnterpriseRelation = infraServiceFacade.isSupportEnterpriseRelation(tenantId);
        if (!supportEnterpriseRelation.isSupportInterconnectBaseAppLicense()) {
            return false;
        }
        List<String> predefinedApiNames = PrmConstant.menuApiNames;
        List<IObjectDescribe> describeList = Lists.newArrayList();
        List<List<String>> partitionList = Lists.partition(predefinedApiNames, 6);
        for (List<String> list : partitionList) {
            List<IObjectDescribe> tempList = objectDescribeService.findDescribeListByApiNames(tenantId, list);
            describeList.addAll(tempList);
        }
        for (IObjectDescribe describe : describeList) {
            List<IFieldDescribe> fieldDescribes = describe.getFieldDescribes();
            List<IFieldDescribe> referenceAccountFieldDescribeList = fieldDescribes.stream()
                    .filter(x -> "object_reference".equals(x.getType()) && "AccountObj".equals(x.get("target_api_name")))
                    .collect(Collectors.toList());

            Optional<IFieldDescribe> accountAny = referenceAccountFieldDescribeList.stream()
                    .filter(x -> "out_owner".equals(x.get("relation_outer_data_privilege")))
                    .findAny();
            if (accountAny.isPresent()) {
                continue;
            }
            List<IFieldDescribe> referencePartnerFieldDescribeList = fieldDescribes.stream()
                    .filter(x -> "object_reference".equals(x.getType()) && "PartnerObj".equals(x.get("target_api_name")))
                    .collect(Collectors.toList());
            // 已经被初始化一次了
            Optional<IFieldDescribe> partnerAnyNotEmpty = referencePartnerFieldDescribeList.stream().filter(x -> x.get("relation_outer_data_privilege") != null).findAny();
            if (partnerAnyNotEmpty.isPresent()) {
                continue;
            }
            Optional<IFieldDescribe> partnerAny = referencePartnerFieldDescribeList.stream()
                    .filter(x -> "out_owner".equals(x.get("relation_outer_data_privilege")))
                    .findAny();
            if (partnerAny.isPresent()) {
                continue;
            }
            IFieldDescribe fieldDescribe = describe.getFieldDescribe("partner_id");
            if (fieldDescribe == null) {
                continue;
            }
            try {
                updateEnterpriseRelationField(fieldDescribe, describe);
            } catch (Exception e) {
                log.warn("updateEnterpriseRelationField error, apiName:{}, tenant:{}", describe.getApiName(), tenantId, e);
            }
        }
        return true;
    }

    private void updateEnterpriseRelationField(IFieldDescribe fieldDescribe, IObjectDescribe describe) throws MetadataServiceException {
        boolean update = false;
        String value = "outer_owner";
        String key = "relation_outer_data_privilege";
        Object outerDataPrivilege = fieldDescribe.get(key);
        if (outerDataPrivilege != null && !value.equals(outerDataPrivilege)) {
            fieldDescribe.set(key, value);
            update = true;
        } else if (outerDataPrivilege == null) {
            fieldDescribe.set(key, value);
            update = true;
        }
        if (update) {
            log.info("updateEnterpriseRelationField, apiName:{}, tenant:{}", describe.getApiName(), describe.getTenantId());
            objectDescribeService.updateFieldDescribe(describe, Lists.newArrayList(fieldDescribe));
        }
    }

    @ServiceMethod("fix_contact_prm_layout")
    public List<String> updateDescribeLayout(List<String> tenants) {
        List<String> failed = Lists.newArrayList();
        for (String tenant : tenants) {
            try {
                User user = new User(tenant, "-10000");
                partnerInitService.createPrmContactLayout(user);
            } catch (Exception e) {
                log.warn("fix_contact_prm_layout failed", e);
                failed.add(tenant);
            }
        }
        log.warn("fix_contact_prm_layout finished, failed tenant:{}", failed);
        return failed;
    }

    @ServiceMethod("fix_contact_prm_record_type")
    public List<String> addRecord(List<String> tenants) {
        List<String> failed = Lists.newArrayList();
        for (String tenant : tenants) {
            try {
                User user = new User(tenant, "-10000");
                partnerInitService.createPrmContactRecordType(user);
            } catch (Exception e) {
                log.warn("fix_contact_prm_record_type failed", e);
                failed.add(tenant);
            }
        }
        log.warn("fix_contact_prm_record_type finished, failed tenant:{}", failed);
        return failed;
    }

    public List<String> getPrmObjects(String tenantId) {
        ListAppAssociationObjectsArg arg = new ListAppAssociationObjectsArg();
        arg.setAppId(AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID));
        arg.setLinkType(1);
        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        arg.setUpstreamEa(ea);
        Map<String, String> header = getHeader(tenantId);
        PrmEnterpriseModel.AppAssociationObjectResult prmRestResult = initObjectsPermissionsAndLayoutProxy
                .listAppAssociationObjects(header, arg);
        if (CollectionUtils.isNotEmpty(prmRestResult.getData())) {
            return prmRestResult.getData().stream()
                    .map(PrmEnterpriseModel.AppAssociationObject::getObjectApiName).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    private Map<String, String> getHeader(String tenantId) {
        Map<String, String> header = Maps.newHashMap();
        header.put("Content-Type", "application/json");
        header.put("x-fs-ei", tenantId);
        header.put("x-eip-appid", "x_app_framework");
        return header;
    }

    @ServiceMethod("add_predefined_object_to_prm_app")
    public Boolean addPredefinedObjectToPrmApp(ServiceContext context) {
        List<String> existsPrmObjects = getPrmObjects(context.getTenantId());
        List<String> needAddPrmAppObjects = Lists.newArrayList();
        for (String object : PrmUtils.prmAppObjects) {
            if (existsPrmObjects.contains(object)) {
                continue;
            }
            needAddPrmAppObjects.add(object);
        }
        List<IObjectDescribe> objectList = serviceFacade.findObjectList(context.getTenantId(), needAddPrmAppObjects);
        needAddPrmAppObjects = objectList.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needAddPrmAppObjects)) {
            return Boolean.TRUE;
        }
        BatchAddObjectToPRMModel.Arg arg = new BatchAddObjectToPRMModel.Arg();
        arg.setApiNames(needAddPrmAppObjects);
        return addObjectRest(context.getTenantId(), arg);
    }

    @ServiceMethod("open_partner")
    public Boolean openPartner(ServiceContext context, List<String> tenants) {
        for (String tenant : tenants) {
            User user = new User(tenant, "-10000");
            openPartnerBizConfigProvider.openPartner(user, "1", "", "37");
        }
        return Boolean.TRUE;
    }

    @ServiceMethod("open_partner_address")
    public Boolean openPartnerAddress(ServiceContext context, List<String> tenants) {
        for (String tenant : tenants) {
            User user = new User(tenant, "-10000");
            openPartnerAddressConfigProvider.openPartnerAddress(user, "1", "", "partner_address");
        }
        return Boolean.TRUE;
    }

    @ServiceMethod("open_partner_subsidiary")
    public Boolean openPartnerSubsidiary(ServiceContext context, List<String> tenants) {
        for (String tenant : tenants) {
            User user = new User(tenant, "-10000");
            openPartnerSubsidiaryConfigProvider.openPartnerSubsidiary(user);
        }
        return Boolean.TRUE;
    }

    @ServiceMethod("init_mapping")
    public Boolean initMapping(ServiceContext context, List<String> mappingNameList) {
        if (CollectionUtils.isEmpty(mappingNameList)) {
            return Boolean.FALSE;
        }
        for (String mappingName : mappingNameList) {
            enterpriseInitService.initObjectMappingRule(context, mappingName);
        }
        return Boolean.TRUE;
    }

    @ServiceMethod("add_options")
    public ApiResponse<Boolean> addOptions(ServiceContext context, PrmRequest.AddOptionArg arg) {
        try {
            prmServiceProvider.addOptions(context.getUser(), arg);
            return ApiResponse.success(Boolean.TRUE);
        } catch (MetadataServiceException | ValidateException e) {
            return ApiResponse.error(500, e.getMessage());
        } catch (Exception e) {
            log.warn("add_options error", e);
            return ApiResponse.error(500, "api error!");
        }
    }
}
