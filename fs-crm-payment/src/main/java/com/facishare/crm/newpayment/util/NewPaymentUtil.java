package com.facishare.crm.newpayment.util;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.constants.NewPaymentI18N;
import com.facishare.crm.newpayment.enums.MatchStatusEnum;
import com.facishare.crm.newpayment.enums.PaymentCollectionTypeEnum;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.mq.RocketMQMessageSender;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.describe.BooleanFieldDescribe;
import com.facishare.paas.metadata.impl.describe.FileAttachmentFieldDescribe;
import com.facishare.paas.metadata.impl.describe.NumberFieldDescribe;
import com.facishare.paas.metadata.impl.describe.OutEmployeeFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class NewPaymentUtil {
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    public static final int MAX_QUERY_SIZE = 1000000;

    public static void sendDhtMq(User user, String status, List<String> orderPaymentIds) {
        if (CollectionUtils.isEmpty(orderPaymentIds)) {
            return;
        }
        RocketMQMessageSender sender = SpringUtil.getContext().getBean("paymentDHTMQSender", RocketMQMessageSender.class);
        Map<String, Object> dhtMq = Maps.newHashMap();
        dhtMq.put("status", status);
        dhtMq.put("paymentOrderIds", orderPaymentIds);
        dhtMq.put("tenantId", user.getTenantId());
        try {
            sender.sendMessage(JSON.toJSONBytes(dhtMq));
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public static void checkPaymentInvalid(List<IObjectData> paymentDataList) {
        if (CollectionUtils.isEmpty(paymentDataList)) {
            return;
        }
        paymentDataList.forEach(x -> {
            BigDecimal matchAmount = x.get(NewPaymentConst.MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            String matchStatus = x.get(NewPaymentConst.MATCH_STATUS, String.class);
            if (MatchStatusEnum.matched(matchStatus) && matchAmount.compareTo(BigDecimal.ZERO) > 0) {
                throw new ValidateException(I18N.text(NewPaymentI18N.PAYMENT_ALREADY_MATCHED_CANNOT_INVALID, x.getName()));
            }
        });
    }

    public static void copyPaymentCustomFieldDataToOrderPayment(IObjectDescribe masterDescribe, ObjectDataDocument masterData, List<ObjectDataDocument> detailDataList) {
        if (CollectionUtils.isEmpty(detailDataList)) {
            return;
        }
        List<IFieldDescribe> fields = ObjectDescribeExt.of(masterDescribe).getFieldDescribesSilently()
                .stream()
                .filter(f -> "custom".equals(f.getDefineType()) && Boolean.TRUE.equals(f.isActive()))
                .collect(Collectors.toList());
        for (IFieldDescribe field : fields) {
            Object value = masterData.get(field.getApiName());
            if (null != value) {
                detailDataList.forEach(x -> x.putIfAbsent(field.getApiName(), value));
            }
        }
    }

    public static boolean isReceivableEnable(String configValue) {
        return "2".equals(configValue);
    }

    public static boolean isTrue(Map<String, String> configMap, String key, String compareValue) {
        String configValue = configMap.getOrDefault(key, "");
        if (Objects.isNull(configValue)) {
            return false;
        }
        return StringUtils.equals(configValue, compareValue);
    }

    public static boolean isSupportRedPayment(String tenantId, IObjectData paymentData) {
        if (!bizConfigThreadLocalCacheService.isOpenKxAutoMatch(tenantId)) {
            return false;
        }
        String collectionType = paymentData.get(NewPaymentConst.COLLECTION_TYPE, String.class);
        if (Strings.isNullOrEmpty(collectionType)) {
            return false;
        }
        return true;
    }

    public static void validatePaymentAmount(User user, IObjectData paymentData, List<IObjectData> orderPaymentList) {
        //主对象上的payment_amount传过来的值，可能有问题（自定义函数或者openApi），这里根据从对象数据重算
        BigDecimal paymentAmount = BigDecimal.ZERO;
        boolean supportRedPayment = NewPaymentUtil.isSupportRedPayment(user.getTenantId(), paymentData);
        boolean enablePaymentNegative = GrayUtil.grayPaymentAllowAmountLTZero(user.getTenantId());

        for (IObjectData orderPaymentData : com.facishare.paas.appframework.common.util.CollectionUtils.nullToEmpty(orderPaymentList)) {
            BigDecimal detailPaymentAmount = orderPaymentData.get(NewPaymentConst.PAYMENT_AMOUNT, BigDecimal.class);
            if (Objects.isNull(detailPaymentAmount)) {
                throw new ValidateException(I18N.text(NewPaymentI18N.SO_PAYMENT_NOTFINDORDERPAYYMENTMONEY));
            }
            if (!supportRedPayment && detailPaymentAmount.compareTo(BigDecimal.ZERO) < 0 && !enablePaymentNegative) {
                throw new ValidateException(I18N.text(NewPaymentI18N.SO_PAYMENT_NOTFINDORDERPAYYMENTMONEYGT0));
            }
            paymentAmount = paymentAmount.add(detailPaymentAmount);
        }
        if (supportRedPayment) {
            //红字回款，不在这里校验；不执行下面回款主对象字段的校验
            return;
        }

        BigDecimal amount = paymentData.get(NewPaymentConst.AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        //不允许回款为负数
        if (!enablePaymentNegative) {
            //无论是否有明细，amount都不能小于0；若无明细，amount不能等于0
            if (amount.compareTo(BigDecimal.ZERO) < 0 || (CollectionUtils.isEmpty(orderPaymentList) && amount.compareTo(BigDecimal.ZERO) <= 0)) {
                throw new ValidateException(I18N.text(NewPaymentI18N.PAYMENT_AMOUNT_MUST_GT_ZERO));
            }
            //回款明细总和不能小于0
            if (paymentAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new ValidateException(I18N.text(NewPaymentI18N.SO_PAYMENT_NOTFINDORDERPAYYMENTMONEYGT0));
            }
            //回款明细总和不能大于回款的amount
            if (paymentAmount.compareTo(amount) > 0) {
                throw new ValidateException(I18N.text(NewPaymentI18N.AMOUNT_MUST_GT_PAYMENT_AMOUNT));
            }
        } else {
            //允许为负数时
            if (!CollectionUtils.isEmpty(orderPaymentList) && paymentAmount.compareTo(amount) > 0) {
                throw new ValidateException(I18N.text(NewPaymentI18N.AMOUNT_MUST_GT_PAYMENT_AMOUNT));
            }
        }
        //paymentAmount与amount的值，必须同向：都>=0或都<=0。
        if (paymentAmount.compareTo(BigDecimal.ZERO) < 0 && amount.compareTo(BigDecimal.ZERO) > 0) {
            throw new ValidateException(I18N.text(NewPaymentI18N.AMOUNT_MUST_LT_ZERO_WHEN_USED_AMOUNT_LT_ZERO));
        }
    }

    public static void checkPaymentCollectionType(IObjectData objectData, List<IObjectData> detailList) {
        String collectionType = objectData.get(NewPaymentConst.COLLECTION_TYPE, String.class);
        if (!Strings.isNullOrEmpty(collectionType)) {
            BigDecimal amount = objectData.get(NewPaymentConst.AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal totalDetailPaymentAmount = BigDecimal.ZERO;
            if (collectionType.equals(PaymentCollectionTypeEnum.Red.value)
                    && amount.compareTo(BigDecimal.ZERO) >= 0) {
                throw new ValidateException(I18N.text(NewPaymentI18N.PAYMENT_RED_AMOUNT_NOT_GT_ZERO));
            }
            if (collectionType.equals(PaymentCollectionTypeEnum.Blue.value)
                    && amount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidateException(I18N.text(NewPaymentI18N.PAYMENT_BLUE_AMOUNT_NOT_GT_ZERO));
            }

            for (IObjectData detailData : detailList) {
                BigDecimal paymentAmount = detailData.get(NewPaymentConst.PAYMENT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
                String orderId = detailData.get(NewPaymentConst.ORDER_ID, String.class);
                String returnedGoodsInvoiceId = detailData.get(NewPaymentConst.RETURNED_GOODS_INVOICE_ID, String.class);
                totalDetailPaymentAmount = totalDetailPaymentAmount.add(paymentAmount);

                if (Strings.isNullOrEmpty(returnedGoodsInvoiceId) == Strings.isNullOrEmpty(orderId)) {
                    throw new ValidateException(I18N.text(NewPaymentI18N.ORDER_PAYMENT_ORDER_RETURN_ONLY_ONE));
                }
                if (collectionType.equals(PaymentCollectionTypeEnum.Red.value)) {
                    if (Strings.isNullOrEmpty(returnedGoodsInvoiceId)) {
                        throw new ValidateException(I18N.text(NewPaymentI18N.ORDER_PAYMENT_RED_RETURN_ID_NOT_EMPTY));
                    }
                    if (paymentAmount.compareTo(BigDecimal.ZERO) >= 0) {
                        throw new ValidateException(I18N.text(NewPaymentI18N.ORDER_PAYMENT_RED_AMOUNT_NOT_GT_ZERO));
                    }
                }
                if (collectionType.equals(PaymentCollectionTypeEnum.Blue.value)) {
                    if (Strings.isNullOrEmpty(orderId)) {
                        throw new ValidateException(I18N.text(NewPaymentI18N.ORDER_PAYMENT_BLUE_ORDER_ID_NOT_EMPTY));
                    }
                    if (paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new ValidateException(I18N.text(NewPaymentI18N.ORDER_PAYMENT_BLUE_AMOUNT_NOT_GT_ZERO));
                    }
                }
            }
            if (totalDetailPaymentAmount.abs().compareTo(amount.abs()) > 0) {
                throw new ValidateException(I18N.text(NewPaymentI18N.AMOUNT_MUST_GT_PAYMENT_AMOUNT));
            }
        }
    }

    public static void copyOrderPaymentCustomFieldData(IObjectDescribe masterDescribe, BaseObjectSaveAction.Arg arg) {
        if (Objects.isNull(arg) || Objects.isNull(masterDescribe)) {
            return;
        }
        ObjectDataDocument masterData = arg.getObjectData();
        Map<String, List<ObjectDataDocument>> details = arg.getDetails();
        if (Objects.isNull(masterData) || MapUtils.isEmpty(details)) {
            return;
        }
        List<ObjectDataDocument> orderPaymentDocuments = details.getOrDefault(NewPaymentConst.ORDER_PAYMENT_API_NAME, Lists.newArrayList());
        if (CollectionUtils.isEmpty(orderPaymentDocuments)) {
            return;
        }

        List<IFieldDescribe> fields = ObjectDescribeExt.of(masterDescribe).getFieldDescribesSilently()
                .stream()
                .filter(f -> "custom".equals(f.getDefineType()) && Boolean.TRUE.equals(f.isActive()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fields)) {
            return;
        }
        for (ObjectDataDocument detailData : orderPaymentDocuments) {
            for (IFieldDescribe field : fields) {
                Object value = masterData.get(field.getApiName());
                if (null != value) {
                    detailData.putIfAbsent(field.getApiName(), value);
                }
            }
        }
    }

    public static void fillAmountIfNotPresent(BaseObjectSaveAction.Arg arg) {
        ObjectDataExt masterDataExt = ObjectDataExt.of(arg.getObjectData());
        BigDecimal amount = masterDataExt.get(NewPaymentConst.AMOUNT, BigDecimal.class);
        //防止【已用金额】为空
        BigDecimal paymentAmount = masterDataExt.get(NewPaymentConst.PAYMENT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        masterDataExt.set(NewPaymentConst.PAYMENT_AMOUNT, paymentAmount);
        List<ObjectDataDocument> orderPaymentList = arg.getDetails().getOrDefault(Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList());
        //单字段编辑，没传amount时，如果没有回款明细这里会被清空
        if (CollectionUtils.isEmpty(orderPaymentList)) {
            if (paymentAmount.compareTo(BigDecimal.ZERO) > 0) {
                //前端从关联对象入口新建回款时，隐藏了回款明细，会导致无回款明细数据，而回款主对象的payment_amount字段的值大于0
                masterDataExt.set(NewPaymentConst.PAYMENT_AMOUNT, BigDecimal.ZERO);
            }
            return;
        }
        String customerId = masterDataExt.get(NewPaymentConst.ACCOUNT_ID, String.class);
        BigDecimal usedAmount = BigDecimal.ZERO;
        for (ObjectDataDocument orderPaymentData : orderPaymentList) {
            BigDecimal v = ObjectDataExt.of(orderPaymentData).get(NewPaymentConst.PAYMENT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            usedAmount = usedAmount.add(v);
            if (StringUtils.isNotEmpty(customerId)) {
                orderPaymentData.put(NewPaymentConst.ACCOUNT_ID, customerId);
            }
        }
        if (Objects.isNull(amount) || (amount.compareTo(BigDecimal.ZERO) <= 0 && !RequestUtil.isCepRequest())) {
            arg.getObjectData().put(NewPaymentConst.AMOUNT, usedAmount.toString());
        }
    }

    public static LayoutDocument setReadOnly(LayoutDocument layoutDocument, Set<String> fieldApiNames, boolean readOnly) {
        LayoutExt layoutExt = LayoutExt.of(layoutDocument);
        layoutExt.getFormComponent().ifPresent(x -> {
            x.setReadOnly(fieldApiNames, readOnly);
        });
        return LayoutDocument.of(layoutExt);
    }

    public static LayoutDocument removeFields(LayoutDocument layoutDocument, Set<String> fieldNamesToRemove) {
        if (Objects.isNull(layoutDocument)) {
            return null;
        }
        if (com.facishare.paas.appframework.common.util.CollectionUtils.empty(fieldNamesToRemove)) {
            return layoutDocument;
        }
        LayoutExt layoutExt = LayoutExt.of(layoutDocument);
        layoutExt.getFormComponent().ifPresent(x -> {
            x.removeFields(fieldNamesToRemove);
        });
        return LayoutDocument.of(layoutExt);
    }

    public static LayoutDocument removeButtonsByAction(LayoutDocument layoutDocument, Set<String> buttonsToRemove) {
        if (Objects.isNull(layoutDocument)) {
            return layoutDocument;
        }
        if (com.facishare.paas.appframework.common.util.CollectionUtils.empty(buttonsToRemove)) {
            return layoutDocument;
        }
        LayoutExt layoutExt = LayoutExt.of(layoutDocument);
        List<IButton> buttonList = layoutExt.getButtons();
        if (com.facishare.paas.appframework.common.util.CollectionUtils.empty(buttonList)) {
            return layoutDocument;
        }
        buttonList.removeIf(x -> buttonsToRemove.contains(x.getAction()));
        layoutExt.setButtons(buttonList);
        return LayoutDocument.of(layoutExt);
    }

    public static IButton getAddPaymentButton() {
        IButton button = new Button();
        String newLabel = I18N.text(NewPaymentI18N.PAYMENT_ADD_BUTTON_LABEL);
        newLabel = StringUtils.isEmpty(newLabel) ? ObjectAction.CREATE.getActionLabel() : newLabel;
        button.setAction("AddPayment");
        button.setName("PaymentObj_Add_button_default");
        button.setLabel(newLabel);
        button.setActionType("default");
        return button;
    }

    public static IButton getButton(ObjectAction objectAction) {
        IButton button = new Button();
        button.setLabel(objectAction.getActionLabel());
        button.setAction(objectAction.getActionCode());
        button.setName(objectAction.getButtonApiName());
        button.setActionType("default");
        return button;
    }

    public static IButton getAsyncBulkButton(ObjectAction objectAction) {
        IButton button = getButton(objectAction);
        button.setAction(objectAction.getBulkButtonApiName());
        return button;
    }

    public static Map<String, Object> builderPaymentRecordRelatedMap() {
        List<ImmutableMap<String, String>> includeFields = LayoutExt.buildPaymentTableColumnList(true)
                .stream()
                .map(c -> ImmutableMap.of("api_name", c.getName(), "field_name", c.getName(), "label", c.getLabelName(), "render_type", c.getRenderType()))
                .collect(Collectors.toList());

        Map<String, Object> openPayLayout = Maps.newHashMap();
        openPayLayout.put("api_name", "payment_record_related_list");
        openPayLayout.put("buttons", Lists.newArrayList());
        openPayLayout.put("header", I18N.text(NewPaymentI18N.SO_PAYMENT_RECORD_RELATED_LIST));
        openPayLayout.put("is_hidden", false);
        openPayLayout.put("is_show_avatar", true);
        openPayLayout.put("include_fields", includeFields);
        openPayLayout.put("order", 3);
        openPayLayout.put("related_list_name", "payment_record_LIST");
        openPayLayout.put("ref_object_api_name", "payment_record");
        openPayLayout.put("relationType", 2);
        openPayLayout.put("type", "relatedlist");
        return openPayLayout;
    }

    public static boolean fieldChanged(IObjectDescribe objectDescribe, IObjectData dbData, IObjectData data, String fieldName) {
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
        if (!data.containsField(fieldName) || Objects.isNull(fieldDescribe)) {
            return false;
        }
        boolean changed;
        if (fieldDescribe instanceof NumberFieldDescribe) {
            BigDecimal value = data.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal dbValue = dbData.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
            changed = value.compareTo(dbValue) != 0;
        } else if (fieldDescribe instanceof BooleanFieldDescribe) {
            Boolean valueBool = data.get(fieldName, Boolean.class);
            Boolean dbValueBool = dbData.get(fieldName, Boolean.class);
            if (Objects.isNull(valueBool) && Objects.isNull(dbValueBool)) {
                changed = false;
            } else if (Objects.nonNull(valueBool) && Objects.nonNull(dbValueBool)) {
                changed = dbValueBool.booleanValue() != valueBool.booleanValue();
            } else {
                changed = true;
            }
        } else if (fieldDescribe instanceof FileAttachmentFieldDescribe || fieldDescribe instanceof OutEmployeeFieldDescribe) {
            List value = data.get(fieldName, List.class);
            List dbValue = dbData.get(fieldName, List.class);
            if (com.facishare.paas.appframework.common.util.CollectionUtils.size(value) != com.facishare.paas.appframework.common.util.CollectionUtils.size(dbValue)) {
                changed = true;
            } else if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(value)) {
                changed = value.stream().anyMatch(x -> !dbValue.contains(x));
            } else {
                changed = false;
            }
        } else {
            String value = data.get(fieldName, String.class);
            String dbValue = dbData.get(fieldName, String.class);
            if (StringUtils.isEmpty(value) && StringUtils.isEmpty(dbValue)) {
                return false;
            }
            changed = !StringUtils.equals(value, dbValue);
        }
        return changed;
    }

    public static void fieldEditCheck(IObjectDescribe objectDescribe, IObjectData dbData, IObjectData data, String fieldName) {
        if (fieldChanged(objectDescribe, dbData, data, fieldName)) {
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
            log.warn("field not support edit,fieldName:{},dbValue:{},newValue:{}", fieldName, dbData.get(fieldName), data.get(fieldName));
            throw new ValidateException(I18N.text(NewPaymentI18N.FILED_NOT_SUPPORT_EDIT, fieldDescribe.getLabel()));
        }
    }

    public static void fieldEditCheck(IObjectDescribe objectDescribe, IObjectData dbData, IObjectData data, Collection<String> fieldNames) {
        if (CollectionUtils.isEmpty(fieldNames)) {
            return;
        }
        fieldNames.forEach(fieldName -> fieldEditCheck(objectDescribe, dbData, data, fieldName));
    }

    public static void fillFilterEq(List filters, String name, Object value) {
        filters.add(filter(name, Operator.EQ, value));
    }

    public static void fillFilterNotEq(List filters, String name, Object value) {
        filters.add(filter(name, Operator.N, value));
    }

    public static Filter filter(String name, Operator operator, Object value) {
        Filter filter = new Filter();
        filter.setFieldName(name);
        if (value instanceof List) {
            filter.setFieldValues((List<String>) value);
        } else {
            filter.setFieldValues(Arrays.asList(value.toString()));
        }
        filter.setOperator(operator);
        return filter;
    }


    private static FsGrayReleaseBiz sfaGray = FsGrayRelease.getInstance("sfa");

    public static boolean hiddenPaymentRecordTab(String tenantId) {
        return sfaGray.isAllow("hidden_payment_record_tab", tenantId);
    }

    public static boolean syncUpdatePaymentPlanStatus(String tenantId) {
        return !sfaGray.isAllow("payment_plan_new_task", tenantId);
    }

    public static boolean supportPaymentNegative(String tenantId) {
        return sfaGray.isAllow("payment_allow_amount_LT_zero", tenantId);
    }

    public static boolean allowOrderPaymentTriggerMasterApproval(String tenantId) {
        return FsGrayRelease.isAllow("sail", "orderPaymentTriggerMasterApprovalEis", tenantId);
    }

    public static boolean onlyPaymentAddButton(String tenantId) {
        return FsGrayRelease.isAllow("sail", "orderPaymentListOnlyPaymentAddButton", tenantId);
    }

    public static boolean isOldPayment(String tenantId) {
        return Objects.nonNull(newPaymentTenantIdsBlack) && newPaymentTenantIdsBlack.contains(tenantId);
    }

    public static boolean isNewPaymentMergeTenant(String tenantId) {
        return !isOldPayment(tenantId);
    }

    public static SearchTemplateQuery minimumQuery() {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");
        return query;
    }
    public static SearchTemplateQuery minimumQuery(IFilter... filters) {
        SearchTemplateQuery query = minimumQuery();
        query.setFilters(Arrays.stream(filters).collect(Collectors.toList()));
        return query;
    }

    public static List<IObjectData> find(ServiceFacade facade, String tenantId, String apiName, SearchTemplateQuery query, List<String> fields) {
        return find(facade, User.systemUser(tenantId), null, apiName, query, fields);
    }


    public static List<IObjectData> find(ServiceFacade facade, User user, RequestContext context, String apiName, SearchTemplateQuery query, List<String> fields) {
        int max = query.getLimit() == 0 || query.getLimit() == -1 ? MAX_QUERY_SIZE : query.getLimit();

        int limit = Math.min(2000, max);
        int offset = 0;

        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> data = Lists.newArrayList();
        QueryResult<IObjectData> result;

        SearchTemplateQuery innerQuery = copy(query);
        while (!(result = facade.findBySearchTemplateQueryWithFields(ActionContextExt.of(user, context).getContext(), apiName, innerQuery, fields)).getData().isEmpty() && data.size() < max) {

            data.addAll(result.getData());
            offset += result.getData().size();

            innerQuery = copy(query);
            innerQuery.setOffset(offset);
        }
        return data;
    }

    @SneakyThrows
    private static SearchTemplateQuery copy(SearchTemplateQuery query) {
        SearchTemplateQuery clone = new SearchTemplateQuery();
        for (Field field : SearchTemplateQuery.class.getDeclaredFields()) {
            field.setAccessible(true);
            field.set(clone, field.get(query));
        }
        return clone;
    }

    private static List<String> newPaymentTenantIdsBlack = Lists.newArrayList();

    static {
        ConfigFactory.getConfig("fs-crm-customeraccount", config -> {
            newPaymentTenantIdsBlack = Splitter.onPattern(",").omitEmptyStrings().splitToList(config.get("newPaymentTenantIdsBlack", ""));
        });
    }

}
