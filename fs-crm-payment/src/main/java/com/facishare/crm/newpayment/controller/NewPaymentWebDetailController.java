package com.facishare.crm.newpayment.controller;

import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.constants.NewPaymentI18N;
import com.facishare.crm.newpayment.enums.PaymentCollectionTypeEnum;
import com.facishare.crm.newpayment.manager.NewPaymentManger;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class NewPaymentWebDetailController extends StandardWebDetailController {
    private final NewPaymentManger newPaymentManger = SpringUtil.getContext().getBean(NewPaymentManger.class);
    protected static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected Result doService(Arg arg) {
        log.warn("test newpayment NewPaymentWebDetailController");
        Result newResult = super.doService(arg);
        newResult = setOrderNameToData(newResult);
        if (!arg.getIncludeLayout()) {
            return newResult;
        }
        if (newResult.getLayout() == null) {
            throw new ValidateException(I18N.text(NewPaymentI18N.PAYMENT_LAYOUT_NOT_EXIST));
        }
        ILayout newLayout = newResult.getLayout().toLayout();
        newLayout = addPaymentRecordRelatedList(newLayout);
        newLayout = modifyResult(newLayout);
        newResult.setLayout(LayoutDocument.of(newLayout));
        return newResult;
    }


    /**
     * 添加企业收款明细页签
     *
     * @param layout
     * @return
     */
    private ILayout addPaymentRecordRelatedList(ILayout layout) {
        String tenantId = controllerContext.getTenantId();
        if(NewPaymentUtil.hiddenPaymentRecordTab(tenantId)){
            return layout;
        }
        String value = serviceFacade.findTenantConfig(User.systemUser(tenantId), NewPaymentConst.SALES_ORDER_PAY_DIRECTLY_SWITCH_CONFIG);
        if ("1".equals(value)) {
            return layout;
        }
        Map<String, Object> stringObjectMap = NewPaymentUtil.builderPaymentRecordRelatedMap();
        try {
            IComponent multiComponent = ComponentFactory.newInstance(stringObjectMap);
            // 放在最后一个
            WebDetailLayout.of(layout).addComponents(Lists.newArrayList(multiComponent));
            return layout;
        } catch (Exception e) {
            log.error("PaymentWebDetailController addComponents error", e);
        }
        return layout;
    }

    /**
     * 过滤当前审批人字段
     * 只做字段屏蔽，从对象的按钮屏蔽
     * 走DetailListHeader
     *
     * @param layout
     * @return
     */
    private ILayout modifyResult(ILayout layout) {
        WebDetailLayout.of(layout).removeFields(Lists.newArrayList("approve_employee_id"));
        return layout;
    }

    /**
     * 添加订单的信息到回款中
     *
     * @param result
     * @return
     */
    private Result setOrderNameToData(Result result) {
        ObjectDataDocument data = result.getData();
        String paymentId = data.getId();
        List<IObjectData> orderPayments = newPaymentManger.queryOrderPaymentByPaymentId(controllerContext.getUser(), paymentId);
        Set<String> orderIds = orderPayments.stream().map(x -> x.get(NewPaymentConst.ORDER_ID, String.class)).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        data.put(NewPaymentConst.ORDER_ID, Joiner.on(",").join(orderIds));
        newPaymentManger.parseOrderName(controllerContext.getUser(), Lists.newArrayList(data));
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if(newResult.getLayout() != null){
            ILayout layout = new Layout(newResult.getLayout());
            processButton(layout, controllerContext.getTenantId());
        }
        return newResult;
    }
    private void processButton(ILayout layout, String tenantId) {
        //List<IButton> buttons = headInfoComponentOp.get().getButtons();
        BigDecimal noMatchedAmount = data.get(NewPaymentConst.NO_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);

        if (!bizConfigThreadLocalCacheService.isOpenAutoMatch(tenantId)|| noMatchedAmount.compareTo(BigDecimal.ZERO) <= 0) {
            //buttons.removeIf(b -> b.getAction().equals(ObjectAction.AUTO_MATCH.getActionCode()));
            WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.AUTO_MATCH.getActionCode()));
        }
        /*String collectionType = data.get(NewPaymentConst.COLLECTION_TYPE, String.class);
        if (Strings.isEmpty(collectionType) || !Objects.equals(collectionType, PaymentCollectionTypeEnum.Blue.value)) {
            //buttons.removeIf(b -> b.getAction().equals(ObjectAction.AUTO_MATCH.getActionCode()));
            WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.AUTO_MATCH.getActionCode()));
        }*/
        boolean enterIntoAccount = data.get(NewPaymentConst.ENTER_INTO_ACCOUNT, Boolean.class, false);
        if (enterIntoAccount || Objects.equals(data.get(NewPaymentConst.MATCH_STATUS, String.class), "all")) {
            //buttons.removeIf(b -> b.getAction().equals(ObjectAction.MANUAL_MATCH.getActionCode()));
            WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.MANUAL_MATCH.getActionCode()));
        }
        //buttons.removeIf(b -> Objects.equals(b.getAction(), ObjectAction.INTELLIGENTFORM.getActionCode()));
        WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.INTELLIGENTFORM.getActionCode()));
        //WebDetailLayout.of(layout).setButtons(buttons);
    }
}
