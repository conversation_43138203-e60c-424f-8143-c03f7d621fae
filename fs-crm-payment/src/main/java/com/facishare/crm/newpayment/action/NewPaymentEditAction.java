package com.facishare.crm.newpayment.action;

import com.facishare.crm.newpayment.service.NewCustomerPaymentService;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.constants.NewPaymentI18N;
import com.facishare.crm.newpayment.enums.MatchStatusEnum;
import com.facishare.crm.newpayment.manager.CommonAccountsReceivableNoteManager;
import com.facishare.crm.newpayment.manager.NewPaymentManger;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.crm.newpayment.validator.RedPaymentAmountValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

public class NewPaymentEditAction extends StandardEditAction {
    private NewPaymentManger newPaymentManger = SpringUtil.getContext().getBean(NewPaymentManger.class);
    private final RedPaymentAmountValidator redPaymentAmountValidator = SpringUtil.getContext().getBean(RedPaymentAmountValidator.class);
    private final CommonAccountsReceivableNoteManager commonAccountsReceivableNoteManager = SpringUtil.getContext().getBean(CommonAccountsReceivableNoteManager.class);
    private final NewCustomerPaymentService newCustomerPaymentService = SpringUtil.getContext().getBean(NewCustomerPaymentService.class);

    @Override
    protected void before(Arg arg) {
        NewPaymentUtil.fillAmountIfNotPresent(arg);
        //super.before(arg)会被没有的字段也补上，只是值=null…… containsField = true
        super.before(arg);
        boolean notForceCheck = true;
        if (!Objects.isNull(arg.getObjectData().get("notForceCheck"))) {
            notForceCheck = Boolean.parseBoolean(arg.getObjectData().get("notForceCheck").toString());
        }
        //已用金额如果为null，就设为0
        IObjectData objectData = arg.getObjectData().toObjectData();
        if (objectData.containsField(NewPaymentConst.PAYMENT_AMOUNT)) {
            BigDecimal paymentAmount = objectData.get(NewPaymentConst.PAYMENT_AMOUNT, BigDecimal.class);
            if (Objects.isNull(paymentAmount)) {
                objectData.set(NewPaymentConst.PAYMENT_AMOUNT, BigDecimal.ZERO);
            }
        }

        List<IObjectData> orderPaymentList = this.detailObjectData.getOrDefault(Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList());
        List<IObjectData> dbOrderPaymentList = this.dbDetailDataMap.getOrDefault(Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList());
        if (!NewPaymentUtil.isSupportRedPayment(actionContext.getTenantId(), objectData)) {
            validatePaymentAmount();
        }
        String matchStatus = this.dbMasterData.get(NewPaymentConst.MATCH_STATUS, String.class);
        BigDecimal matchAmount = this.dbMasterData.get(NewPaymentConst.MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        BigDecimal amount = this.objectData.get(NewPaymentConst.AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        ObjectDataExt objectDataExt = ObjectDataExt.of(this.objectData);
        //已核销状态
        if (MatchStatusEnum.matched(matchStatus) && objectDataExt.containsField(NewPaymentConst.AMOUNT)
                && matchAmount.abs().compareTo(amount.abs()) > 0) {
            throw new ValidateException(I18N.text(NewPaymentI18N.AMOUNT_MUST_GT_MATCH_AMOUNT));
        }

        String dbCustomerId = this.dbMasterData.get(NewPaymentConst.ACCOUNT_ID, String.class);
        String customerId = this.objectData.get(NewPaymentConst.ACCOUNT_ID, String.class);

        Set<String> orderIds = Sets.newHashSet();
        for (IObjectData orderPaymentData : orderPaymentList) {
            String orderId = orderPaymentData.get(NewPaymentConst.ORDER_ID, String.class);
            if (StringUtils.isNotEmpty(orderId)) {
                orderIds.add(orderId);
            }
        }
        //校验红蓝回款对应正负金额
        if (NewPaymentUtil.isSupportRedPayment(actionContext.getTenantId(), objectData)) {
            NewPaymentUtil.checkPaymentCollectionType(objectData, orderPaymentList);
        }
        //仅启用新回款:未关联客户的回款编辑 : 如果未选择客户，从对象不可创建。
        if (Strings.isNullOrEmpty(dbCustomerId) && Strings.isNullOrEmpty(customerId) && CollectionUtils.notEmpty(orderPaymentList)) {
            throw new ValidateException(I18N.text(NewPaymentI18N.PAYMENT_NO_ACCOUNT_CANNOT_ADD_DETAIL));
        }
        //仅启用新回款:已关联客户的回款编辑：如果关联了回款明细，则【客户名称】不可再编辑
        if (!Strings.isNullOrEmpty(dbCustomerId) && CollectionUtils.notEmpty(dbOrderPaymentList)) {
            if (!Objects.equals(dbCustomerId, customerId)) {
                throw new ValidateException(I18N.text(NewPaymentI18N.PAYMENT_HAS_PAYMENT_DETAIL_CUSTOMER_CANNOT_EDIT));
            }
        }

        //新回款+应收管理: 已关联客户的回款编辑: 如果已被核销单关联，则不可编辑【客户名称】（包括已作废的核销单）
        String paymentId = this.dbMasterData.getId();
        boolean hasLinkMatchNote = newPaymentManger.hasLinkMatchNote(actionContext.getUser(), paymentId);
        if (!Strings.isNullOrEmpty(dbCustomerId) && hasLinkMatchNote && !Objects.equals(dbCustomerId, customerId)) {
            throw new ValidateException(I18N.text(NewPaymentI18N.PAYMENT_HAS_MATCH_NOTE_CUSTOMER_CANNOT_EDIT));
        }
        if (CollectionUtils.notEmpty(orderIds)) {
            this.objectData.set(NewPaymentConst.ORDER_ID, Joiner.on(",").join(orderIds));
        }
        //兼容
        NewPaymentUtil.copyPaymentCustomFieldDataToOrderPayment(this.objectDescribe, arg.getObjectData(), ObjectDataDocument.ofList(orderPaymentList));
        redPaymentAmountValidator.validateAmountExcess(actionContext.getTenantId(), arg);
        if (NewPaymentUtil.isSupportRedPayment(actionContext.getTenantId(), objectData)) {
            commonAccountsReceivableNoteManager.checkPaymentMatchNote(actionContext.getUser(), arg.getObjectData().toObjectData());
            commonAccountsReceivableNoteManager.checkOpeningBalance(actionContext.getUser(), objectData, notForceCheck);
        }
        if (newCustomerPaymentService.checkOrderPaymentIsRequired(actionContext.getUser(), objectData)) {
            if (CollectionUtils.empty(arg.getDetails().get(NewPaymentConst.ORDER_PAYMENT_API_NAME))) {
                throw new ValidateException(I18N.text("newpayment.add.validate.orderpayment.isrequired"));
            }
        }
    }

    private void validatePaymentAmount() {
        List<IObjectData> orderPaymentList = this.detailObjectData.getOrDefault(Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList());
        if (CollectionUtils.empty(orderPaymentList) && !this.detailObjectData.containsKey(Utils.ORDER_PAYMENT_API_NAME)) {
            //列表页快速编辑时，orderPaymentList为空；arg参数中没有OrderPaymentObj数据
            orderPaymentList = newPaymentManger.queryOrderPaymentByPaymentId(actionContext.getUser(), this.objectData.getId());
        }
        NewPaymentUtil.validatePaymentAmount(actionContext.getUser(), objectData, orderPaymentList);
    }

    @Override
    protected List<String> modifyObjectDataWhenStartApprovalFlowByResultMap(ApprovalFlowTriggerType type, List<IObjectData> objectDataList, Map<String, ApprovalFlowStartResult> resultMap) {
        List<String> fieldsProjection = super.modifyObjectDataWhenStartApprovalFlowByResultMap(type, objectDataList, resultMap);
        if (fieldsProjection.isEmpty()) {
            return fieldsProjection;
        }
        for (IObjectData objectData : objectDataList) {
            ApprovalFlowStartResult result = resultMap.get(objectData.getId());
            if (result == ApprovalFlowStartResult.SUCCESS) {
                objectData.set("submit_time", System.currentTimeMillis());
            }
        }
        fieldsProjection.add("submit_time");
        return fieldsProjection;
    }

    @Override
    protected Set<String> getIgnoreFieldsForApproval() {
        Set<String> result = super.getIgnoreFieldsForApproval();
        result.add(NewPaymentConst.ORDER_ID);
        return result;
    }

}
