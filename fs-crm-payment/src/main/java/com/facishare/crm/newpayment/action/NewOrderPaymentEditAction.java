package com.facishare.crm.newpayment.action;

import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.constants.OrderPaymentConstants;
import com.facishare.crm.newpayment.manager.NewPaymentManger;
import com.facishare.crm.newpayment.manager.PaymentPlanManager;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.newpayment.constants.NewPaymentI18N;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class NewOrderPaymentEditAction extends StandardEditAction {
    private final NewPaymentManger newPaymentManger = SpringUtil.getContext().getBean(NewPaymentManger.class);
    private final PaymentPlanManager paymentPlanManager = SpringUtil.getContext().getBean(PaymentPlanManager.class);

    private IObjectData masterData;
    private List<IObjectData> dbOrderPaymentDataList;
    private boolean orderIdUpdated;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        //回款的所有回款明细【使用金额】总额不可大于【回款金额】
        String paymentId = this.objectData.get(NewPaymentConst.PAYMENT_ID, String.class);
        masterData = serviceFacade.findObjectData(actionContext.getUser(), paymentId, Utils.CUSTOMER_PAYMENT_API_NAME);
        dbOrderPaymentDataList = newPaymentManger.queryOrderPaymentByPaymentId(actionContext.getUser(), paymentId);
        orderIdUpdated = ObjectDataExt.of(this.dbMasterData).diff(this.objectData, this.objectDescribe).containsKey(OrderPaymentConstants.Field.SalesOrder.apiName);
        BigDecimal amount = masterData.get(NewPaymentConst.AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        BigDecimal usedAmount = dbOrderPaymentDataList.stream()
                .filter(x -> !ObjectLifeStatus.INEFFECTIVE.getCode().equals(ObjectDataExt.of(x).getLifeStatusText()) && !x.getId().equals(this.objectData.getId()))
                .map(x -> x.get(NewPaymentConst.PAYMENT_AMOUNT, BigDecimal.class)).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal paymentAmount = this.objectData.get(NewPaymentConst.PAYMENT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);

        //校验红蓝回款对应正负金额
        if (NewPaymentUtil.isSupportRedPayment(actionContext.getTenantId(), masterData)) {
            List<IObjectData> detailCheckList = new ArrayList<>();
            detailCheckList.add(objectData);
            if (CollectionUtils.notEmpty(dbOrderPaymentDataList)) {
                detailCheckList.addAll(dbOrderPaymentDataList);
            }
            NewPaymentUtil.checkPaymentCollectionType(masterData, detailCheckList);
        } else {
            if (usedAmount.add(paymentAmount).compareTo(amount) > 0) {
                throw new ValidateException(I18N.text(NewPaymentI18N.ORDER_PAYMENT_AVAILABLE_AMOUNT_NOT_ENOUGH, masterData.getName()));
            }
            if (!NewPaymentUtil.supportPaymentNegative(actionContext.getTenantId())) {
                if (paymentAmount.compareTo(BigDecimal.ZERO) < 0) {
                    throw new ValidateException(I18N.text(NewPaymentI18N.SO_PAYMENT_NOTFINDORDERPAYYMENTMONEYGT0));
                }
                if (usedAmount.add(paymentAmount).compareTo(BigDecimal.ZERO) < 0) {
                    throw new ValidateException(I18N.text(NewPaymentI18N.SO_PAYMENT_NOTFINDORDERPAYYMENTMONEYGT0));
                }
            }
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        if (orderIdUpdated) {
            List<IObjectData> newOrderPaymentList = dbOrderPaymentDataList.stream().filter(x -> !x.getId().equals(this.objectData.getId())).collect(Collectors.toList());
            newOrderPaymentList.add(this.objectData);
            newPaymentManger.updatePaymentOrderIdText(actionContext.getRequestContext(), masterData, newOrderPaymentList);
        }
        String planId = result.getObjectData().toObjectData().get(NewPaymentConst.PAYMENT_PLAN_ID, String.class);
        if (StringUtils.isNotEmpty(planId) && NewPaymentUtil.syncUpdatePaymentPlanStatus(actionContext.getTenantId())) {
            paymentPlanManager.updatePaymentPlan(actionContext.getUser(), Sets.newHashSet(planId));
        }
        return result;
    }
}
