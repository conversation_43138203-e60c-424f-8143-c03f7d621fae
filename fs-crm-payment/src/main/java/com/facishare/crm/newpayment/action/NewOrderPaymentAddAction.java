package com.facishare.crm.newpayment.action;

import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.manager.NewPaymentManger;
import com.facishare.crm.newpayment.manager.PaymentPlanManager;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.newpayment.constants.NewPaymentI18N;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

public class NewOrderPaymentAddAction extends StandardAddAction {
    private final PaymentPlanManager paymentPlanManager = SpringUtil.getContext().getBean(PaymentPlanManager.class);
    private final NewPaymentManger newPaymentManger = SpringUtil.getContext().getBean(NewPaymentManger.class);
    private IObjectData masterData;
    private List<IObjectData> dbOrderPaymentDataList;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        //校验回款金额
        String paymentId = this.objectData.get(NewPaymentConst.PAYMENT_ID, String.class);
        masterData = serviceFacade.findObjectData(actionContext.getUser(), paymentId, Utils.CUSTOMER_PAYMENT_API_NAME);
        dbOrderPaymentDataList = newPaymentManger.queryOrderPaymentByPaymentId(actionContext.getUser(), paymentId);
        BigDecimal amount = masterData.get(NewPaymentConst.AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        BigDecimal usedAmount = dbOrderPaymentDataList.stream().filter(x -> !ObjectLifeStatus.INEFFECTIVE.getCode().equals(ObjectDataExt.of(x).getLifeStatusText()))
                .map(x -> x.get(NewPaymentConst.PAYMENT_AMOUNT, BigDecimal.class)).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal paymentAmount = this.objectData.get(NewPaymentConst.PAYMENT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        String customerId = masterData.get(NewPaymentConst.ACCOUNT_ID, String.class);
        if (StringUtils.isNotEmpty(customerId)) {
            this.objectData.set(NewPaymentConst.ACCOUNT_ID, customerId);
        }
        //校验红蓝回款对应正负金额
        if (NewPaymentUtil.isSupportRedPayment(actionContext.getTenantId(), masterData)) {
            List<IObjectData> detailCheckList = new ArrayList<>();
            detailCheckList.add(objectData);
            if (CollectionUtils.notEmpty(dbOrderPaymentDataList)) {
                detailCheckList.addAll(dbOrderPaymentDataList);
            }
            NewPaymentUtil.checkPaymentCollectionType(masterData, detailCheckList);
        } else {
            if (usedAmount.add(paymentAmount).compareTo(amount) > 0) {
                throw new ValidateException(I18N.text(NewPaymentI18N.ORDER_PAYMENT_AVAILABLE_AMOUNT_NOT_ENOUGH, masterData.getName()));
            }

            if (paymentAmount.compareTo(BigDecimal.ZERO) <= 0 && !GrayUtil.grayPaymentAllowAmountLTZero(actionContext.getTenantId())) {
                throw new ValidateException(I18N.text(NewPaymentI18N.SO_PAYMENT_NOTFINDORDERPAYYMENTMONEYGT0));
            }
        }
    }

    /**
     * 不触发主对象的审批流
     */
    @Override
    protected boolean needTriggerMasterApproval() {
        return NewPaymentUtil.allowOrderPaymentTriggerMasterApproval(actionContext.getTenantId());
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        String planId = result.getObjectData().toObjectData().get(NewPaymentConst.PAYMENT_PLAN_ID, String.class);
        if (StringUtils.isNotEmpty(planId) && NewPaymentUtil.syncUpdatePaymentPlanStatus(actionContext.getTenantId())) {
            paymentPlanManager.updatePaymentPlan(actionContext.getUser(), Sets.newHashSet(planId));
        }
        String orderId = this.objectData.get(NewPaymentConst.ORDER_ID, String.class);
        if (StringUtils.isNotEmpty(orderId)) {
            Set<String> orderIds = Sets.newHashSet();
            orderIds.addAll(dbOrderPaymentDataList.stream().map(x -> x.get(NewPaymentConst.ORDER_ID, String.class)).filter(Objects::nonNull).collect(Collectors.toSet()));
            orderIds.add(orderId);
            masterData.set(NewPaymentConst.ORDER_ID, Joiner.on(",").join(orderIds));
            serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(masterData), Lists.newArrayList(NewPaymentConst.ORDER_ID));
            newPaymentManger.calculateAndUpdateFormulaFields(actionContext.getRequestContext(), Utils.SALES_ORDER_API_NAME,
                    Lists.newArrayList(orderId), Lists.newArrayList(NewPaymentConst.RECEIVABLE_AMOUNT, NewPaymentConst.PAYMENT_AMOUNT));
        }
        return result;
    }
}
