package com.facishare.crm.newpayment.validator;

import com.facishare.crm.constants.MatchNoteDetailConstants;
import com.facishare.crm.constants.ReturnedGoodsInvoiceConstants;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.constants.NewPaymentI18N;
import com.facishare.crm.newpayment.constants.OrderPaymentConstants;
import com.facishare.crm.newpayment.enums.PaymentCollectionTypeEnum;
import com.facishare.crm.newpayment.manager.CommonAccountsReceivableNoteManager;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class RedPaymentAmountValidator {
    @Resource
    private ServiceFacade serviceFacade;
    @Autowired
    private CommonAccountsReceivableNoteManager commonAccountsReceivableNoteManager;

    public void validate(RequestContext requestContext, BaseObjectSaveAction.Arg arg, IObjectDescribe objectDescribe, boolean receivableEnable) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        User user = requestContext.getUser();

        Map<String, List<ObjectDataDocument>> details = arg.getDetails();
        List<ObjectDataDocument> orderPaymentDataList = details.getOrDefault(Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList());
        if (receivableEnable && !CollectionUtils.isEmpty(orderPaymentDataList)) {
            log.warn("receivableEnable orderPaymentDataList not empty tenantId:{}", user.getTenantId());
        }
        if (NewPaymentUtil.isSupportRedPayment(requestContext.getTenantId(), objectData)) {
            //校验红蓝回款对应正负金额
            NewPaymentUtil.checkPaymentCollectionType(objectData, ObjectDataDocument.ofDataList(orderPaymentDataList));
        } else {
            NewPaymentUtil.validatePaymentAmount(user, objectData, orderPaymentDataList.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList()));
        }

        Set<String> orderIds = Sets.newHashSet();
        for (ObjectDataDocument orderPaymentData : orderPaymentDataList) {
            IObjectData orderPayment = ObjectDataExt.of(orderPaymentData);
            String orderId = orderPayment.get(NewPaymentConst.ORDER_ID, String.class, "");
            if (StringUtils.isNotEmpty(orderId)) {
                orderIds.add(orderId);
            }
        }
        objectData.set(NewPaymentConst.ORDER_ID, Joiner.on(",").join(orderIds));
        String paymentTerm = objectData.get(NewPaymentConst.PAYMENT_TERM, String.class);
        SelectOneFieldDescribe paymentField = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe(NewPaymentConst.PAYMENT_TERM);
        boolean noneMatch = StringUtils.isNotEmpty(paymentTerm) && paymentField.getSelectOptions().stream().noneMatch(x -> !x.isNotUsable() && x.getValue().equals(paymentTerm));
        if (noneMatch) {
            throw new ValidateException(I18N.text(NewPaymentI18N.OPTION_NOT_USABLE_OR_NOT_EXIST, paymentField.getLabel()));
        }
        String accountId = objectData.get(NewPaymentConst.ACCOUNT_ID, String.class, null);
        //如果未选择客户，从对象不可创建
        if (Strings.isNullOrEmpty(accountId) && !CollectionUtils.isEmpty(orderPaymentDataList)) {
            throw new ValidateException(I18N.text(NewPaymentI18N.PAYMENT_NO_ACCOUNT_CANNOT_ADD_DETAIL));
        }

        validateAmountExcess(user.getTenantId(), arg);
        if (NewPaymentUtil.isSupportRedPayment(requestContext.getTenantId(), objectData)) {
            boolean notForceCheck = true;
            if (!Objects.isNull(arg.getObjectData().get("notForceCheck"))) {
                notForceCheck = Boolean.parseBoolean(arg.getObjectData().get("notForceCheck").toString());
            }
            commonAccountsReceivableNoteManager.checkPaymentMatchNote(user, objectData);
            commonAccountsReceivableNoteManager.checkOpeningBalance(user, objectData, notForceCheck);
        }
    }

    /**
     * 可生成红字回款单的最大金额（负数金额取绝对值）=退货单.退货金额-max(退货单.已退款金额,退货单.累计结算金额）
     *
     * @param tenantId tenantId
     * @param arg      param of payment add
     */
    public void validateAmountExcess(String tenantId, BaseObjectSaveAction.Arg arg) {
        if (!GrayUtil.redPaymentMaxPaymentAmountValidate(tenantId)) {
            return;
        }
        IObjectData objectData = arg.getObjectData().toObjectData();
        String collectionType = objectData.get(NewPaymentConst.COLLECTION_TYPE, String.class);
        if (!Objects.equals(collectionType, PaymentCollectionTypeEnum.Red.value)) {
            return;
        }
        List<ObjectDataDocument> orderPaymentDataList = arg.getDetails().getOrDefault(Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList());
        if (CollectionUtils.isEmpty(orderPaymentDataList)) {
            return;
        }
        List<IObjectData> detailList = orderPaymentDataList.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());

        Map<String, List<IObjectData>> detailsByReturnedInvoiceId = detailList.stream().collect(Collectors.groupingBy(detail -> detail.get(OrderPaymentConstants.Field.ReturnedGoodsInvoice.getApiName(), String.class)));
        for (Map.Entry<String, List<IObjectData>> entry : detailsByReturnedInvoiceId.entrySet()) {
            String returnedInvoiceId = entry.getKey();
            //key为null时为订单的回款 无需交验
            if (StringUtils.isEmpty(returnedInvoiceId)) {
                continue;
            }
            BigDecimal amount = calculateMaxAmount(tenantId, returnedInvoiceId).abs();

            BigDecimal detailsSumAmount = calculateDetailSumPaymentAmount(entry.getValue()).abs();

            if (amount.compareTo(detailsSumAmount) < 0) {
                throw new ValidateException( I18N.text("redpayment.validate.amount"));//您的回款单明细本次回款金额总和已超出退货单的最大可回款金额，请调整后重新提交.
            }
        }
    }

    private BigDecimal calculateDetailSumPaymentAmount(List<IObjectData> details) {

        BigDecimal detailSumAmount = new BigDecimal("0");
        for (IObjectData detail : details) {
            detailSumAmount = detailSumAmount.add(detail.get(OrderPaymentConstants.Field.PaymentAmount.getApiName(), BigDecimal.class));
        }
        return detailSumAmount.abs();
    }

    public BigDecimal calculateMaxAmount(String tenantId, String returnGoodsInvoiceId) {

        IObjectData returnGoodsInvoice = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), returnGoodsInvoiceId, SystemConstants.ReturnedGoodsInvoiceApiName);
        BigDecimal returnGoodsInvAmount = returnGoodsInvoice.get(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceField.RETURNED_GOODS_INV_AMOUNT.getApiName(), BigDecimal.class, new BigDecimal("0")).abs();

        BigDecimal carSalesReturnedAmount = calculateRefundAmount(tenantId, returnGoodsInvoiceId);

        List<String> accountReceivableDetailIds = accountReceivableDetailIds(tenantId, returnGoodsInvoiceId);
        BigDecimal totalSettledAmount = calculateTotalSettledAmount(tenantId, accountReceivableDetailIds);


        BigDecimal amount;
        if (carSalesReturnedAmount.compareTo(totalSettledAmount) < 0) {
            amount = returnGoodsInvAmount.subtract(totalSettledAmount);
        } else {
            amount = returnGoodsInvAmount.subtract(carSalesReturnedAmount);
        }

        return amount;

    }

    private BigDecimal calculateRefundAmount(String tenantId, String returnGoodsInvoiceId) {

        if (StringUtils.isEmpty(returnGoodsInvoiceId)) {
            return new BigDecimal("0");
        }
        BigDecimal total = new BigDecimal("0");

        Filter arDetailIdFilter = new Filter();
        arDetailIdFilter.setFieldName(OrderPaymentConstants.Field.ReturnedGoodsInvoice.getApiName());
        arDetailIdFilter.setOperator(Operator.EQ);
        arDetailIdFilter.setFieldValues(Lists.newArrayList(returnGoodsInvoiceId));

        SearchTemplateQuery stq = NewPaymentUtil.minimumQuery(arDetailIdFilter);
        List<IObjectData> details = NewPaymentUtil.find(
                serviceFacade,
                tenantId,
                OrderPaymentConstants.API_NAME,
                stq,
                Lists.newArrayList("_id", OrderPaymentConstants.Field.PaymentAmount.getApiName())
        );

        for (IObjectData detail : details) {
            BigDecimal amount = detail.get(OrderPaymentConstants.Field.PaymentAmount.getApiName(), BigDecimal.class);
            if (Objects.nonNull(amount)) {
                total = total.add(amount);
            }

        }
        return total.abs();
    }

    private BigDecimal calculateTotalSettledAmount(String tenantId, List<String> accountReceivableDetailIds) {

        BigDecimal creditSettledAmount = calculateCreditSettledAmount(tenantId, accountReceivableDetailIds);
        BigDecimal debitSettledAmount = calculateDebitSettledAmount(tenantId, accountReceivableDetailIds);

        return creditSettledAmount.add(debitSettledAmount);
    }

    //收付款对象已结算金额
    protected BigDecimal calculateCreditSettledAmount(String tenantId, List<String> accountReceivableNoteDetailIds) {
        List<List<String>> partition = Lists.partition(accountReceivableNoteDetailIds, 200);
        BigDecimal amount = new BigDecimal("0");
        for (List<String> accountReceivableNoteDetailIdsGroup : partition) {

            SearchTemplateQuery query = new SearchTemplateQuery();

            query.setSearchSource("db");
            query.setLimit(200);
            query.setOffset(0);

            Filter debitDataIdFilter = new Filter();
            debitDataIdFilter.setFieldName(MatchNoteDetailConstants.Field.CREDIT_DETAIL_DATA_ID.getApiName());
            debitDataIdFilter.setOperator(Operator.IN);
            debitDataIdFilter.setFieldValues(accountReceivableNoteDetailIdsGroup);

            Filter debitApiNameFilter = new Filter();
            debitApiNameFilter.setFieldName(MatchNoteDetailConstants.Field.CREDIT_DETAIL_API_NAME.getApiName());
            debitApiNameFilter.setOperator(Operator.EQ);
            debitApiNameFilter.setFieldValues(Lists.newArrayList(SystemConstants.AccountsReceivableDetailApiName));

            Filter isDeletedFilter = new Filter();
            isDeletedFilter.setFieldName(SystemConstants.Field.IsDelete.apiName);
            isDeletedFilter.setOperator(Operator.EQ);
            isDeletedFilter.setFieldValues(Lists.newArrayList("false"));

            query.setFilters(Lists.newArrayList(debitDataIdFilter, debitApiNameFilter, isDeletedFilter));

            List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                    User.systemUser(tenantId),
                    query,
                    MatchNoteDetailConstants.API_NAME,
                    Lists.newArrayList(MatchNoteDetailConstants.Field.CREDIT_DETAIL_DATA_ID.getApiName(), MatchNoteDetailConstants.Field.CREDIT_DETAIL_API_NAME.getApiName()),
                    "sum",
                    MatchNoteDetailConstants.Field.CREDIT_MATCH_AMOUNT.getApiName()
            );

            if (!CollectionUtils.isEmpty(data)) {
                for (IObjectData datum : data) {
                    BigDecimal sumAmount = datum.get("sum_" + MatchNoteDetailConstants.Field.CREDIT_MATCH_AMOUNT.getApiName(), BigDecimal.class);
                    if (!Objects.isNull(sumAmount)) {
                        amount = amount.add(sumAmount);
                    }
                }

            }
        }
        return amount.abs();
    }

    //核销对象已结算金额
    protected BigDecimal calculateDebitSettledAmount(String tenantId, List<String> accountReceivableNoteDetailIds) {
        List<List<String>> partition = Lists.partition(accountReceivableNoteDetailIds, 200);
        BigDecimal amount = new BigDecimal("0");
        for (List<String> accountReceivableNoteDetailIdsGroup : partition) {

            SearchTemplateQuery query = new SearchTemplateQuery();

            query.setSearchSource("db");
            query.setLimit(200);
            query.setOffset(0);

            Filter debitDataIdFilter = new Filter();
            debitDataIdFilter.setFieldName(MatchNoteDetailConstants.Field.DEBIT_DETAIL_DATA_ID.getApiName());
            debitDataIdFilter.setOperator(Operator.IN);
            debitDataIdFilter.setFieldValues(accountReceivableNoteDetailIdsGroup);

            Filter debitApiNameFilter = new Filter();
            debitApiNameFilter.setFieldName(MatchNoteDetailConstants.Field.DEBIT_DETAIL_API_NAME.getApiName());
            debitApiNameFilter.setOperator(Operator.EQ);
            debitApiNameFilter.setFieldValues(Lists.newArrayList(SystemConstants.AccountsReceivableDetailApiName));

            Filter isDeletedFilter = new Filter();
            isDeletedFilter.setFieldName(SystemConstants.Field.IsDelete.apiName);
            isDeletedFilter.setOperator(Operator.EQ);
            isDeletedFilter.setFieldValues(Lists.newArrayList("false"));


            query.setFilters(Lists.newArrayList(debitDataIdFilter, debitApiNameFilter, isDeletedFilter));

            List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                    User.systemUser(tenantId),
                    query,
                    MatchNoteDetailConstants.API_NAME,
                    Lists.newArrayList(MatchNoteDetailConstants.Field.DEBIT_DETAIL_DATA_ID.getApiName(), MatchNoteDetailConstants.Field.DEBIT_DETAIL_API_NAME.getApiName()),
                    "sum",
                    MatchNoteDetailConstants.Field.THIS_MATCH_AMOUNT.getApiName()
            );

            if (!CollectionUtils.isEmpty(data)) {
                for (IObjectData datum : data) {
                    BigDecimal sumAmount = datum.get("sum_" + MatchNoteDetailConstants.Field.THIS_MATCH_AMOUNT.getApiName(), BigDecimal.class);
                    if (!Objects.isNull(sumAmount)) {
                        amount = amount.add(sumAmount);
                    }
                }

            }
        }
        return amount.abs();
    }

    private List<String> accountReceivableDetailIds(String tenantId, String returnGoodsInvoiceId) {
        if (org.apache.commons.lang.StringUtils.isEmpty(returnGoodsInvoiceId)) {
            return Lists.newArrayList();
        }
        Filter sourceDataIdFilter = new Filter();
        sourceDataIdFilter.setFieldName("source_data_id");
        sourceDataIdFilter.setOperator(Operator.EQ);
        sourceDataIdFilter.setFieldValues(Lists.newArrayList(returnGoodsInvoiceId));

        Filter sourceApiNameFilter = new Filter();
        sourceApiNameFilter.setFieldName("source_api_name");
        sourceApiNameFilter.setOperator(Operator.EQ);
        sourceApiNameFilter.setFieldValues(Lists.newArrayList(SystemConstants.ReturnedGoodsInvoiceApiName));

        SearchTemplateQuery stq = NewPaymentUtil.minimumQuery(sourceDataIdFilter, sourceApiNameFilter);
        List<IObjectData> details = NewPaymentUtil.find(
                serviceFacade,
                tenantId,
                SystemConstants.AccountsReceivableDetailApiName,
                stq,
                Lists.newArrayList("_id")
        );
        return details.stream().map(IObjectData::getId).collect(Collectors.toList());
    }
}
