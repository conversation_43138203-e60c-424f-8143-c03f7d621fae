package com.facishare.crm.prm.resource;

import com.facishare.crm.sfa.prm.platform.model.ApiResponse;
import com.facishare.crm.prm.resource.param.MembershipBenefitRequest;
import com.facishare.crm.prm.resource.view.CorporateMembershipView;
import com.facishare.crm.prm.resource.view.IndividualMembershipView;
import com.facishare.crm.prm.resource.view.MembershipBenefitView;
import com.facishare.crm.prm.service.PartnerMembershipService;
import com.facishare.crm.prm.service.model.*;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2024-11-29
 * ============================================================
 */
@Service
@Slf4j
@ServiceModule("partner_loyalty")
public class PartnerLoyaltyResource {
    @Resource
    private PartnerMembershipService partnerMembershipService;

    public PartnerLoyaltyResource(PartnerMembershipService partnerMembershipService) {
        this.partnerMembershipService = partnerMembershipService;
    }

    @ServiceMethod("query_corporate_membership_info")
    public ApiResponse<CorporateMembershipView> queryEnterpriseLoyalty(ServiceContext serviceContext) {
        CorporateMembership corporateMembership = partnerMembershipService.queryCorporateMembership(serviceContext.getUser());
        if (corporateMembership == null) {
            return ApiResponse.success(CorporateMembershipView.builder().build());
        }
        RankingAndPointsModel rankingAndPointsModel = partnerMembershipService.queryCorporateRankingAndPoints(
                serviceContext.getUser(),
                corporateMembership.getPartnerId(),
                corporateMembership.getId());
        if (rankingAndPointsModel.getRankDelta() != null) {
            corporateMembership.setRanking(rankingAndPointsModel.getRankDelta().getCurrentMonthRank());
        }
        CorporateMembershipView corporateMembershipView = CorporateMembershipView.builder()
                .membershipInfo(corporateMembership)
                .pointDelta(rankingAndPointsModel.getPointDelta())
                .rankDelta(rankingAndPointsModel.getRankDelta())
                .build();
        List<MembershipTier> membershipTiers = partnerMembershipService.queryMembershipTiers(serviceContext.getUser(), corporateMembership.getLoyaltyProgramId());
        corporateMembershipView.setMembershipTiers(membershipTiers);
        return ApiResponse.success(corporateMembershipView);
    }

    @ServiceMethod("query_individual_membership_info")
    public ApiResponse<IndividualMembershipView> queryMemberLoyalty(ServiceContext serviceContext) {
        IndividualMembership individualMembership = partnerMembershipService.queryIndividualMembership(serviceContext.getUser());
        if (individualMembership == null) {
            return ApiResponse.success(IndividualMembershipView.builder().build());
        }
        RankingAndPointsModel rankingAndPointsModel = partnerMembershipService.queryIndividualRankingAndPoints(
                serviceContext.getUser(),
                individualMembership.getPartnerId(),
                individualMembership.getId());
        if (rankingAndPointsModel.getRankDelta() != null) {
            individualMembership.setRanking(rankingAndPointsModel.getRankDelta().getCurrentMonthRank());
        }
        IndividualMembershipView individualMembershipView = IndividualMembershipView.builder()
                .membershipInfo(individualMembership)
                .pointDelta(rankingAndPointsModel.getPointDelta())
                .rankDelta(rankingAndPointsModel.getRankDelta())
                .build();
        List<MembershipTier> membershipTiers = partnerMembershipService.queryMembershipTiers(serviceContext.getUser(), individualMembership.getLoyaltyProgramId());
        individualMembershipView.setMembershipTiers(membershipTiers);
        return ApiResponse.success(individualMembershipView);
    }

    @ServiceMethod("query_membership_benefits")
    public ApiResponse<MembershipBenefitView> queryMembershipBenefits(ServiceContext serviceContext, MembershipBenefitRequest request) {
        List<MembershipTier> membershipTiers = partnerMembershipService.queryMembershipBenefits(serviceContext.getUser(), request.getLoyaltyMemberId());
        List<MembershipBenefit> allBenefits = partnerMembershipService.collectUniqueBenefits(membershipTiers);
        MembershipBenefitView benefitView = MembershipBenefitView.builder()
                .membershipBenefits(membershipTiers)
                .allBenefits(allBenefits)
                .build();
        return ApiResponse.success(benefitView);
    }
}

