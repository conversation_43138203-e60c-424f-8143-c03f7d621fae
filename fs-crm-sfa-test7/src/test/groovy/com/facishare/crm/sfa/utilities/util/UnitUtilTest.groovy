package com.facishare.crm.sfa.utilities.util

import com.facishare.crm.describebuilder.SelectOneFieldDescribeBuilder
import com.facishare.crm.util.UnitUtil
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.ISelectOption
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.describe.SelectOption
import com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Shared
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([IFieldDescribe, IObjectDescribe])
@SuppressStaticInitializationFor([
        "com.facishare.paas.metadata.api.describe.IFieldDescribe",
        "com.facishare.paas.metadata.api.describe.IObjectDescribe",
        "com.facishare.crm.util.UnitUtil"])
class UnitUtilTest extends Specification {
    @Shared
    def user = new User("84844", "-10000")
    @Shared
    def objectDescribeMock = PowerMockito.mock(IObjectDescribe)

    def cleanupSpec() {
        user = null
        objectDescribeMock = null
    }

    def setupSpec() {
        showMemory()

        PowerMockito.mockStatic(IFieldDescribe)
        PowerMockito.mockStatic(IObjectDescribe)
    }

    def showMemory() {
        Runtime runtime = Runtime.getRuntime()

        long totalMemory = runtime.totalMemory()
        long freeMemory = runtime.freeMemory()
        long usedMemory = totalMemory - freeMemory
        long maxMemory = runtime.maxMemory()

        long toMB = 1024 * 1024

        println "Total Memory: ${totalMemory / toMB} MB"
        println "Free Memory: ${freeMemory / toMB} MB"
        println "Used Memory: ${usedMemory / toMB} MB"
        println "Max Memory: ${maxMemory / toMB} MB"
    }

    def "getUnitOption have options"() {
        given:
//        def user = new User("84844", "-10000")
//        def objectDescribeMock = PowerMockito.mock(IObjectDescribe)
        IFieldDescribe fieldDescribe = getField(true).get(0)
        and: "mock objectDescribe behavior if needed"
        def serviceFacade = PowerMockito.mock(ServiceFacade)

        def objectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.doReturn(objectDescribeMock).when(serviceFacade, "findObject", any(), any())
        PowerMockito.doReturn(fieldDescribe).when(objectDescribe, "getFieldDescribe", any())
        when:
        def result = UnitUtil.getUnitOption(user, objectDescribe, objectApiName, fieldApiName)

        then:
        result != null // 或者具体的预期结果，例如 result == "expectedValue"

        where:

        objectApiName | fieldApiName
        "Product"     | "unit"
        // 可以添加更多测试场景
    }

    def "getUnitOption no options"() {
        given:
//        def user = new User("84844", "-10000")
//        def objectDescribeMock = PowerMockito.mock(IObjectDescribe)
        IFieldDescribe fieldDescribe = getField(false).get(0)
        and: "mock objectDescribe behavior if needed"
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        def objectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.doReturn(objectDescribeMock).when(serviceFacade, "findObject", any(), any())
        PowerMockito.doReturn(fieldDescribe).when(objectDescribe, "getFieldDescribe", any())

        when:
        def result = UnitUtil.getUnitOption(user, objectDescribe, objectApiName, fieldApiName)

        then:
        result != null // 或者具体的预期结果，例如 result == "expectedValue"

        where:

        objectApiName | fieldApiName
        "Product"     | "unit"
        // 可以添加更多测试场景
    }

    def "getUnitOption objectDescribe is null"() {
        given:
//        def user = new User("84844", "-10000")
//        def objectDescribeMock = PowerMockito.mock(IObjectDescribe)
        IFieldDescribe fieldDescribe = getField(false).get(0)
        and: "mock objectDescribe behavior if needed"
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        org.powermock.reflect.Whitebox.setInternalState(UnitUtil, "serviceFacade", serviceFacade)
        def objectDescribe = PowerMockito.mock(IObjectDescribe)
        //PowerMockito.when(serviceFacade.findObject(user.getTenantId(), objectApiName)).thenReturn(objectDescribe);
        PowerMockito.when(serviceFacade.findObject(any(), any())).thenReturn(objectDescribe)
        PowerMockito.doReturn(fieldDescribe).when(objectDescribe, "getFieldDescribe", any())

        when:
        def result = UnitUtil.getUnitOption(user, null, objectApiName, fieldApiName)

        then:
        result != null // 或者具体的预期结果，例如 result == "expectedValue"

        where:

        objectApiName | fieldApiName
        "Product"     | "unit"
        // 可以添加更多测试场景
    }

    def getField(boolean haveOptions) {
        List<ISelectOption> selectOptions = Lists.newArrayList();
        if (haveOptions) {
            SelectOption one = new SelectOption("个", "Electronic", "");
            SelectOption two = new SelectOption("箱", "Paper", "");
            selectOptions.add(one)
            selectOptions.add(two)
        }

        IFieldDescribe fieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName("unit")
                .label("单位")
                .required(false)
                .selectOptions(selectOptions)
                .build();
        def list = Lists.newArrayList()
        list.add(fieldDescribe)
        list
    }
}
