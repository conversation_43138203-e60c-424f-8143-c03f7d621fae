package com.facishare.crm.sfa.predefine.service
//package com.facishare.crm.sfa.test_package.test06
//
import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext
import com.facishare.crm.sfa.predefine.bizvalidator.validator.salesorder.MultiUnitValidator
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService
import com.facishare.crm.util.DomainPluginDescribeExt
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.util.SpringUtil
import com.fxiaoke.functions.utils.Lists
import com.google.common.collect.Maps
import com.sun.imageio.plugins.common.I18N
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared

/**
 * <AUTHOR>
 * @time 2024-04-07 10:31
 * @Description
 */
@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([I18N,SpringUtil])
@SuppressStaticInitializationFor([
        "com.facishare.paas.I18N",
        "com.facishare.paas.metadata.util.SpringUtil"
])
class MultiUnitValidatorTest extends RemoveUseless {
    @Shared
    User user = new User("71568", "-10000")
    def setupSpec() {
        PowerMockito.mockStatic(I18N)
        PowerMockito.mockStatic(SpringUtil)
    }

    def "validate"() {
        given:
        def bizConfigThreadLocalCacheService = Mockito.mock(BizConfigThreadLocalCacheService)
        def multiUnitService = Mockito.mock(MultiUnitService)
        def domainPluginDescribeExt = Mockito.mock(DomainPluginDescribeExt)
        def serviceFacade = Mockito.mock(ServiceFacade)
        Map<String, List<IObjectData>> map = Maps.newHashMap()

        ObjectData objectData = new ObjectData()
        objectData.set("is_multiple_unit", "是")
        objectData.set("actual_unit", "1")
        map.put("1", Lists.newArrayList(objectData))
        ValidatorContext validatorContext = ValidatorContext.builder()
                .user(user)
                .detailObjectData(map)
                .build()

        domainPluginDescribeExt.getDefaultDetailObjectApiName() >> "1"

        ApplicationContext applicationContext=PowerMockito.mock(ApplicationContext)
        PowerMockito.mockStatic(SpringUtil)
        PowerMockito.stub(PowerMockito.method(SpringUtil.class, "getContext"))
                .toReturn(applicationContext)

        def arTester = new MultiUnitValidator(domainPluginDescribeExt)

        org.springframework.test.util.ReflectionTestUtils.setField(arTester, "serviceFacade", serviceFacade)
        org.springframework.test.util.ReflectionTestUtils.setField(arTester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        org.springframework.test.util.ReflectionTestUtils.setField(arTester, "multiUnitService", multiUnitService)

        Mockito.when(bizConfigThreadLocalCacheService.isMultipleUnit(Mockito.any())).thenReturn(true)
        Mockito.when(serviceFacade.findObject(Mockito.any(), Mockito.any())).thenReturn(null)
        Mockito.doNothing().when(multiUnitService).validateQuantityDecimal(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())
        Mockito.doNothing().when(multiUnitService).validateProductUnit(Mockito.any(), Mockito.any(), Mockito.any())

        when:
        def result = arTester.validate(validatorContext)

        then:
        result == null
    }
}