<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
         http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.facishare.crm.sfa"/>
    <context:annotation-config/>
    <import resource="classpath*:/dubborestouterapi/fs-wechat-dubbo-rest-outer-api-client.xml"/>

    <import resource="classpath*:spring/ei-ea-converter.xml"/>

    <import resource="classpath*:/META-INF/spring/pod-api-client.xml"/>
    <import resource="classpath:fs-plat-privilege-api-rest-client.xml"/>
    <import resource="classpath:/META-INF/spring/industry-spring-client.xml"/>
    <import resource="classpath:lto.xml"/>
    <import resource="classpath:spring/spring-muti-thread.xml"/>
    <import resource="classpath:enterpriserelation2/enterpriserelation-verify.xml"/>
    <import resource="classpath:spring/eservice-rest.xml"/>


    <bean id="newopportunityEnginePullMQSender"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
          p:configName="newopportunity-enginepull-mq"/>

    <bean id="locationCalculationMessageSender" class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
          p:configName="fs-crm-mq-location-calculation" init-method="init"/>

    <bean id="accountPathMessageSender" class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
          p:configName="fs-crm-mq-account-path" init-method="init"/>

    <bean id="qywxHistoryDataSyncProducer" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer"
          init-method="start" destroy-method="close">
        <constructor-arg name="configName" value="fs-crm-task-sfa-mq.ini"/>
        <constructor-arg name="sectionNames" value="qywx-history-data-sync-producer"/>
    </bean>

    <bean id="qywxDataSyncProducer" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer"
          init-method="start" destroy-method="close">
        <constructor-arg name="configName" value="fs-crm-task-sfa-mq.ini"/>
        <constructor-arg name="sectionNames" value="sfa-qywx-data-sync-producer"/>
    </bean>

    <bean id="pivotTableProducer" class="com.facishare.crm.sfa.task.PivotTableMQProducer"
          init-method="start" destroy-method="close">
        <constructor-arg name="configName" value="fs-crm-task-sfa-mq.ini"/>
        <constructor-arg name="sectionNames" value="sfa-pivot-table"/>
    </bean>

    <bean id="addProMqMessageSender" class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
          p:configName="fs-crm-add-product-pricebook" init-method="init"/>
    <bean id="addDataCleanMqMessageSender" class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
          p:configName="fs-crm-mq-data-clean-task" init-method="init"/>

    <!--<import resource="spring-muti-thread.xml"/>-->


    <bean id="outHttpClientSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean">
        <property name="configName" value="fs-crm-out-http-support"/>
    </bean>
    <bean class="com.fxiaoke.paas.gnomon.api.NomonProducer"/>
    <aop:aspectj-autoproxy expose-proxy="true"/>

    <dubbo:reference id="qywxDataSyncService" interface="com.facishare.marketing.outapi.service.QywxDataSyncService"
                     version="1.0" retries="0" timeout="15000" lazy="true" check="false"/>

    <bean id="feedsSaleActionProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.FeedsSaleActionProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="licenseParaChange" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer"
          init-method="start" destroy-method="close">
        <constructor-arg name="configName" value="fs-crm-task-sfa-mq.ini"/>
        <constructor-arg name="sectionNames" value="license_para_change"/>
    </bean>
    <bean id="caMQSender" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer">
        <constructor-arg name="configName" value="fs-sail-mq-producer-config"/>
        <constructor-arg name="sectionNames" value="customer-account-producer"/>
    </bean>

    <bean id="fsBiStatProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.predefine.service.GoalValue.rpc.proxy.FsBiStatProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="fsBiPermProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.predefine.service.GoalValue.rpc.proxy.FsBiPermProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="fsBiUiProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.predefine.service.GoalValue.rpc.proxy.FsBiUiProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="getHomePermissionsProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.GetHomePermissionsProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="customerAccountProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.CustomerAccountProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="salesOrderBizProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.SalesOrderBizProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="sailAdminProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.SailAdminProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>


    <bean id="newOpportunityProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.NewOpportunityProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="promotionProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.PromotionProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="qiXinProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.QiXinProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>


    <bean id="salesEventProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.SalesEventProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="saleActionProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.SaleActionProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="refundProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.RefundProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>


    <bean id="crmRemindRecordProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.CRMRemindRecordProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="sfaRecyclingService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.SFARecyclingProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="smsHubProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.SmsHubProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="coordinationProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.CoordinationProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="objectFieldReferenceProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.ObjectFieldReferenceProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="industryEnterInfoProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.IndustryEnterInfoProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="feedsProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.FeedsProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="feedsCRMProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.FeedsCRMProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="feedsSaleActionProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.FeedsSaleActionProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="leadsPerdictoneProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.LeadsPerdictoneProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="aiForecastedWinRateProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.AIForecastedWinRateProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="fsUserMarketingActionProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.FSUserMarketingActionProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="fsOnlineServiceMessagesProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.FSOnlineServiceMessagesProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="paasWorkFlowProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.PaasWorkFlowProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="paasLogServiceProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.PaasLogServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="mergeJobProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.MergeJobProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="paasUserGroupProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.rest.PaasUserGroupProxy">
        <property name="factory" ref="restServiceProxyFactory">
        </property>
    </bean>

    <bean id="paasUserRoleProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.rest.PaasUserRoleProxy">
        <property name="factory" ref="restServiceProxyFactory">
        </property>
    </bean>

    <bean id="ruleEngineProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.predefine.service.pricepolicy.rest.proxy.RuleEngineProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <!-- 自定义服务rest接口 -->
    <bean id="webPageProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.WebPageProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="crmMetaDataProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.CRMMetaDataServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="customProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.CustomProxy">
        <property name="factory" ref="restServiceProxyFactory">
        </property>
    </bean>

    <bean id="aiRestProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.AiRestProxy">
        <property name="factory" ref="restServiceProxyFactory">
        </property>
    </bean>


    <bean id="openAiRestServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-openai-rest-proxy-config" init-method="init"/>

    <bean id="oneFlowClient" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.OneFlowClient">
        <property name="factory" ref="openAiRestServiceProxyFactory">
        </property>
    </bean>

    <!-- Bi Crm Rest -->
    <bean id="biRestServiceProxyFactorySFA" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-bi-rest-proxy" init-method="init"/>
    <bean id="biCrmRestProxySFA" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.BiCrmRestProxy">
        <property name="factory" ref="biRestServiceProxyFactorySFA"/>
    </bean>

    <bean id="loyaltyMq" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer"
          init-method="start" destroy-method="close">
        <constructor-arg name="configName" value="fs-crm-task-sfa-mq.ini"/>
        <constructor-arg name="sectionNames" value="loyalty-member"/>
    </bean>
</beans>
