{"components": [{"field_section": [{"form_fields": [{"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "product_id"}, {"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "core_version"}, {"is_readonly": false, "is_required": true, "render_type": "select_one", "field_name": "category"}, {"is_readonly": false, "is_required": true, "render_type": "select_one", "field_name": "purpose"}, {"is_readonly": false, "is_required": true, "render_type": "select_one", "field_name": "sale_strategy"}, {"is_readonly": false, "is_required": false, "render_type": "long_text", "field_name": "remark"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "is_show": true}, {"form_fields": [{"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": false, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": false, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}], "api_name": "sysinfo_section__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "is_show": true}], "api_name": "form_component", "type": "form", "buttons": []}], "buttons": [], "package": "CRM", "api_name": "BomCoreObj_layout_generate_by_UDObjectServer__c", "ref_object_api_name": "BomCoreObj", "layout_type": "detail", "display_name": "默认布局", "layout_description": "产品组合默认布局", "is_default": true, "api_version": 1.0}