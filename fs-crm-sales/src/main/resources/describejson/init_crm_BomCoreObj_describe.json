{"fields": {"_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "_id", "api_name": "_id", "description": "_id", "status": "released", "is_extend": false}, "name": {"prefix": "{yyyy}{mm}{dd}-", "auto_adapt_places": false, "description": "", "is_unique": true, "start_number": 1, "type": "auto_number", "is_required": true, "define_type": "system", "postfix": "", "is_single": false, "is_index": true, "is_active": true, "auto_number_type": "normal", "is_encrypted": false, "serial_number": 6, "default_value": "01", "label": "编号", "condition": "NONE", "is_need_convert": false, "api_name": "name", "is_index_field": false, "help_text": "", "status": "released"}, "product_id": {"default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "wheres": [], "is_required": true, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 256, "is_index": true, "is_active": true, "is_encrypted": false, "target_api_name": "ProductObj", "label": "父项产品", "target_related_list_name": "bom_core_product_list", "target_related_list_label": "产品组合", "action_on_target_delete": "cascade_delete", "related_wheres": [], "api_name": "product_id", "is_index_field": true, "status": "new", "help_text": ""}, "product_code": {"auto_adapt_places": false, "quote_field_type": "text", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "quote", "quote_field": "product_id__r.product_code", "is_required": false, "define_type": "package", "is_single": false, "is_index": false, "is_active": true, "is_encrypted": false, "default_value": "", "label": "产品编码", "api_name": "product_code", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "released"}, "core_version": {"prefix": "b{yyyy}{mm}{dd}-v", "auto_adapt_places": false, "description": "", "is_unique": true, "start_number": 1, "type": "auto_number", "is_required": true, "define_type": "package", "postfix": "", "is_single": false, "is_index": true, "is_active": true, "auto_number_type": "normal", "is_encrypted": false, "serial_number": 6, "default_value": "b{yyyy}{mm}{dd}-v000001", "label": "BOM版本", "condition": "NONE", "is_need_convert": false, "api_name": "core_version", "is_index_field": false, "help_text": "", "status": "released"}, "category": {"is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "configure", "label": "BOM类型", "type": "select_one", "is_need_convert": false, "is_required": true, "api_name": "category", "options": [{"label": "配置BOM", "value": "configure"}], "define_type": "package", "is_single": false, "is_index_field": false, "help_text": "", "status": "released"}, "sale_strategy": {"is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "whole", "label": "售卖方式", "type": "select_one", "is_need_convert": false, "is_required": true, "api_name": "sale_strategy", "options": [{"label": "按整套价格折扣售卖", "value": "whole"}], "define_type": "package", "is_single": false, "is_index_field": false, "help_text": "", "status": "released"}, "purpose": {"is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "sale", "label": "BOM用途", "type": "select_one", "is_need_convert": false, "is_required": true, "api_name": "purpose", "options": [{"label": "销售BOM", "value": "sale"}, {"label": "服务BOM", "value": "service"}], "define_type": "package", "is_single": false, "is_index_field": false, "help_text": "", "status": "released"}, "remark": {"expression_type": "long_text", "default_is_expression": false, "is_index": true, "is_active": true, "pattern": "", "is_unique": false, "default_value": "", "label": "备注", "type": "long_text", "default_to_zero": false, "is_required": false, "api_name": "remark", "define_type": "package", "help_text": "", "max_length": 256, "status": "released", "is_extend": false}, "erp_version": {"default_is_expression": false, "is_index": true, "is_active": true, "pattern": "", "is_unique": false, "default_value": "", "label": "ERP版本号", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "erp_version", "define_type": "package", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "owner": {"is_index": true, "is_active": true, "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_single": true, "help_text": "", "status": "released", "is_extend": false}, "lock_status": {"is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "help_text": "锁定状态", "status": "released", "is_extend": false}, "lock_rule": {"is_index": true, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "help_text": "锁定规则", "is_extend": false}, "lock_user": {"is_index": true, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "help_text": "加锁人", "is_extend": false}, "life_status": {"is_index": true, "is_active": true, "description": "生命状态", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "help_text": "生命状态", "status": "released", "is_extend": false}, "life_status_before_invalid": {"is_index": true, "is_active": true, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "help_text": "作废前生命状态", "max_length": 256, "is_extend": false}, "owner_department": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "default_value": "", "label": "负责人所在部门", "type": "text", "default_to_zero": false, "is_need_convert": false, "is_required": false, "api_name": "owner_department", "define_type": "package", "is_single": true, "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "relevant_team": {"embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "help_text": "相关团队", "is_extend": false}, "record_type": {"is_index": false, "is_active": true, "description": "业务类型", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "help_text": "", "status": "released", "is_extend": false}, "created_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "创建人", "api_name": "created_by", "description": "创建人", "status": "released", "is_extend": false}, "last_modified_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "最后修改人", "api_name": "last_modified_by", "description": "最后修改人", "status": "released", "is_extend": false}, "package": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "package", "api_name": "package", "description": "package", "status": "released", "is_extend": false}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released", "is_extend": false}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released", "is_extend": false}, "out_owner": {"is_index": false, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "创建时间", "status": "released", "is_extend": false}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "最后修改时间", "status": "released", "is_extend": false}, "extend_obj_data_id": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "extend_obj_data_id", "default_value": "", "type": "text", "label": "extend_obj_data_id", "default_to_zero": false, "is_required": false, "api_name": "extend_obj_data_id", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}}, "validate_rules": {}, "triggers": {}, "actions": {}, "index_version": 1, "api_name": "BomCoreObj", "display_name": "产品组合", "package": "CRM", "define_type": "package", "is_active": true, "store_table_name": "biz_bom_core", "is_deleted": false}