{"fields": {"object_api_name": {"is_unique": false, "define_type": "package", "is_required": false, "is_index": true, "is_active": true, "status": "released", "api_name": "object_api_name", "label": "对象名称", "type": "text", "max_length": 100}, "object_id": {"is_unique": false, "define_type": "package", "is_required": false, "is_index": true, "is_active": true, "status": "released", "api_name": "object_id", "label": "对象id", "type": "text", "max_length": 100}, "accounts_receivable_quick_rule_id": {"is_unique": false, "define_type": "package", "is_required": false, "is_index": true, "is_active": true, "status": "released", "api_name": "accounts_receivable_quick_rule_id", "label": "快捷应收规则id", "type": "text", "max_length": 100}, "accounts_receivable_note_id": {"is_unique": false, "define_type": "package", "is_required": false, "is_index": true, "is_active": true, "status": "released", "api_name": "accounts_receivable_note_id", "label": "应收单id", "type": "text", "max_length": 100}, "is_completed": {"is_index": false, "is_active": true, "pattern": "", "is_unique": false, "label": "是否完成", "type": "true_or_false", "field_num": null, "is_need_convert": false, "is_required": false, "api_name": "is_completed", "define_type": "package", "is_index_field": false, "is_single": false, "default_value": false, "status": "released", "options": [{"label": "否", "value": false}, {"label": "是", "value": true}]}, "_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "_id", "api_name": "_id", "description": "_id", "status": "released", "is_extend": false}, "tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "tenant_id", "api_name": "tenant_id", "description": "tenant_id", "status": "released", "is_extend": false}, "created_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "released", "label": "创建人", "is_active": true, "index_name": "crt_by"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released"}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "released", "is_active": true, "index_name": "md_by", "label": "最后修改人"}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "apiName", "is_active": true, "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released"}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released"}}, "actions": {}, "index_version": 1, "api_name": "ObjectArRuleRelatedObj", "display_name": "应收规则关联表", "package": "CRM", "is_active": true, "plural_name": null, "define_type": "internal", "is_deleted": false, "description": null, "visible_scope": null, "store_table_name": "biz_object_ar_rule_related"}