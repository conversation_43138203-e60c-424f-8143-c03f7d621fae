{"fields": {"data_id": {"is_index": true, "is_active": true, "is_unique": false, "label": "关联数据", "type": "text", "is_abstract": null, "is_required": true, "api_name": "data_id", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "max_length": 100, "status": "released"}, "data_object_describe_api_name": {"is_index": true, "is_active": true, "is_unique": false, "label": "关联数据apiName", "type": "text", "is_abstract": null, "is_required": true, "api_name": "data_object_describe_api_name", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "max_length": 100, "status": "released"}, "ref_data_id": {"is_index": true, "is_active": true, "is_unique": false, "label": "关联属性/非标属性", "type": "text", "is_abstract": null, "is_required": false, "api_name": "ref_data_id", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "max_length": 100, "status": "released"}, "ref_object_describe_api_name": {"is_index": true, "is_active": true, "is_unique": false, "label": "关联属性/非标属性apiName", "type": "text", "is_abstract": null, "is_required": false, "api_name": "ref_object_describe_api_name", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "max_length": 100, "status": "released"}, "ref_data_no": {"is_index": true, "is_active": true, "is_unique": false, "label": "关联属性/非标属性序号", "type": "number", "is_abstract": null, "is_required": false, "api_name": "ref_data_no", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "max_length": 100, "status": "released"}, "attr_group_id": {"is_index": true, "is_active": true, "is_unique": false, "label": "属性分组", "type": "text", "is_abstract": null, "is_required": false, "api_name": "attr_group_id", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "max_length": 100, "status": "released"}, "attr_group_no": {"is_index": true, "is_active": true, "is_unique": false, "label": "属性分组序号", "type": "number", "is_abstract": null, "is_required": false, "api_name": "attr_group_no", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "max_length": 100, "status": "released"}, "tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "tenant_id", "api_name": "tenant_id", "description": "tenant_id", "status": "released", "is_extend": false}, "created_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "released", "label": "创建人", "is_active": true, "index_name": "crt_by"}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "released", "is_active": true, "index_name": "md_by", "label": "最后修改人"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released"}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "is_active": true, "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released"}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released"}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released"}}, "actions": {}, "index_version": 1, "is_udef": false, "api_name": "AttributeRangeObj", "display_name": "属性范围", "package": "CRM", "is_active": true, "define_type": "internal", "is_deleted": false, "store_table_name": "biz_attribute_range"}