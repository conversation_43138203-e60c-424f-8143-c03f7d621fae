package com.facishare.crm.sfa.predefine.service.proposal.dto.request;

import com.facishare.crm.platform.annotation.Convertible;
import lombok.Data;

import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-20
 * ============================================================
 */
@Data
@Convertible
public class QuickCreateCaseOverviewRequest {
    private String relatedObjectDescribe;
    private String relatedDataId;
    private List<Object> promptAttachment;
}
