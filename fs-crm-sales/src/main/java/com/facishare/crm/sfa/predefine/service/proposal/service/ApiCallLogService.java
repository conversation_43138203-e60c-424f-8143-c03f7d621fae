package com.facishare.crm.sfa.predefine.service.proposal.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.platform.AssertValidator;
import com.facishare.crm.platform.converter.Converter;
import com.facishare.crm.sfa.predefine.service.proposal.access.ApiCallLogAccess;
import com.facishare.crm.sfa.predefine.service.proposal.dto.ApiCallLogDTO;
import com.facishare.crm.sfa.predefine.service.proposal.dto.UserInfoDTO;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.ApiCallLogRequest;
import com.facishare.crm.sfa.predefine.service.proposal.vo.ApiCallRecordListVO;
import com.facishare.crm.sfa.prm.platform.model.ContextMQ;
import com.facishare.crm.sfa.task.AsyncTaskProducer;
import com.facishare.crm.util.Safes;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.service.dto.UserInfoExt;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_PARAMET_ERERROR;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Service
@Slf4j
public class ApiCallLogService {
    @Resource
    private ApiCallLogAccess apiCallLogAccess;
    @Resource(name = "objectConverter")
    private Converter converter;
    @Resource
    private AsyncTaskProducer asyncTaskProducer;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource(name = "orgServiceDirect")
    private OrgService orgService;
    @Resource
    private AssertValidator assertValidator;

    private static final String API_CALL_LOG = "api_call_log";


    public void saveLog(User user, ApiCallLogRequest logRequest) {
        ApiCallLogDTO apiCallLogDTO = converter.convertDTO(logRequest, ApiCallLogDTO.class);
        calculateDurationMs(apiCallLogDTO);
        calculateCostUnit(apiCallLogDTO);
        resolveArtifactMeta(apiCallLogDTO);
        this.create(user, apiCallLogDTO);
    }

    private void resolveArtifactMeta(ApiCallLogDTO apiCallLogDTO) {
        if (apiCallLogDTO.getArtifactMeta() != null) {
            return;
        }
    }

    private void calculateCostUnit(ApiCallLogDTO apiCallLogDTO) {
        if (apiCallLogDTO.getCostUnits() != null) {
            return;
        }
        apiCallLogDTO.setCostUnits(2);
    }


    private void calculateDurationMs(ApiCallLogDTO apiCallLogDTO) {
        if (apiCallLogDTO.getDurationMs() != null) {
            return;
        }
        Long requestTime = apiCallLogDTO.getRequestTime();
        Long responseTime = apiCallLogDTO.getResponseTime();
        if (requestTime == null && responseTime == null) {
            apiCallLogDTO.setDurationMs(0L);
        }
        if (requestTime == null || responseTime == null) {
            throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
        }
        if (requestTime > responseTime) {
            throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
        }
        apiCallLogDTO.setDurationMs(responseTime - requestTime);
    }

    public void create(User user, ApiCallLogDTO thirdPartyCallLog) {
        IObjectData objectData = converter.convertObjectData(thirdPartyCallLog);
        objectData.setOwner(Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
        apiCallLogAccess.create(user, objectData);
    }

    public void callLog(User user, ApiCallLogRequest logRequest) {
        assertValidator.assertAllNotBlank(logRequest.getRequestId(), logRequest.getPlatform(), logRequest.getBizSource(),
                logRequest.getSourceType(), logRequest.getSourceId());
        if (Boolean.TRUE.equals(logRequest.getHasArtifact())) {
            assertValidator.assertAllNotBlank(logRequest.getArtifactType(), logRequest.getArtifactPath());
        }

        ContextMQ<ApiCallLogRequest> contextMQ = new ContextMQ<>();
        contextMQ.setPayload(logRequest);
        contextMQ.setTenantId(user.getTenantId());
        contextMQ.setUserId(user.getUpstreamOwnerIdOrUserId());
        String messageBody = JSON.toJSONString(contextMQ);
        String form = "%s@third-party%s";
        String messageKey = String.format(form, user.getTenantId(), user.getUpstreamOwnerIdOrUserId());
        log.info("ApiCallLogService#asyncSaveLog by send rocketmq message {}", messageBody);
        asyncTaskProducer.create(API_CALL_LOG, messageBody, messageKey);
    }

    public List<ApiCallRecordListVO> listByBiz(User user, String bizSource, String platform) {
        if (StringUtils.isAnyBlank(bizSource, platform)) {
            throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
        }
        List<IObjectData> dataList = apiCallLogAccess.listByBiz(user, bizSource, platform);
        Set<String> owners = dataList.stream()
                .map(IObjectData::getOwner)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toSet());
        Map<String, UserInfoDTO> userInfoMapping = queryUserInfo(user, owners);
        List<ApiCallRecordListVO> apiCallRecordListVOList = Lists.newArrayList();
        dataList.forEach(data -> {
            ApiCallRecordListVO vo = converter.convertDTO(data, ApiCallRecordListVO.class);
            apiCallRecordListVOList.add(vo);
            String owner = Safes.first(data.getOwner());
            UserInfoDTO infoDTO = userInfoMapping.get(owner);
            if (infoDTO == null) {
                return;
            }
            vo.setUserName(infoDTO.getUserName());
            vo.setDepartmentName(infoDTO.getDepartmentName());
        });
        return apiCallRecordListVOList;
    }

    private Map<String, UserInfoDTO> queryUserInfo(User user, Set<String> owners) {
        owners.removeIf(User.SUPPER_ADMIN_USER_ID::equals);
        Map<String, UserInfoDTO> userInfoMapping = Maps.newHashMap();
        UserInfoDTO systemUserInfo = new UserInfoDTO();
        systemUserInfo.setUserName(I18N.text(I18NKey.SYSTEM));
        userInfoMapping.put(User.SUPPER_ADMIN_USER_ID, systemUserInfo);
        if (CollectionUtils.isEmpty(owners)) {
            return userInfoMapping;
        }
        List<UserInfoExt> userInfos = serviceFacade.getUserExtByIds(user.getTenantId(), User.SUPPER_ADMIN_USER_ID, Lists.newArrayList(owners));
        if (CollectionUtils.isEmpty(userInfos)) {
            return userInfoMapping;
        }
        Set<String> departments = userInfos.stream().map(UserInfoExt::getDept).filter(Objects::nonNull).collect(Collectors.toSet());
        List<QueryDeptInfoByDeptIds.DeptInfo> allDeptInfoNameByIds = orgService.getAllDeptInfoNameByIds(user.getTenantId(), User.SUPPER_ADMIN_USER_ID, Lists.newArrayList(departments));
        Map<String, String> departmentNameMapping = allDeptInfoNameByIds.stream().collect(Collectors.toMap(QueryDeptInfoByDeptIds.DeptInfo::getDeptId, QueryDeptInfoByDeptIds.DeptInfo::getDeptName, (k1, k2) -> k1));
        for (UserInfoExt userInfo : userInfos) {
            UserInfoDTO temp = new UserInfoDTO();
            temp.setUserName(userInfo.getName());
            temp.setDepartmentName(departmentNameMapping.get(userInfo.getDept()));
            userInfoMapping.put(userInfo.getId(), temp);
        }
        return userInfoMapping;
    }
}
