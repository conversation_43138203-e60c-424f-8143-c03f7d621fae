package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.SalesOrderUtil;
import com.facishare.crm.sfa.utilities.util.i18n.SalesOrderI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/10/31 20:05
 * @instruction
 */
public class SalesOrderUpdateImportDataAction extends StandardUpdateImportDataAction {
    protected final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    List<String> orderIds = Lists.newArrayList();
    Map<String, Map> salesOrderAccount = Maps.newHashMap();
    Map<String, Map> ineffectiveOrUnderReview = Maps.newHashMap();
    Map<String, Map> normalOrLogistics = Maps.newHashMap();
    List<String> noUpdateDataIds = Lists.newArrayList();
    Map<String, Integer> rowNoAndOrderId = Maps.newHashMap();
    StringBuilder opportunityWheres = new StringBuilder();
    private boolean hasOrderManagerRole = false;
    private boolean hasFinanceRole = false;
    private boolean isCrmAdmin = false;

    @Override
    protected void before(Arg arg) {
        checkRole();
        super.before(arg);
        isCrmAdmin = isCrmAdmin(actionContext.getUser());

    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        dataList.stream().forEach(o -> {
            String id = o.getData().getId();
            if(StringUtils.isNotEmpty(id)){
                orderIds.add(id);
            }

            String accountId = o.getData().get("account_id", String.class);
            String opportunityId = o.getData().get("opportunity_id",String.class);
            if(StringUtils.isNotEmpty(opportunityId)){
                opportunityWheres.append("(account_id='").append(accountId).append("' and id='").append(opportunityId).append("') or ");
            }
        });

        if (CollectionUtils.notEmpty(orderIds)) {
            validateAccount();
            checkWarehouse(dataList);
            // crm管理员 不做任何的校验
            if (isCrmAdmin) {
                return;
            }
            filterDataByLifeAndLogisticsStatus();
            validateLifeAndLogisticsStatus();
        }
    }

    private void checkWarehouse(List<ImportData> dataList) {
        List<ImportError> errorlist = Lists.newArrayList();
        dataList.forEach(o -> {
            String orderId = o.getData().getId();
            String shippingWarehouseId = o.getData().get("shipping_warehouse_id", String.class, "");
            if(StringUtils.isNotBlank(shippingWarehouseId)){
                Map orderObj = salesOrderAccount.get(orderId);
                if (Objects.nonNull(orderObj)) {
                    Object lifeStatus = orderObj.get(ObjectLifeStatus.LIFE_STATUS_API_NAME);
                    if (Objects.nonNull(lifeStatus)&&Objects.equals(lifeStatus.toString(),ObjectLifeStatus.NORMAL.getCode())) {
                        errorlist.add(new ImportError(o.getRowNo(), I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_ORDER_WAREHOUSE_NOT_UPDATE_WARN)));
                    }
                }
            }
        });
        mergeErrorList(errorlist);
    }

    private Map<String, List<String>> validateOpportunity(StringBuilder opportunityWheres){
        Map<String, List<String>> accountAndOpportunity = Maps.newHashMap();
        String wheres = opportunityWheres.toString();
        if(StringUtils.isEmpty(wheres)){
            return accountAndOpportunity;
        }
        String sql = "select * from biz_opportunity  where tenant_id = '%s' and (%s)";
        if(null != wheres && wheres.length() > 0){
            String querySql = wheres.substring(0, wheres.length() - 3);
            querySql = String.format(sql, SqlEscaper.pg_escape(actionContext.getTenantId()),querySql);
            accountAndOpportunity = SalesOrderUtil.validateOpportunity(querySql, actionContext.getTenantId());
        }
        return accountAndOpportunity;
    }

    private void validateAccount() {
        List<ImportError> errorlist = Lists.newArrayList();
        Map<String, List<String>> dbOpportunity = validateOpportunity(opportunityWheres);
        salesOrderAccount = SalesOrderUtil.getSalesOrderAccount(actionContext.getTenantId(), orderIds);
        dataList.forEach(o -> {
            String orderId = o.getData().getId();
            String opportunityId = o.getData().get("opportunity_id",String.class);
            rowNoAndOrderId.put(orderId, o.getRowNo());
            if (salesOrderAccount.containsKey(orderId)) {
                String dbAccountId = salesOrderAccount.get(orderId).getOrDefault("account_id","").toString();
                if (!Objects.equals(o.getData().get("account_id", String.class), dbAccountId) && o.containsField("account_id")) {
                    errorlist.add(new ImportError(o.getRowNo(), I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_ACCOUNT_NAME_NOT_ALLOWED_UPDATE)));
                }else{
                    // 商机不为空的情况下
                    if(StringUtils.isNotEmpty(opportunityId) && CollectionUtils.notEmpty(dbOpportunity)){
                        // 校验商机
                        if(!dbOpportunity.containsKey(dbAccountId)){
                            errorlist.add(new ImportError(o.getRowNo(), I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_ACCOUNT_DONT_HAS_OPPORTUNITY)));
                        }else{
                            List<String> opportunityIds = dbOpportunity.get(dbAccountId);
                            if(!opportunityIds.contains(opportunityId)){
                                errorlist.add(new ImportError(o.getRowNo(), I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_ACCOUNT_DONT_HAS_OPPORTUNITY)));
                            }
                        }
                    }
                }
            } else {
                errorlist.add(new ImportError(o.getRowNo(), I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_NAME_FAIL)));
            }
        });
        mergeErrorList(errorlist);
    }

    private void filterDataByLifeAndLogisticsStatus() {
        List<ImportError> errorlist = Lists.newArrayList();
        salesOrderAccount.forEach((k, v) -> {
            String lifeStatus = v.get("life_status").toString();
            String orderId = v.get("id").toString();
            String logisticsStatus = v.getOrDefault("logistics_status","1").toString();
            if (judgeStatus(lifeStatus)) {
                ineffectiveOrUnderReview.put(orderId, v);
            } else {
                if (judgeLogisticsStatus(logisticsStatus, lifeStatus)) {
                    normalOrLogistics.put(orderId, v);
                } else {
                    noUpdateDataIds.add(orderId);
                }
            }
        });
        dataList.forEach(o -> {
            String orderId = o.getData().getId();
            String shippingWarehouseId = o.getData().get("shipping_warehouse_id", String.class, "");
            if(StringUtils.isNotBlank(shippingWarehouseId)){
                Map orderObj = salesOrderAccount.get(orderId);
                if (Objects.nonNull(orderObj)) {
                    Object lifeStatus = orderObj.get(ObjectLifeStatus.LIFE_STATUS_API_NAME);
                    if (Objects.nonNull(lifeStatus)&&Objects.equals(lifeStatus.toString(),ObjectLifeStatus.NORMAL.getCode())) {
                        errorlist.add(new ImportError(o.getRowNo(), I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_ORDER_WAREHOUSE_NOT_UPDATE_WARN)));
                    }
                }
            }
            if (noUpdateDataIds.contains(orderId)) {
                errorlist.add(new ImportError(o.getRowNo(), I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_ORDER_STATUS_NOT_UPDATE)));
            }
        });
        mergeErrorList(errorlist);
    }

    /**
     * 校验订单的生命状态和发货状态
     * 1。生命状态 = 正常
     * 2。发货状态= 代发货/已发货/已收货
     * 3。当前人是 订单管理员/订单财务，并且设置了审批部们
     */
    private void validateLifeAndLogisticsStatus() {
        if (bizConfigThreadLocalCacheService.isEnlargeEditPrivilege(actionContext.getTenantId())) {
            return ;
        }
        List<ImportError> errorList = Lists.newArrayList();
        List<Integer> ownerIds = SalesOrderUtil.getApprovalUser(actionContext.getUser(), hasOrderManagerRole, hasFinanceRole);
        // 没人
        if (CollectionUtils.empty(ownerIds)) {
            normalOrLogistics.forEach((k, v) -> {
                Integer rowNo = rowNoAndOrderId.get(k);
                errorList.add(new ImportError(rowNo, I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_ORDER_MF_NOT_UPDATE)));
            });
        } else {
            //normalOrLogistics 负责人在集合里面的人的数据允许修改
            normalOrLogistics.forEach((k, v) -> {
                Integer owner = ObjectDataExt.of(v).getOwnerIdInt();
                // 表示不允许更新
                if (!ownerIds.contains(owner)) {
                    Integer rowNo = rowNoAndOrderId.get(k);
                    errorList.add(new ImportError(rowNo, I18N.text(SalesOrderI18NKeyUtil.SFA_SALES_ORDER_NOT_IN_APPROVE_DEPARTMENT)));
                }
            });
        }
        mergeErrorList(errorList);
    }

    private boolean judgeStatus(String status) {
        return (status.equals("ineffective") || status.equals("under_review"));
    }

    private boolean judgeLogisticsStatus(String logisticsStatus, String lifeStatus) {
        return (Objects.equals(logisticsStatus, "1")
                || Objects.equals(logisticsStatus, "3")
                || Objects.equals(logisticsStatus, "5"))
                && Objects.equals(lifeStatus, "normal");
    }

    private void checkRole() {
        List<String> roleCodes = serviceFacade.getUserRole(actionContext.getUser());
        // 订单管理员
        if (CollectionUtils.notEmpty(roleCodes) && roleCodes.contains("00000000000000000000000000000016")) {
            hasOrderManagerRole = true;
        }
        // 订单财务
        if (CollectionUtils.notEmpty(roleCodes) && roleCodes.contains("00000000000000000000000000000003")) {
            hasFinanceRole = true;
        }
    }

    protected Boolean isCrmAdmin(User user){
        return user.getIsCrmAdmin().orElseGet(() -> serviceFacade.isAdmin(user));
    }
}

