package com.facishare.crm.sfa.predefine.service.proposal.llm;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.platform.AssertValidator;
import com.facishare.crm.sfa.predefine.service.GoalValue.utilities.SearchUtil;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.proposal.service.CaseOverviewSourceService;
import com.facishare.crm.sfa.predefine.service.proposal.vo.CaseOverviewSourceVO;
import com.facishare.crm.sfa.predefine.service.proposal.vo.FieldVO;
import com.facishare.crm.sfa.prm.api.enhancer.DescribeEnhancer;
import com.facishare.crm.sfa.utilities.proxy.AiRestProxy;
import com.facishare.crm.sfa.utilities.proxy.model.AiRestProxyModel;
import com.facishare.crm.util.Safes;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.proposal.dto.ProposalGeneratorI18N.CASE_OVERVIEW_SOURCE_NOT_EXISTS;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-25
 * ============================================================
 */
@Service
@Slf4j
public class CaseOverviewLLMService {
    @Resource(name = "aiRestProxy")
    private AiRestProxy aiRestProxy;
    @Resource
    private CaseOverviewSourceService caseOverviewSourceService;
    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Resource
    private AssertValidator assertValidator;
    @Resource
    private DescribeEnhancer describeEnhancer;

    public String generate(User user, String objectApiName, String id) {
        CaseOverviewSourceVO caseOverviewSourceVO = caseOverviewSourceService.queryCaseOverviewSourceByApiName(user, objectApiName);
        assertValidator.assertNotNull(caseOverviewSourceVO);
        if (CollectionUtils.isEmpty(caseOverviewSourceVO.getRelatedFields())) {
            throw new ValidateException(I18N.text(CASE_OVERVIEW_SOURCE_NOT_EXISTS));
        }
        AiRestProxyModel.Arg arg = new AiRestProxyModel.Arg();
        arg.setApiName("prompt_case_overview");
        Map<String, Object> map = assembleSceneVariables(user, id, objectApiName, caseOverviewSourceVO.getRelatedFields());
        arg.setSceneVariables(map);
        AiRestProxyModel.Resposne response = aiRestProxy.completions(arg, AiRestProxy.getHeaders(user.getTenantId()));
        if (ObjectUtils.isEmpty(response) || response.getErrCode() != 0) {
            log.error("CaseOverviewLLMService aiRestProxy.completions response :{}", JSONObject.toJSONString(response));
            return "";
        }
        return response.getResult().getMessage();
    }

    private Map<String, Object> assembleSceneVariables(User user, String id, String objectApiName, List<FieldVO> relatedFields) {
        Set<String> fields = relatedFields.stream().map(FieldVO::getFieldApiName).collect(Collectors.toSet());
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        SearchUtil.fillFilterEq(query.getFilters(), DBRecord.ID, id);
        List<IObjectData> dataList = metaDataFindServiceExt.findBySearchQueryWithFields(user, objectApiName, query, Lists.newArrayList(fields), false).getData();
        IObjectData data = Safes.first(dataList);
        assertValidator.assertNotNull(data);
        IObjectDescribe describe = describeEnhancer.fetchObject(user, objectApiName);
        assertValidator.assertNotNullDescribe(describe, objectApiName);

        StringBuilder builder = new StringBuilder();
        for (FieldVO field : relatedFields) {
            String label = field.getLabel();
            Object value = data.get(field.getFieldApiName());
            String stringValue = (value != null) ? value.toString() : "NULL";
            builder.append("【").append(label).append("】：").append(stringValue).append("  \n");
        }
        String sceneValue = builder.toString();
        Map<String, Object> sceneVariablesMapping = new HashMap<>(1);
        sceneVariablesMapping.put("caseOverviewSource", sceneValue);
        sceneVariablesMapping.put("objectName", describe.getDisplayName());
        return sceneVariablesMapping;
    }
}
