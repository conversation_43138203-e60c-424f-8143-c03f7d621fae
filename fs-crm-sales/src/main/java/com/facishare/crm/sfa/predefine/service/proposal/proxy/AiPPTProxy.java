package com.facishare.crm.sfa.predefine.service.proposal.proxy;

import com.facishare.crm.sfa.predefine.service.proposal.dto.GrantCodeData;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_SYS_ERROR_MSG;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-19
 * ============================================================
 */
@Service
@Slf4j
public class AiPPTProxy {
    @Resource(name = "httpClientSupport")
    private OkHttpSupport okHttpSupport;

    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false); // 宽容模式
    }

    private static final String GRANT_URL_FORMAT = "https://co.aippt.cn/api/grant/code?uid=%s&type=&channel=%s";
    public static final String CHANNEL = "proposal";

    public GrantCodeData grant(User user, String signature, String apiKey, long epochSecond) {
        String uid = user.getTenantId() + user.getUpstreamOwnerIdOrUserId();
        String url = String.format(GRANT_URL_FORMAT, uid, CHANNEL);
        Headers headers = new Headers.Builder().add("x-api-key", apiKey)
                .add("x-timestamp", String.valueOf(epochSecond))
                .add("x-signature", signature).build();
        Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .build();
        Object responseObject = okHttpSupport.syncExecute(request, new SyncCallback() {
            @Override
            public Object response(Response response) throws Exception {
                if (!response.isSuccessful() || response.body() == null) {
                    log.error("AiPPTProxy#grant-response error, response:{}, responseCode:{}", response, response.code());
                    throw new ValidateException(I18N.text(SFA_SYS_ERROR_MSG));
                }
                String json = response.body().string();
                JavaType type = mapper.getTypeFactory()
                        .constructParametricType(AiPPTResponse.class, GrantCodeData.class);
                return mapper.readValue(json, type);
            }

            @Override
            public void onFailure(Call call, IOException e) {
                log.error("AiPPTProxy#grant-onFailure", e);
                throw new ValidateException(I18N.text(SFA_SYS_ERROR_MSG));
            }
        });
        AiPPTResponse<GrantCodeData> response = (AiPPTResponse<GrantCodeData>) responseObject;
        return response.getData();

    }

}
