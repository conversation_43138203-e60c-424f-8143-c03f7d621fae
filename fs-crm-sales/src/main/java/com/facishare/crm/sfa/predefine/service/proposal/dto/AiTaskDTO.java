package com.facishare.crm.sfa.predefine.service.proposal.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-26
 * ============================================================
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.ALWAYS)
public class AiTaskDTO {
    private String status;
    private Long duration;
}
