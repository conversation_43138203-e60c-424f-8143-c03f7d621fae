package com.facishare.crm.sfa.predefine.service.proposal.access;

import com.facishare.crm.sfa.cache.RedisDataAccessor;
import com.facishare.crm.sfa.predefine.service.proposal.dto.AiTaskDTO;
import com.facishare.crm.sfa.predefine.service.proposal.enums.AiTaskStatus;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-20
 * ============================================================
 */
@Component
@Slf4j
public class GeneratorCacheAccess {
    /**
     * ppt 鉴权结果缓存
     */
    private static final String TOKEN_CACHE_KEY_FORMAT = "sfa:proposal:%s:string";
    /**
     * 异步大模型任务状态查询
     * 值：TaskStatus-执行时间
     */
    private static final String TOKEN_CACHE_KEY_PREFIX = "sfa:llm:%s:%s:string";

    @Resource
    private RedisDataAccessor redisDataAccessor;

    public String getCacheCode(User user) {
        String key = getCodeKey(user);
        return redisDataAccessor.get(String.format(TOKEN_CACHE_KEY_FORMAT, key));
    }


    private String getCodeKey(User user) {
        if (user.isOutUser()) {
            return user.getTenantId() + ":" + user.getOutUserId();
        } else {
            return user.getTenantId() + ":" + user.getUpstreamOwnerIdOrUserId();
        }
    }

    public String cacheCode(User user, String code, int expire) {
        String key = getCodeKey(user);
        redisDataAccessor.set(String.format(TOKEN_CACHE_KEY_FORMAT, key), code, expire);
        return code;
    }

    public void cleanCacheCode(User user, String key) {
        if (StringUtils.isBlank(key)) {
            key = getCodeKey(user);
        }
        redisDataAccessor.del(String.format(TOKEN_CACHE_KEY_FORMAT, key));
    }

    public AiTaskDTO queryTask(String objectDescribeApiName, String dataId) {
        String key = String.format(TOKEN_CACHE_KEY_PREFIX, objectDescribeApiName, dataId);
        String value = redisDataAccessor.get(key);
        if (StringUtils.isBlank(value)) {
            return AiTaskDTO.builder().status(AiTaskStatus.UNKNOW.name()).build();
        } else {
            String[] parts = value.split("-", 2);
            if (parts.length != 2) {
                return AiTaskDTO.builder().status(AiTaskStatus.UNKNOW.name()).build();
            }
            String status = parts[0].trim();
            Long duration = Long.valueOf(parts[1].trim());
            return AiTaskDTO.builder().status(status).duration(duration).build();
        }
    }

    public void llmTask(String objectDescribeApiName, String dataId, AiTaskStatus aiTaskStatus, long duration) {
        String key = String.format(TOKEN_CACHE_KEY_PREFIX, objectDescribeApiName, dataId);
        String value = aiTaskStatus.name() + "-" + duration;
        // 24h = 3600s x 24 = 86400s
        redisDataAccessor.set(key, value, 86400);
    }
}
