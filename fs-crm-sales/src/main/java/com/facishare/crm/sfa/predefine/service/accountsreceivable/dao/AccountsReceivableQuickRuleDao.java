package com.facishare.crm.sfa.predefine.service.accountsreceivable.dao;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AccountsReceivableQuickRuleConstants;
import com.facishare.crm.sfa.utilities.constant.ObjectArRuleRelatedConstants;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据访问类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AccountsReceivableQuickRuleDao {

    @Resource
    private ServiceFacade serviceFacade;

    private static final int MAX_BATCH_SIZE = 2000;
    public List<IObjectData> getDataListByApiName(User user, String objectApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AccountsReceivableQuickRuleConstants.OBJECT_API_NAME, objectApiName);
        query.setFilters(filters);
        query.setPermissionType(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setLimit(MAX_BATCH_SIZE);
        return serviceFacade.findBySearchQueryIgnoreAll(user, SFAPreDefineObject.AccountsReceivableQuickRule.getApiName(), query).getData();
    }
}
