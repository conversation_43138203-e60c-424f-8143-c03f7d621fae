package com.facishare.crm.sfa.predefine.service.proposal.dto;

import com.facishare.crm.platform.annotation.Convertible;
import lombok.Data;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Data
@Convertible
public class ApiCallLogDTO {
    private String requestId;
    private String platform;
    private String apiEndpoint;
    private String method;
    private Long requestTime;
    private Long responseTime;
    private int statusCode;
    private Boolean success;
    private Object requestParams;
    private Object responseBody;
    private Boolean hasArtifact;
    private String artifactType;
    private String artifactPath;
    private Object artifactMeta;
    private String sourceType;
    private String sourceId;
    private String responseMessage;
    private String bizSource;
    private Integer costUnits;
    private Object remark;
    private Long durationMs;
}
