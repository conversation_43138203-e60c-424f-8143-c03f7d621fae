package com.facishare.crm.sfa.predefine.service.proposal.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.predefine.service.proposal.dto.CaseOverviewMQ;
import com.facishare.crm.sfa.predefine.service.proposal.dto.ProposalMQ;
import com.facishare.crm.sfa.predefine.service.proposal.producer.ProposalGenerateProducer;
import com.facishare.crm.sfa.prm.platform.model.ContextMQ;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.facishare.crm.sfa.predefine.service.proposal.access.CaseOverviewAccess.CASE_OVERVIEW_OBJ;
import static com.facishare.crm.sfa.predefine.service.proposal.access.ProposalAccess.PROPOSAL_OBJ;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-27
 * ============================================================
 */
@Service
@Slf4j
public class AsyncLLMService {
    @Resource(name = "proposalGenerateProducer")
    private ProposalGenerateProducer proposalGenerateProducer;

    public void sendCaseOverviewMessage(User user, String taskId, boolean byFile) {
        CaseOverviewMQ payload = new CaseOverviewMQ();
        payload.setTaskId(taskId);
        payload.setByFile(byFile);
        ContextMQ<CaseOverviewMQ> contextMQ = new ContextMQ<>();
        contextMQ.setPayload(payload);
        contextMQ.setTenantId(user.getTenantId());
        contextMQ.setUserId(user.getUserId());
        contextMQ.setOutUserId(user.getUserId());
        contextMQ.setOutTenantId(user.getTenantId());
        contextMQ.setAppId(RequestContextManager.getContext().getAppId());
        contextMQ.setLanguage(RequestContextManager.getContext().getLang().getValue());
        String messageBody = JSON.toJSONString(contextMQ);
        String form = "%s-%s@CaseOverviewObj-%s";
        String messageKey = String.format(form, user.getTenantId(), user.getUpstreamOwnerIdOrUserId(), contextMQ.getPayload());
        log.info("AsyncLLMService#sendCaseOverviewMessage by send rocketmq message {}", messageBody);
        proposalGenerateProducer.create(CASE_OVERVIEW_OBJ, messageBody, messageKey);
    }

    public void sendProposalMessage(User user, String taskId, String customPrompt) {
        ProposalMQ payload = new ProposalMQ();
        payload.setTaskId(taskId);
        payload.setCustomPrompt(customPrompt);
        ContextMQ<ProposalMQ> contextMQ = new ContextMQ<>();
        contextMQ.setPayload(payload);
        contextMQ.setTenantId(user.getTenantId());
        contextMQ.setUserId(user.getUserId());
        contextMQ.setOutUserId(user.getUserId());
        contextMQ.setOutTenantId(user.getTenantId());
        contextMQ.setAppId(RequestContextManager.getContext().getAppId());
        contextMQ.setLanguage(RequestContextManager.getContext().getLang().getValue());
        String messageBody = JSON.toJSONString(contextMQ);
        String form = "%s-%s@ProposalObj-%s";
        String messageKey = String.format(form, user.getTenantId(), user.getUpstreamOwnerIdOrUserId(), contextMQ.getPayload());
        log.info("AsyncLLMService#sendCaseOverviewMessage by send rocketmq message {}", messageBody);
        proposalGenerateProducer.create(PROPOSAL_OBJ, messageBody, messageKey);
    }
}
