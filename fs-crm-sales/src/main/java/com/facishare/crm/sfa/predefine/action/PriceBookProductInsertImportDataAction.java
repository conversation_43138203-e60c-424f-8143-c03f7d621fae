package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.PriceBookCommonService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.constant.dmConstants.MultiUnitConstants;
import com.facishare.crm.sfa.utilities.util.PriceBookImportUtils;
import com.facishare.crm.sfa.utilities.util.PriceBookUtil;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.crm.sfa.utilities.validator.PriceBookImportValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.Where;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_PRICEBOOK_PRICEBOOKPRODUCTEXIST;

/**
 * Created by luxin on 2018/4/2.
 */
@Slf4j
public class PriceBookProductInsertImportDataAction extends StandardInsertImportDataAction {
    private static final PriceBookCommonService priceBookCommonService = SpringUtil.getContext().getBean(PriceBookCommonService.class);
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private static final MetaDataFindServiceExt metaDataFindServiceExt = SpringUtil.getContext().getBean(MetaDataFindServiceExt.class);

    @Override
    protected void customValidate(List<ImportData> dataList) {
        String standardPriceBookId = priceBookCommonService.getStandardPriceBookId(actionContext.getUser());
        List<ImportError> errorList = Lists.newArrayList();
        PriceBookImportValidator.pricebookSpecialDeal(actionContext.getUser(), dataList, errorList);
        Map<String, Integer> mapParamData = Maps.newHashMap();
        //Map<String, Integer> standMapParamData = Maps.newHashMap();
        List<Wheres> wheres = Lists.newArrayList();
        for (ImportData importData : dataList) {
            IObjectData priceBookProductData = importData.getData();
            String priceBookId = priceBookProductData.get("pricebook_id", String.class);
            String productId = priceBookProductData.get("product_id", String.class);
            String actualUnit = StringUtils.EMPTY;
            if (bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(actionContext.getTenantId())) {
                actualUnit = priceBookProductData.get(MultiUnitConstants.DetailField.ACTUAL_UNIT, String.class);
            }
            if (StringUtils.isBlank(priceBookId) || StringUtils.isBlank(productId)) {
                errorList.add(new ImportError(importData.getRowNo(), I18N.text("sfa.price.book.null.or.special.format")));
            } else {
                if (priceBookId.equals(standardPriceBookId)) {
                    if (mapParamData.containsKey(priceBookId + productId)) {
                        if (!PriceBookUtil.isAnyPricingFeatureEnabled(actionContext.getTenantId())) {
                            errorList.add(new ImportError(importData.getRowNo(), I18N.text(SO_PRICEBOOK_PRICEBOOKPRODUCTEXIST)));
                        }
                    } else {
                        mapParamData.put(priceBookId + productId, importData.getRowNo());
                    }
                } else {
                    if (mapParamData.containsKey(priceBookId + productId + actualUnit)) {
                        if (!PriceBookUtil.isAnyPricingFeatureEnabled(actionContext.getTenantId())) {
                            errorList.add(new ImportError(importData.getRowNo(), I18N.text("sfa.pricebookproduct.check.samename")));
                        }
                    } else {
                        mapParamData.put(priceBookId + productId + actualUnit, importData.getRowNo());
                    }
                }

                List<IFilter> filters = Lists.newArrayList();
                Wheres where = new Wheres();
                SearchUtil.fillFilterEq(filters, "product_id", Lists.newArrayList(productId));
                SearchUtil.fillFilterEq(filters, "pricebook_id", Lists.newArrayList(priceBookId));
                where.setFilters(filters);
                where.setConnector(Where.CONN.OR.toString());
                wheres.add(where);

            }
        }
        if (CollectionUtils.isNotEmpty(wheres)) {
            validatePriceBookProductRepeat(wheres, dataList, standardPriceBookId, errorList, mapParamData);
        }
        PriceBookImportValidator.validatePriceRange(errorList, dataList);
        if (bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(actionContext.getTenantId())) {
            PriceBookImportValidator.validePriceBookProductUnit(actionContext.getUser(), errorList, dataList);
        }
        mergeErrorList(errorList);
    }


    @Override
    protected void customDefaultValue(List<IObjectData> validList) {
        validList.forEach(objectData -> objectData.set("record_type", "default__c"));
        PriceBookImportUtils.handleCurrency(actionContext.getTenantId(), validList);
        super.customDefaultValue(validList);
    }

    private void validatePriceBookProductRepeat(List<Wheres> wheres, List<ImportData> dataList, String standardPriceBookId, List<ImportError> errorList, Map<String, Integer> mapParamData) {
        SearchTemplateQuery searchTemplateQuery = SoCommonUtils.buildSearchTemplateQuery(0, 0);
        searchTemplateQuery.setWheres(wheres);
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        QueryResult<IObjectData> queryResult = metaDataFindServiceExt.findBySearchQueryIgnoreAll(actionContext.getUser(), PriceBookConstants.API_NAME_PRODUCT, searchTemplateQuery);
        List<IObjectData> priceBookProductDataList = queryResult.getData();
        if (PriceBookUtil.isAnyPricingFeatureEnabled(actionContext.getTenantId())) {
            boolean isOpenStratifiedPrice = bizConfigThreadLocalCacheService.isOpenStratifiedPricing(actionContext.getTenantId());
            boolean isOpenTieredPrice = bizConfigThreadLocalCacheService.isOpenPriceBookProductTieredPrice(actionContext.getTenantId());
            for (ImportData importData : dataList) {
                IObjectData priceBookProductData = importData.getData();
                String priceBookId = priceBookProductData.get("pricebook_id", String.class);
                String productId = priceBookProductData.get("product_id", String.class);
                List<IObjectData> samePriceBookAndProductData = Lists.newArrayList();
                String unit = priceBookProductData.get(MultiUnitConstants.DetailField.ACTUAL_UNIT, String.class);
                List<IObjectData> sameDataList;
                List<IObjectData> samePriceBookProductDataList;
                if (StringUtils.isEmpty(unit)) {
                    sameDataList = dataList.stream().filter(o -> priceBookId.equals(o.getData().get("pricebook_id", String.class)) && productId.equals(o.getData().get("product_id", String.class)) && o.getRowNo() != importData.getRowNo())
                            .map(o -> o.getData()).collect(Collectors.toList());
                    samePriceBookProductDataList = priceBookProductDataList.stream().filter(o -> priceBookId.equals(o.get("pricebook_id", String.class)) && productId.equals(o.get("product_id", String.class))).collect(Collectors.toList());
                } else {
                    sameDataList = dataList.stream().filter(o -> priceBookId.equals(o.getData().get("pricebook_id", String.class)) && productId.equals(o.getData().get("product_id", String.class)) && unit.equals(o.getData().get(MultiUnitConstants.DetailField.ACTUAL_UNIT, String.class)) && o.getRowNo() != importData.getRowNo())
                            .map(o -> o.getData()).collect(Collectors.toList());
                    samePriceBookProductDataList = priceBookProductDataList.stream().filter(o -> priceBookId.equals(o.get("pricebook_id", String.class)) && productId.equals(o.get("product_id", String.class)) && unit.equals(o.get(MultiUnitConstants.DetailField.ACTUAL_UNIT, String.class))).collect(Collectors.toList());
                }
                samePriceBookAndProductData.addAll(sameDataList);
                samePriceBookAndProductData.addAll(samePriceBookProductDataList);
                try {
                    priceBookCommonService.validatePriceBookProduct(priceBookProductData, samePriceBookAndProductData, actionContext, objectDescribe, standardPriceBookId, isOpenStratifiedPrice, isOpenTieredPrice);
                } catch (ValidateException ex) {
                    errorList.add(new ImportError(importData.getRowNo(), ex.getMessage()));
                }
            }
        } else {
            for (IObjectData priceBookProductData : priceBookProductDataList) {
                String priceBookId = priceBookProductData.get("pricebook_id", String.class);
                String productId = priceBookProductData.get("product_id", String.class);
                String actualUnit = StringUtils.EMPTY;
                if (!Objects.isNull(priceBookProductData.get("actual_unit"))) {
                    actualUnit = priceBookProductData.get(MultiUnitConstants.DetailField.ACTUAL_UNIT, String.class);
                }
                if (priceBookId.equals(standardPriceBookId)) {
                    if (mapParamData.containsKey(priceBookId + productId)) {
                        errorList.add(new ImportError(mapParamData.get(priceBookId + productId), I18N.text(SO_PRICEBOOK_PRICEBOOKPRODUCTEXIST)));
                        continue;
                    }
                } else {
                    if (mapParamData.containsKey(priceBookId + productId + actualUnit)) {
                        errorList.add(new ImportError(mapParamData.get(priceBookId + productId + actualUnit), I18N.text("sfa.pricebookproduct.check.samename")));
                        continue;
                    }
                }
            }
        }
    }
}
