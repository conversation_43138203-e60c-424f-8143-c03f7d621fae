package com.facishare.crm.sfa.predefine.service.proposal.dto;

import com.facishare.crm.platform.annotation.Convertible;
import com.facishare.crm.platform.annotation.Mappable;
import lombok.Data;

import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Data
@Convertible
public class CaseOverviewDTO {
    @Mappable(fieldApiName = "_id")
    private String id;
    private String relatedObjectDescribe;
    private String relatedDataId;
    private String accountId;
    private String leadsId;
    private String newOpportunityId;
    private String content;
    private List<Object> promptAttachment;
}
