package com.facishare.crm.sfa.predefine.service.cpq;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.BomConstraintLineModel;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.cpq.model.BomTreeModel;
import com.facishare.crm.sfa.predefine.service.task.ProductStatusChangeTaskService;
import com.facishare.crm.sfa.task.AsyncTaskProducer;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.BomConstraintConstants;
import com.facishare.crm.sfa.utilities.constant.BomCoreConstants;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.enums.EnumUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.ProductConstraintUtil;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.AutoNumberLogicService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.constant.SpuSkuConstants.MULTIUNITRELEATED_IS_PACKAGE;


@Service
@Slf4j
public class BomCoreV3ServiceImpl implements BomCoreV3Service, InitializingBean {

    private static final int MAX_SIZE = 2000;
    private Map<Integer, List<String>> vipBomNodeMap = Maps.newHashMap();

    @Autowired
    private ObjectDataProxy dataProxy;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private BomService bomService;
    @Autowired
    private AutoNumberLogicService autoNumberLogicService;
    @Autowired
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Autowired
    private ProductStatusChangeTaskService productStatusChangeTaskService;
    @Autowired
    private AsyncTaskProducer asyncTaskProducer;
    @Autowired
    private BomConstraintService bomConstraintService;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;


    @Override
    public List<IObjectData> saveBomTreeV3(User user, List<ObjectDataDocument> bomTree, List<ObjectDataDocument> deletedBomAndGroupList, String rootProductId, StopWatch stopWatch, BomTreeModel.Arg arg) {
        IObjectData rootBomData = getRootBomData(user, rootProductId);
        int total = 0;
        String masterId = serviceFacade.generateId();
        String rootBomId = rootBomData != null ? rootBomData.getId() : serviceFacade.generateId();
        List<IObjectData> allNodeList = Lists.newArrayList();
        IObjectData masterData = null;
        if (Objects.nonNull(rootBomData)) {
            masterId = rootBomData.get(BomConstants.FIELD_CORE_ID, String.class);
            allNodeList = queryNodeListByCond(user, rootBomId);
            total = allNodeList.size();
        } else {
            masterData = getMasterData(user, rootProductId, masterId);
        }
        List<IObjectData> allList = Lists.newArrayList();
        List<IObjectData> deletedBomDataList = deletedBomAndGroupList.stream()
                .filter(o -> Utils.BOM_API_NAME.equals(o.get("object_describe_api_name")))
                .map(ObjectDataDocument::toObjectData)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        total = total - deletedBomDataList.size();
        List<IObjectData> deletedBomGroupList = deletedBomAndGroupList.stream()
                .filter(o -> Utils.PRODUCT_GROUP_API_NAME.equals(o.get("object_describe_api_name")))
                .map(ObjectDataDocument::toObjectData)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(deletedBomDataList)) {
            allList.addAll(deletedBomDataList);
        }
        Map<String, List<ObjectDataDocument>> bomMap = analyzeBOMV3Tree(user, bomTree, rootBomId, total, allNodeList, rootProductId);
        validateSumNode(user.getTenantId(), bomMap.getOrDefault(Utils.BOM_API_NAME, Lists.newArrayList()), total);
        List<IObjectData> bomDataList = bomMap.get(Utils.BOM_API_NAME).stream()
                .map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        List<IObjectData> bomGroupList = bomMap.get(Utils.PRODUCT_GROUP_API_NAME).stream()
                .map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        validateShareRate(user, rootProductId, rootBomId, bomDataList);
        IObjectDescribe bomDesc = serviceFacade.findObject(user.getTenantId(), Utils.BOM_API_NAME);
        IObjectDescribe groupDesc = serviceFacade.findObject(user.getTenantId(), Utils.PRODUCT_GROUP_API_NAME);
        stopWatch.lap("preData");
        checkGroupRule(arg, bomDataList, allNodeList, user, deletedBomDataList);
        stopWatch.lap("checkGroupRule");
        saveBomGroup(user, bomGroupList, groupDesc, bomDataList);
        stopWatch.lap("saveBomGroup");
        updateBomList(arg, user, allNodeList, bomDesc, groupDesc);
        stopWatch.lap("updateBomGroup");
        List<IObjectData> objectDataList = saveBom(user, bomDataList, bomDesc, rootBomId, stopWatch, masterId);
        stopWatch.lap("saveBom");
        allList.addAll(objectDataList);
        deletedBomNode(user, deletedBomDataList, bomDesc, allNodeList);
        stopWatch.lap("deletedBomNode");
        deletedGroup(user, deletedBomGroupList, groupDesc, allNodeList, bomDesc);
        stopWatch.lap("deletedGroup");
        Map<String, String> maps = Maps.newHashMap();
        maps.put(rootBomId, rootProductId);
        List<IObjectData> rootNodes = bulkCreateBomRootNode(user, maps, bomDesc, masterData);
        stopWatch.lap("bulkCreateBomRootNode");
        if (CollectionUtils.notEmpty(rootNodes)) {
            allList.addAll(rootNodes);
            objectDataList.addAll(rootNodes);
        }
        if (GrayUtil.bomUpdateNotifyFunction(user.getTenantId())) {
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            parallelTask.submit(() -> executeCustomFunction(user, allList));
            parallelTask.run();
        }
        stopWatch.lap("executeCustomFunction");
        return objectDataList;
    }

    @NotNull
    private IObjectData getMasterData(User user, String rootProductId, String masterId) {
        IObjectData masterData = new ObjectData();
        masterData.setTenantId(user.getTenantId());
        masterData.setDescribeApiName(Utils.BOM_CORE_API_NAME);
        masterData.setCreatedBy(user.getUpstreamOwnerIdOrUserId());
        masterData.setOwner(Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
        masterData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        masterData.set(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());
        masterData.set(BomConstants.FIELD_PRODUCT_ID, rootProductId);
        masterData.setId(masterId);
        if (SFAConfigUtil.isSimpleBom(user.getTenantId())) {
            masterData.set(BomCoreConstants.FIELD_CATEGORY, BomCoreConstants.category.standard.getValue());
        } else {
            masterData.set(BomCoreConstants.FIELD_CATEGORY, BomCoreConstants.category.configure.getValue());
        }
        masterData.set(BomCoreConstants.FIELD_PURPOSE, BomCoreConstants.purpose.sale.getValue());
        masterData.set(BomCoreConstants.SALE_STRATEGY, BomCoreConstants.SaleStrategy.whole.getValue());
        return masterData;
    }

    private void checkGroupRule(BomTreeModel.Arg arg, List<IObjectData> bomDataList, List<IObjectData> allNodeList, User user, List<IObjectData> deletedBomDataList) {
        if (CollectionUtils.empty(allNodeList)) {
            return;
        }
        List<ObjectDataDocument> tmpUpdateList = CollectionUtils.nullToEmpty(arg.getUpdateList()).stream()
                .filter(o -> Objects.nonNull(o) && StringUtils.isNotBlank(o.getId())).collect(Collectors.toList());
        if (CollectionUtils.empty(tmpUpdateList)) {
            return;
        }
        List<IObjectData> updateList = ObjectDataDocument.ofDataList(tmpUpdateList);
        List<IObjectData> updateGroupList = updateList.stream().filter(o -> Utils.PRODUCT_GROUP_API_NAME.equals(o.get("object_describe_api_name"))
                && StringUtils.isNotBlank(o.get(BomConstants.FIELD_MAX_PROD_COUNT, String.class))).collect(Collectors.toList());
        Map<String/*product_group_id*/, List<IObjectData>> updateBomMap = updateList.stream().filter(o -> Utils.BOM_API_NAME.equals(o.get("object_describe_api_name"))
                && StringUtils.isNotBlank(o.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class))
                && o.get(BomConstants.FIELD_SELECTED_BY_DEFAULT, Boolean.class, false))
                .collect(Collectors.groupingBy(o -> o.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)));
        Map<String/*product_group_id*/, List<IObjectData>> allDataMap = allNodeList.stream().filter(o -> Utils.BOM_API_NAME.equals(o.get("object_describe_api_name"))
                && StringUtils.isNotBlank(o.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class))
                && o.get(BomConstants.FIELD_SELECTED_BY_DEFAULT, Boolean.class, false))
                .collect(Collectors.groupingBy(x -> x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)));
        Set<String> updateGroupIds = updateBomMap.keySet();
        if (CollectionUtils.notEmpty(updateGroupIds)) {
            Set<String> tmpUpdateGroupIds = updateGroupList.stream().map(DBRecord::getId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            updateGroupIds.removeIf(tmpUpdateGroupIds::contains);
            if (CollectionUtils.notEmpty(updateGroupIds)) {
                List<IObjectData> dbGroupList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(updateGroupIds), Utils.PRODUCT_GROUP_API_NAME);
                if (CollectionUtils.notEmpty(dbGroupList)) {
                    updateGroupList.addAll(dbGroupList);
                }
            }
        }
        if (CollectionUtils.empty(updateGroupList)) {
            return;
        }
        Set<String> deleteIds = deletedBomDataList.stream().map(DBRecord::getId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        updateGroupList.stream().filter(x -> StringUtils.isNotBlank(x.get(BomConstants.FIELD_MAX_PROD_COUNT, String.class))
                && StringUtils.isNotBlank(x.getId())
                && StringUtils.isNotBlank(x.getName())).forEach(x -> {
            long count = bomDataList.stream().filter(d -> StringUtils.equals(x.getId(), d.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class))
                    && d.get(BomConstants.FIELD_SELECTED_BY_DEFAULT, Boolean.class, false)
                    && Objects.equals("create", d.get("action_type", String.class))).count();
            List<IObjectData> updateDataList = updateBomMap.getOrDefault(x.getId(), Lists.newArrayList());
            List<IObjectData> allDataList = allDataMap.getOrDefault(x.getId(), Lists.newArrayList());
            Set<String> updateIds = updateDataList.stream().map(DBRecord::getId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            List<IObjectData> dbList = allDataList.stream().filter(d -> !updateIds.contains(d.getId()) && !deleteIds.contains(d.getId()) && d.get(BomConstants.FIELD_SELECTED_BY_DEFAULT, Boolean.class, false)).collect(Collectors.toList());
            if ((updateDataList.size() + dbList.size() + count) > Long.parseLong(x.get(BomConstants.FIELD_MAX_PROD_COUNT, String.class))) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_RULE_WARN, x.getName()));
            }
        });
    }

    private void updateBomList(BomTreeModel.Arg arg, User user, List<IObjectData> nodeList, IObjectDescribe bomDesc, IObjectDescribe groupDesc) {
        List<ObjectDataDocument> tmpUpdateList = CollectionUtils.nullToEmpty(arg.getUpdateList()).stream()
                .filter(o -> Objects.nonNull(o) && StringUtils.isNotBlank(o.getId())).collect(Collectors.toList());
        if (CollectionUtils.empty(tmpUpdateList)) {
            return;
        }
        List<IObjectData> updateList = ObjectDataDocument.ofDataList(tmpUpdateList);
        List<IObjectData> updateGroupList = updateList.stream().filter(o -> Utils.PRODUCT_GROUP_API_NAME.equals(o.get("object_describe_api_name"))).collect(Collectors.toList());
        Map<String, IObjectData> nodeMap = nodeList.stream().collect(Collectors.toMap(DBRecord::getId, o -> o, (o1, o2) -> o1));
        Map<String, IObjectData> updateMap = updateList.stream().collect(Collectors.toMap(DBRecord::getId, o -> o, (o1, o2) -> o1));
        Boolean priceEditDefaultValue = true;
        Boolean enableStatusDefaultValue = true;
        IFieldDescribe priceEditFieldDescribe = bomDesc.getFieldDescribe(BomConstants.FIELD_PRICE_EDITABLE);
        if (Objects.nonNull(priceEditFieldDescribe)) {
            priceEditDefaultValue = priceEditFieldDescribe.get("default_value", Boolean.class, true);
        }
        IFieldDescribe enableStatusFieldDescribe = bomDesc.getFieldDescribe(BomConstants.FIELD_ENABLED_STATUS);
        if (Objects.nonNull(enableStatusFieldDescribe)) {
            enableStatusDefaultValue = enableStatusFieldDescribe.get("default_value", Boolean.class, true);
        }
        List<IObjectData> updateBomList = Lists.newArrayList();
        for (IObjectData objectData : updateList) {
            if (!Utils.BOM_API_NAME.equals(objectData.get("object_describe_api_name"))) {
                continue;
            }
            IObjectData dbObj = nodeMap.get(objectData.getId());
            if (Objects.isNull(dbObj)) {
                continue;
            }
            if (objectData.get(BomConstants.FIELD_IS_REQUIRED, Boolean.class, false)) {
                objectData.set(BomConstants.FIELD_SELECTED_BY_DEFAULT, true);
            }
            objectData.set(BomConstants.FIELD_BOM_PATH, dbObj.get(BomConstants.FIELD_BOM_PATH));
            objectData.set(BomConstants.FIELD_PARENT_BOM_ID, dbObj.get(BomConstants.FIELD_PARENT_BOM_ID));
            objectData.set(BomConstants.FIELD_ROOT_ID, dbObj.get(BomConstants.FIELD_ROOT_ID));
            Boolean priceEdit = objectData.get(BomConstants.FIELD_PRICE_EDITABLE, Boolean.class, priceEditDefaultValue);
            Boolean enableStatus = objectData.get(BomConstants.FIELD_ENABLED_STATUS, Boolean.class, enableStatusDefaultValue);
            Boolean dbPriceEdit = dbObj.get(BomConstants.FIELD_PRICE_EDITABLE, Boolean.class, true);
            if ((!priceEdit && dbPriceEdit) || !enableStatus) {
                nodeList.stream()
                        .filter(d -> d.get(BomConstants.FIELD_BOM_PATH, String.class).startsWith(objectData.get(BomConstants.FIELD_BOM_PATH, String.class, "").concat(".")))
                        .forEach(d -> {
                            IObjectData updateData = updateMap.get(d.getId());
                            if (Objects.nonNull(updateData)) {
                                if (!priceEdit && dbPriceEdit) {
                                    updateData.set(BomConstants.FIELD_PRICE_EDITABLE, false);
                                    Integer priceMode = updateData.get(BomConstants.FIELD_PRICE_MODE, Integer.class, 1);
                                    if (EnumUtil.PriceMode.DEF.getValue() == priceMode) {
                                        BigDecimal price = updateData.get(BomConstants.FIELD_ADJUST_PRICE, BigDecimal.class, BigDecimal.ZERO);
                                        if (price.compareTo(BigDecimal.ZERO) != 0) {
                                            updateData.set(BomConstants.FIELD_AMOUNT_EDITABLE, false);
                                        }
                                    }
                                }
                                if (!enableStatus) {
                                    updateData.set(BomConstants.FIELD_ENABLED_STATUS, false);
                                }
                            } else {
                                if (!priceEdit && dbPriceEdit) {
                                    d.set(BomConstants.FIELD_PRICE_EDITABLE, false);
                                    Integer priceMode = d.get(BomConstants.FIELD_PRICE_MODE, Integer.class, 1);
                                    if (EnumUtil.PriceMode.DEF.getValue() == priceMode) {
                                        BigDecimal price = d.get(BomConstants.FIELD_ADJUST_PRICE, BigDecimal.class, BigDecimal.ZERO);
                                        if (price.compareTo(BigDecimal.ZERO) != 0) {
                                            d.set(BomConstants.FIELD_AMOUNT_EDITABLE, false);
                                        }
                                    }
                                }
                                if (!enableStatus) {
                                    d.set(BomConstants.FIELD_ENABLED_STATUS, false);
                                }
                                updateBomList.add(d);
                            }
                        });
            }
            updateBomList.add(objectData);
        }
        if (CollectionUtils.notEmpty(updateBomList)) {
            serviceFacade.batchUpdate(updateBomList, user);
            logAsync(user, bomDesc, updateBomList, EventType.MODIFY, ActionType.Modify);
        }
        if (CollectionUtils.notEmpty(updateGroupList)) {
            serviceFacade.batchUpdate(updateGroupList, user);
            logAsync(user, groupDesc, updateGroupList, EventType.MODIFY, ActionType.Modify);
        }
    }

    private List<IObjectData> queryNodeListByCond(User user, String rootId) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(2000);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, "0");
        SearchUtil.fillFilterEq(filters, ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());
        SearchUtil.fillFilterEq(filters, BomConstants.FIELD_ROOT_ID, rootId);
        searchTemplateQuery.setFilters(filters);
        return serviceFacade.findBySearchQueryIgnoreAll(user, Utils.BOM_API_NAME, searchTemplateQuery).getData();
    }

    private void validateShareRate(User user, String rootProductId, String parentBomId, List<IObjectData> bomDataList) {
        Predicate<IObjectData> predicate = x -> Objects.equals(parentBomId, x.get(BomConstants.FIELD_PARENT_BOM_ID)) &&
                StringUtils.isNotBlank(x.get(BomConstants.SHARE_RATE, String.class));
        List<IObjectData> nodeList = bomDataList.stream().filter(predicate).collect(Collectors.toList());
        if (CollectionUtils.empty(nodeList)) {
            return;
        }
        BigDecimal sum = nodeList.stream().map(x -> x.get(BomConstants.SHARE_RATE, BigDecimal.class, BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (sum.compareTo(new BigDecimal("100")) > 0) {
            String productId = bomDataList.stream().filter(x -> Objects.equals(parentBomId, x.getId())).map(x -> x.get(BomConstants.FIELD_PRODUCT_ID, String.class)).findFirst().orElse(rootProductId);
            IObjectData productObj = serviceFacade.findObjectDataIgnoreAll(user, productId, Utils.PRODUCT_API_NAME);
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_SHARE_RATE_LIMIT, productObj.getName()));
        }
        nodeList.stream().forEach(x -> validateShareRate(user, rootProductId, x.getId(), bomDataList));
    }


    @Override
    public Map<String, List<ObjectDataDocument>> analyzeBOMV3Tree(User user, List<ObjectDataDocument> bomTree, String rootBomId, int total, List<IObjectData> allNodeList, String rootProductId) {
        List<ObjectDataDocument> bomList = Lists.newArrayList();
        List<ObjectDataDocument> groupList = Lists.newArrayList();
        ArrayList<String> midProductIds = Lists.newArrayList();
        recursiveV3(user, bomTree, bomList, groupList, Lists.newArrayList(rootBomId), rootBomId, null, midProductIds, 1, rootBomId, allNodeList, rootProductId);
        Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();
        details.put(Utils.BOM_API_NAME, bomList);
        details.put(Utils.PRODUCT_GROUP_API_NAME, groupList);
        return details;
    }

    private void validateSumNode(String tenantId, List<ObjectDataDocument> bomList, int total) {
        List<ObjectDataDocument> createList = bomList.stream().filter(x -> Objects.equals("create", MapUtils.getString(x, "action_type"))).collect(Collectors.toList());
        total = total + createList.size();
        checkQuantity(tenantId, total);

    }

    public void checkQuantity(String tenantId, int total) {
        boolean flag = false;
        for (Map.Entry<Integer, List<String>> entry : vipBomNodeMap.entrySet()) {
            Integer count = entry.getKey();
            List<String> tenantIds = entry.getValue();
            if (tenantIds.contains(tenantId)) {
                flag = true;
                if (total > count) {
                    throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_NODE_CAN_NOT_EXCEED_MAX_LIMIT_COUNT, count));
                }
            }
        }
        if (!flag && total > 500) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_NODE_CAN_NOT_EXCEED_MAX_LIMIT_COUNT, 500));
        }
    }


    @Override
    public void updateProductIsPackage(User user, List<String> ids, Boolean isPackage) {
        if (CollectionUtils.empty(ids)) {
            return;
        }
        SearchTemplateQuery bomSearchQuery = new SearchTemplateQuery();
        bomSearchQuery.setLimit(ids.size());
        if (ids.size() == 1) {
            SearchUtil.fillFilterEq(bomSearchQuery.getFilters(), DBRecord.ID, ids);
        } else {
            SearchUtil.fillFilterIn(bomSearchQuery.getFilters(), DBRecord.ID, ids);
        }
        SearchUtil.fillFilterNotEq(bomSearchQuery.getFilters(), MULTIUNITRELEATED_IS_PACKAGE, isPackage);
        QueryResult<IObjectData> productDataListResult;
        IActionContext actionContext = ActionContextExt.of(user).skipRelevantTeam().getContext();
        if (SFAConfigUtil.isSimpleBom(user.getTenantId())) {
            actionContext.setDbType("pg");
            productDataListResult = serviceFacade.findBySearchQuery(actionContext, Utils.PRODUCT_API_NAME, bomSearchQuery);
        } else {
            productDataListResult = metaDataFindServiceExt.findBySearchQuery(user, Utils.PRODUCT_API_NAME, bomSearchQuery);
        }
        if (Objects.nonNull(productDataListResult) && CollectionUtils.notEmpty(productDataListResult.getData())) {
            List<IObjectData> productDataList = productDataListResult.getData();
            productDataList.stream().forEach(o -> o.set(MULTIUNITRELEATED_IS_PACKAGE, isPackage));
            try {
                List<IObjectData> objectDataList = objectDataService.batchUpdateIgnoreOther(productDataList
                        , Lists.newArrayList(MULTIUNITRELEATED_IS_PACKAGE)
                        , actionContext);
                logAsync(user, serviceFacade.findObject(user.getTenantId(), Utils.PRODUCT_API_NAME), objectDataList, EventType.MODIFY, ActionType.Modify);
            } catch (MetadataServiceException e) {
                log.error("updateProductIsPackage error", e);
                throw new MetaDataBusinessException(e);
            }

        }
    }

    @Override
    public IObjectData getRootBomData(User user, String rootProductId) {
        SearchTemplateQuery bomSearchQuery = SoCommonUtils.buildSearchTemplateQuery(0, 2);
        SearchUtil.fillFilterEq(bomSearchQuery.getFilters(), BomConstants.FIELD_PRODUCT_ID, rootProductId);
        SearchUtil.fillFilterIsNull(bomSearchQuery.getFilters(), BomConstants.FIELD_PARENT_BOM_ID);
        QueryResult<IObjectData> rootBomData = serviceFacade.findBySearchQuery(user, Utils.BOM_API_NAME, bomSearchQuery);
        if (Objects.isNull(rootBomData) || CollectionUtils.empty(rootBomData.getData())) {
            return null;
        }
        if (rootBomData.getData().size() > 1 && GrayUtil.bomMasterSlaveMode(user.getTenantId())) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CREATE_WARN));
        }
        return rootBomData.getData().get(0);
    }

    private int recursiveV3(User user, List<ObjectDataDocument> bomTreeList,
                            List<ObjectDataDocument> bomList,
                            List<ObjectDataDocument> groupList,
                            List<String> bomPath,
                            String rootBomId,
                            String groupId,
                            ArrayList<String> midProductIds,
                            int depth,
                            String rootId, List<IObjectData> allNodeList, String parentProductId) {

        if (depth > 10) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_LEVEL_CAN_NOT_EXCEED_TEN_LEVELS));
        }
        Map<String, IObjectData> nodeMap = allNodeList.stream().collect(Collectors.toMap(DBRecord::getId, o -> o, (o1, o2) -> o1));
        int sumBom = 0;
        ArrayList<String> peerGroupName = Lists.newArrayList();
        for (ObjectDataDocument node : bomTreeList) {
            node.put(Tenantable.TENANT_ID, user.getTenantId());
            if (StringUtils.isBlank(node.getId())) {
                node.put(DBRecord.ID, serviceFacade.generateId());
                node.put("action_type", "create");
            }

            if (Utils.BOM_API_NAME.equals(node.get("object_describe_api_name"))) {
                IObjectData existNode = nodeMap.get(node.getId());
                if (Objects.nonNull(existNode)) {
                    groupId = MapUtils.getString(node, BomConstants.FIELD_PRODUCT_GROUP_ID);
                    bomPath = Lists.newArrayList();
                    bomPath.addAll(Splitter.on(".").splitToList(existNode.get(BomConstants.FIELD_BOM_PATH, String.class)));
                    node.put(BomConstants.FIELD_PARENT_BOM_ID, existNode.get(BomConstants.FIELD_PARENT_BOM_ID, String.class));
                    node.put(BomConstants.FIELD_ROOT_ID, rootId);
                    node.put(BomConstants.FIELD_BOM_PATH, existNode.get(BomConstants.FIELD_BOM_PATH, String.class));
                    IObjectData parentData = nodeMap.get(existNode.get(BomConstants.FIELD_PARENT_BOM_ID, String.class));
                    if (Objects.nonNull(parentData)) {
                        node.put(BomConstants.FIELD_PARENT_PRODUCT_ID, parentData.get(BomConstants.FIELD_PRODUCT_ID, String.class));
                    }
                } else {
                    bomPath.add(node.getId());
                    node.put(BomConstants.FIELD_PARENT_BOM_ID, rootBomId);
                    node.put(BomConstants.FIELD_ROOT_ID, rootId);
                    node.put(BomConstants.FIELD_BOM_PATH, Joiner.on(".").join(Lists.newArrayList(bomPath)));
                    node.put(BomConstants.FIELD_PARENT_PRODUCT_ID, parentProductId);
                }
                bomService.checkSubProducts(node);
            } else {
                if (!Objects.equals(node.get("action_type"), EnumUtil.actionType.create.getValue()) && StringUtils.isNotBlank(MapUtils.getString(node, BomConstants.FIELD_PARENT_BOM_ID)) && depth == 1) {
                    IObjectData parentData = nodeMap.get(MapUtils.getString(node, BomConstants.FIELD_PARENT_BOM_ID));
                    if (parentData != null) {
                        bomPath = Lists.newArrayList();
                        bomPath.addAll(Splitter.on(".").splitToList(parentData.get(BomConstants.FIELD_BOM_PATH, String.class)));
                        rootBomId = parentData.getId();
                    }
                } else {
                    if (Objects.equals(rootBomId, rootId)) {
                        bomPath = Lists.newArrayList();
                        bomPath.add(rootId);
                    }
                    node.put(BomConstants.FIELD_PARENT_BOM_ID, rootBomId);
                }
                String groupName = node.toObjectData().get("name", String.class);
                if (peerGroupName.contains(groupName) && Objects.equals(node.get("action_type"), EnumUtil.actionType.create.getValue())) {
                    throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_SAME_LEVEL_GROUP_CAN_NOT_HAVE_THE_SAME_NAME));
                } else {
                    if (Objects.equals(node.get("action_type"), EnumUtil.actionType.create.getValue())) {
                        peerGroupName.add(groupName);
                    }
                }
            }

            if (Objects.nonNull(node.get("children"))) {
                if (Utils.BOM_API_NAME.equals(node.get("object_describe_api_name"))) {
                    List<ObjectDataDocument> children = ((List<Map<String, Object>>) node.get("children")).stream().map(ObjectDataDocument::of).collect(Collectors.toList());
                    sumBom += recursiveV3(user, children, bomList, groupList, bomPath, node.getId(), null, midProductIds, depth + 1, rootId, allNodeList, MapUtils.getString(node, BomConstants.FIELD_PRODUCT_ID));
                    midProductIds.add(node.toObjectData().get(BomConstants.FIELD_PRODUCT_ID, String.class));
                } else {
                    List<ObjectDataDocument> children = ((List<Map<String, Object>>) node.get("children")).stream().map(ObjectDataDocument::of).collect(Collectors.toList());
                    sumBom += recursiveV3(user, children, bomList, groupList, bomPath, rootBomId, node.getId(), midProductIds, depth, rootId, allNodeList, parentProductId);
                }
            }

            if (Utils.BOM_API_NAME.equals(node.get("object_describe_api_name"))) {
                node.put(BomConstants.FIELD_PRODUCT_GROUP_ID, groupId);
                node.remove("children");
                bomList.add(node);
                bomPath.remove(node.getId());
            } else {
                node.remove("children");
                groupList.add(node);
            }
        }
        return sumBom;
    }

    @Override
    public List<IObjectData> bulkCreateBomRootNode(User user, Map<String, String> bomIdToProductId, IObjectDescribe bomDesc, IObjectData masterData) {
        // 移除已经存在的bom跟节点
        List<IObjectData> existingBomRootNodeList = findBomRootDataList(user, Lists.newArrayList(bomIdToProductId.keySet()));
        if (CollectionUtils.notEmpty(existingBomRootNodeList)) {
            existingBomRootNodeList.forEach(x -> bomIdToProductId.remove(x.getId()));
        }

        if (CollectionUtils.empty(bomIdToProductId)) {
            return existingBomRootNodeList;
        }

        List<IObjectData> bomDataList = Lists.newArrayList();
        bomIdToProductId.forEach((rootBomId, rootProductId) -> {
            IObjectData bomData = new ObjectData();
            bomData.set(BomConstants.FIELD_PRODUCT_ID, rootProductId);
            bomData.set(BomConstants.FIELD_PARENT_PRODUCT_ID, rootProductId);
            bomData.set(IObjectData.DESCRIBE_API_NAME, Utils.BOM_API_NAME);
            bomData.setTenantId(user.getTenantId());
            bomData.setLastModifiedTime(System.currentTimeMillis());
            bomData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
            bomData.setCreateTime(System.currentTimeMillis());
            bomData.setCreatedBy(user.getUpstreamOwnerIdOrUserId());
            bomData.setId(rootBomId);
            bomData.set("root_id", rootBomId);
            bomData.set("bom_path", rootBomId);
            if (Objects.nonNull(masterData)) {
                bomData.set(BomConstants.FIELD_CORE_ID, masterData.getId());
            }
            bomData.set(BomConstants.FIELD_AMOUNT, "1");
            bomDataList.add(bomData);
        });

        try {

            autoNumberLogicService.calculateAutoNumberValue(bomDesc, bomDataList);
            IActionContext actionContext = SoCommonUtils.getIActionContext(user);
            List<IObjectData> objectData = dataProxy.bulkCreate(bomDataList, true, actionContext);
            if (GrayUtil.bomMasterSlaveMode(user.getTenantId())) {
                IObjectDescribe bomCoreDesc = serviceFacade.findObject(user.getTenantId(), Utils.BOM_CORE_API_NAME);
                List<IObjectData> masterList = Lists.newArrayList(masterData);
                autoNumberLogicService.calculateAutoNumberValue(bomCoreDesc, masterList);
                dataProxy.bulkCreate(masterList, true, actionContext);
            }
            return objectData;
        } catch (MetadataServiceException e) {
            log.error("RootBom create fail", e);
            throw new ValidateException(e.getMessage());
        }
    }


    /**
     * 删除分组
     *
     * @param user
     * @param deletedGroupList
     * @param groupDesc
     * @param allNodeList
     * @param bomDesc
     */


    private void deletedGroup(User user, List<IObjectData> deletedGroupList, IObjectDescribe groupDesc, List<IObjectData> allNodeList, IObjectDescribe bomDesc) {
        if (CollectionUtils.empty(deletedGroupList)) {
            return;
        }
        List<IObjectData> updateList = Lists.newArrayList();
        List<IObjectData> deleteBomList = Lists.newArrayList();
        List<IObjectData> deleteChildBomList = Lists.newArrayList();
        Set<String> deleteChildGroupList = Sets.newHashSet();
        deletedGroupList.stream().filter(x -> StringUtils.isNotBlank(x.getId())).forEach(x -> {
            Boolean deleteChildBom = x.get("delete_child_bom", Boolean.class, false);
            if (deleteChildBom) {
                deleteBomList.addAll(allNodeList.stream()
                        .filter(d -> Objects.equals(x.getId(), d.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)))
                        .collect(Collectors.toList()));
                deleteBomList.stream().filter(d -> StringUtils.isNotBlank(d.get(BomConstants.FIELD_BOM_PATH, String.class))).forEach(d -> {
                    String bomPath = d.get(BomConstants.FIELD_BOM_PATH, String.class).concat(".");
                    allNodeList.forEach(k -> {
                        String childBomPath = k.get(BomConstants.FIELD_BOM_PATH, String.class);
                        if (StringUtils.isNotBlank(childBomPath) && childBomPath.startsWith(bomPath)) {
                            String groupId = k.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class);
                            if (StringUtils.isNotBlank(groupId)) {
                                deleteChildGroupList.add(groupId);
                            }
                            deleteChildBomList.add(k);
                        }
                    });
                });
                deleteBomList.addAll(deleteChildBomList);
            } else {
                String parentBomId = allNodeList.stream()
                        .filter(d -> Objects.equals(x.getId(), d.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class))
                                && StringUtils.isNotBlank(d.get(BomConstants.FIELD_PARENT_BOM_ID, String.class)))
                        .map(d -> d.get(BomConstants.FIELD_PARENT_BOM_ID, String.class))
                        .findFirst()
                        .orElse("");
                if (StringUtils.isNotBlank(parentBomId)) {
                    Long max = allNodeList.stream()
                            .filter(d -> Objects.equals(parentBomId, d.get(BomConstants.FIELD_PARENT_BOM_ID, String.class))
                                    && StringUtils.isNotBlank(d.get(BomConstants.FIELD_ORDER_FIELD, String.class))
                                    && StringUtils.isBlank(d.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)))
                            .mapToLong(d -> d.get(BomConstants.FIELD_ORDER_FIELD, Long.class))
                            .max()
                            .orElse(0L);
                    for (IObjectData d : allNodeList) {
                        if (Objects.equals(x.getId(), d.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class))) {
                            d.set(BomConstants.FIELD_ORDER_FIELD, d.get(BomConstants.FIELD_ORDER_FIELD, Long.class, 1L) + max);
                            d.set(BomConstants.FIELD_PRODUCT_GROUP_ID, null);
                            updateList.add(d);
                        }
                    }
                } else {
                    allNodeList.stream()
                            .filter(d -> Objects.equals(x.getId(), d.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)))
                            .forEach(d -> {
                                d.set(BomConstants.FIELD_PRODUCT_GROUP_ID, null);
                                updateList.add(d);
                            });
                }
            }
        });
        if (CollectionUtils.notEmpty(updateList)) {
            try {
                objectDataService.batchUpdateIgnoreOther(updateList, Lists.newArrayList(BomConstants.FIELD_PRODUCT_GROUP_ID, BomConstants.FIELD_ORDER_FIELD), ActionContextExt.of(user).skipRelevantTeam().getContext());
                logAsync(user, bomDesc, updateList, EventType.MODIFY, ActionType.Modify);
            } catch (MetadataServiceException e) {
                log.error("update bom group error", e);
            }
        }
        if (CollectionUtils.notEmpty(deleteBomList)) {
            serviceFacade.bulkDeleteDirect(deleteBomList, user);
            logAsync(user, bomDesc, deleteBomList, EventType.DELETE, ActionType.Delete);
        }
        if (CollectionUtils.notEmpty(deleteChildGroupList)) {
            List<IObjectData> groupList = serviceFacade.findObjectDataByIds(user.getTenantId(), Lists.newArrayList(deleteChildGroupList), Utils.PRODUCT_GROUP_API_NAME);
            if (CollectionUtils.notEmpty(groupList)) {
                deletedGroupList.addAll(groupList);
            }
        }
        deleteBomGroup(user, new ArrayList<>(deletedGroupList), groupDesc);
    }

    /**
     * 如果库里还有此产品的bom节点，就只删除该节点，如果没有，需要同时删除其下的子节点
     *
     * @param user
     * @param needDelBomList
     * @param bomDesc
     * @param allNodeList
     * @return 已删除的bom id
     */


    private void deletedBomNode(User user, List<IObjectData> needDelBomList, IObjectDescribe bomDesc, List<IObjectData> allNodeList) {
        if (CollectionUtils.notEmpty(needDelBomList)) {
            deletedBom(user, new ArrayList<>(needDelBomList), bomDesc, allNodeList);
        }
    }


    /**
     * 保存bom分组
     *
     * @param user
     * @param groupList
     * @param groupDesc
     * @param bomDataList
     */


    private void saveBomGroup(User user, List<IObjectData> groupList, IObjectDescribe groupDesc, List<IObjectData> bomDataList) {
        ArrayList<IObjectData> create = Lists.newArrayList();
        ArrayList<IObjectData> update = Lists.newArrayList();
        for (IObjectData groupData : groupList) {
            Object actionType = groupData.get("action_type");
            if (Objects.nonNull(actionType)) {
                if (actionType.toString().equals("create")) {
                    create.add(groupData);
                }
                if (actionType.toString().equals("update")) {
                    update.add(groupData);
                }
            }
        }
        if (CollectionUtils.notEmpty(create)) {
            create.stream().filter(x -> StringUtils.isNotBlank(x.get(BomConstants.FIELD_MAX_PROD_COUNT, String.class))
                    && StringUtils.isNotBlank(x.getId())
                    && StringUtils.isNotBlank(x.getName())).forEach(x -> {
                long count = bomDataList.stream().filter(d -> StringUtils.equals(x.getId(), d.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class))
                        && d.get(BomConstants.FIELD_SELECTED_BY_DEFAULT, Boolean.class, false)).count();
                if (count > Long.parseLong(x.get(BomConstants.FIELD_MAX_PROD_COUNT, String.class))) {
                    throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_RULE_WARN, x.getName()));
                }
            });
            createBomGroup(user, create, groupDesc);
        }
        if (CollectionUtils.notEmpty(update)) {
            updateBomGroup(user, update, groupDesc);
        }
    }


    //保存bom
    private ArrayList<IObjectData> saveBom(User user, List<IObjectData> bomList, IObjectDescribe bomDesc, String rootBomId, StopWatch stopWatch, String masterId) {
        ArrayList<IObjectData> create = Lists.newArrayList();
        ArrayList<IObjectData> update = Lists.newArrayList();
        ArrayList<IObjectData> allList = Lists.newArrayList();
        List<String> bomIds = Lists.newArrayList();
        Map<String, String> bomIdProductId = Maps.newHashMap();
        boolean simpleBom = SFAConfigUtil.isSimpleBom(user.getTenantId());
        for (IObjectData bomData : bomList) {

            Object actionType = bomData.get("action_type");
            if (Objects.nonNull(actionType)) {
                if (simpleBom) {
                    bomData.set(BomConstants.FIELD_IS_REQUIRED, true);
                    bomData.set(BomConstants.FIELD_SELECTED_BY_DEFAULT, true);
                    bomData.set(BomConstants.FIELD_PRICE_EDITABLE, false);
                    bomData.set(BomConstants.FIELD_AMOUNT_EDITABLE, false);
                    bomData.set(BomConstants.FIELD_INCREMENT, "");
                }
                if (actionType.toString().equals("create")) {
                    create.add(bomData);
                }
                if (actionType.toString().equals("update")) {
                    Boolean isRequired = bomData.get("is_required", Boolean.class);
                    if (Objects.equals(isRequired, Boolean.TRUE)) {
                        bomIds.add(bomData.getId());
                        bomIdProductId.put(bomData.getId(), bomData.get(BomConstants.FIELD_PRODUCT_ID, String.class));
                    }
                    update.add(bomData);
                }
                bomData.set(BomConstants.FIELD_CORE_ID, masterId);
            }
        }
        stopWatch.lap("preSaveBomData");
        if (!simpleBom) {
            try {
                checkBomConstraint(user, rootBomId, bomIds, bomIdProductId);
            } catch (Exception e) {
                log.warn("bom save warn:{}", e.getMessage());
            }
        }
        stopWatch.lap("checkBomConstraint");
        if (CollectionUtils.notEmpty(create)) {
            List<IObjectData> addList = createBom(user, create, bomDesc);
            allList.addAll(addList);
        }
        stopWatch.lap("createBom");
        if (CollectionUtils.notEmpty(update)) {
            List<IObjectData> updateList = updateBom(user, update, bomDesc);
            allList.addAll(updateList);
        }
        stopWatch.lap("updateBom");
        productStatusChangeTaskService.createOrUpdateTask(user.getTenantId(), Lists.newArrayList(), bomIds);
        stopWatch.lap("createOrUpdateTask");
        return allList;
    }

    private void checkBomConstraint(User user, String rootBomId, List<String> bomIds, Map<String, String> bomIdProductId) {
        if (CollectionUtils.empty(bomIds)) {
            return;
        }
        List<IObjectData> bomConstraintList = bomConstraintService.findBomConstrainByRootBomId(user, Collections.singletonList(rootBomId), true);
        if (CollectionUtils.empty(bomConstraintList)) {
            return;
        }
        List<IObjectData> bomConstraintLineList = bomConstraintService.findBomConstraintLinesByMasterId(user, Collections.singletonList(bomConstraintList.get(0).getId()));
        if (CollectionUtils.empty(bomConstraintLineList)) {
            return;
        }
        List<String> ids = Lists.newArrayList();
        bomConstraintLineList.forEach(x -> {
            String json = x.get(BomConstraintConstants.RESULT_RANGE, String.class);
            List<BomConstraintLineModel> bomConstraintLineModels = JSON.parseArray(json, BomConstraintLineModel.class);
            bomConstraintLineModels.forEach(b -> {
                if (CollectionUtils.empty(b.getAttribute()) && Objects.nonNull(bomIdProductId.get(b.getBom_id()))) {
                    ids.add(bomIdProductId.get(b.getBom_id()));
                }
            });
        });
        if (CollectionUtils.empty(ids)) {
            return;
        }
        Map<String, String> productName = ProductConstraintUtil.getProductName(user, Sets.newHashSet(ids));
        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_LINES_CANNOT_REQUIRED, productName.values()));
    }

    private void executeCustomFunction(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        IUdefFunction function = serviceFacade.getFunctionLogicService().findUDefFunction(user, "func_Ge2c9__c", Utils.BOM_API_NAME);
        if (function == null) {
            log.warn("function is null");
            return;
        }
        if (!function.isActive()) {
            log.warn("function is not active");
            return;
        }
        if (!"flow".equals(function.getNameSpace())) {
            log.warn("function name space is not flow");
            return;
        }
        objectDataList.parallelStream().forEach(objectData -> {
            List<String> fieldNames = function.getParameters();
            Map<String, Object> paramMap = Maps.newHashMap();
            for (String fieldName : fieldNames) {
                paramMap.put(fieldName, objectData.get(fieldName));
            }
            serviceFacade.getFunctionLogicService().executeUDefFunction(user, function, paramMap, objectData, Maps.newHashMap());
        });
    }

    private void createBomGroup(User user, List<IObjectData> groupList, IObjectDescribe bomDesc) {
        List<IObjectData> iObjectData = serviceFacade.bulkSaveObjectData(groupList, user);
        logAsync(user, bomDesc, iObjectData, EventType.ADD, ActionType.Add);
    }

    public void updateBomGroup(User user, List<IObjectData> groupList, IObjectDescribe bomDesc) {
        List<IObjectData> iObjectData = serviceFacade.batchUpdate(groupList, user);
        logAsync(user, bomDesc, iObjectData, EventType.MODIFY, ActionType.Modify);
    }

    private void deleteBomGroup(User user, List<IObjectData> groupList, IObjectDescribe groupDesc) {
        serviceFacade.bulkDeleteDirect(groupList, user);
        logAsync(user, groupDesc, groupList, EventType.DELETE, ActionType.Delete);
    }

    private List<IObjectData> createBom(User user, List<IObjectData> bomList, IObjectDescribe bomDesc) {
        List<IObjectData> iObjectData = serviceFacade.bulkSaveObjectData(bomList, user);
        logAsync(user, bomDesc, iObjectData, EventType.ADD, ActionType.Add);
        return iObjectData;
    }

    private List<IObjectData> updateBom(User user, List<IObjectData> bomList, IObjectDescribe bomDesc) {
        List<IObjectData> iObjectData = serviceFacade.batchUpdate(bomList, user);
        logAsync(user, bomDesc, iObjectData, EventType.MODIFY, ActionType.Modify);
        return iObjectData;
    }

    private void deletedBom(User user, List<IObjectData> needDelBomList, IObjectDescribe bomDesc, List<IObjectData> allNodeList) {
        logAsync(user, bomDesc, needDelBomList, EventType.DELETE, ActionType.Delete);
        List<String> bomIds = needDelBomList.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<IObjectData> delList = Lists.newArrayList();
        bomIds.parallelStream().forEach(x -> {
            List<IObjectData> deletedIdList = CollectionUtils.nullToEmpty(allNodeList).stream().filter(
                    s -> StringUtils.isNotBlank(s.get(BomConstants.FIELD_BOM_PATH, String.class)) &&
                            s.get(BomConstants.FIELD_BOM_PATH, String.class).contains(x) &&
                            !s.get("is_deleted", Boolean.class) && !bomIds.contains(s.getId())
            ).collect(Collectors.toList());
            delList.addAll(deletedIdList);
        });
        delList.addAll(needDelBomList);
        if (CollectionUtils.notEmpty(delList)) {
            serviceFacade.bulkDeleteDirect(delList, user);
        }
        productStatusChangeTaskService.createOrUpdateTask(user.getTenantId(), Lists.newArrayList(), bomIds);
    }

    private void logAsync(User user, IObjectDescribe objectDescribes, List<IObjectData> allObjectData, EventType eventType, ActionType actionType) {
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();

        parallelTask.submit(() -> serviceFacade.log(user, eventType, actionType, objectDescribes, allObjectData));
        parallelTask.run();
    }

    private List<IObjectData> findBomRootDataList(User user, List<String> productIds) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(200);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, DBRecord.ID, productIds);
        SearchUtil.fillFilterIsNull(filters, BomConstants.FIELD_PARENT_BOM_ID);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user
                , Utils.BOM_API_NAME, searchTemplateQuery);
        if (Objects.isNull(queryResult) || CollectionUtils.empty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }

    @Override
    public List<String> execute(User user, List<BomTreeModel.SubParam> nodeList, BomTreeModel.SubParam node, boolean same) {
        if (Objects.isNull(node)) {
            return Lists.newArrayList();
        }
        List<IObjectData> originNodes = queryNodeListByCond(user, node).stream()
                .filter(d -> d.get(BomConstants.FIELD_BOM_PATH, String.class).contains(node.getBomId().concat(".")))
                .sorted(Comparator.comparing(d -> Splitter.on(".").splitToList(d.get(BomConstants.FIELD_BOM_PATH, String.class)).size()))
                .collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(originNodes)) {
            return Lists.newArrayList();
        }

        if (same) {
            if (StringUtils.isAnyBlank(node.getBomId(), node.getRootId(), node.getProductId())) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_MISS_PARAM));
            }
            List<IObjectData> allProductNodeList = queryNodeListByProductId(user, node);
            if (CollectionUtils.empty(allProductNodeList)) {
                return Lists.newArrayList();
            }
            List<BomTreeModel.SubParam> newNodeList = Lists.newArrayList();
            allProductNodeList.parallelStream().forEach(x -> {
                BomTreeModel.SubParam entity = new BomTreeModel.SubParam();
                entity.setRootId(x.get(BomConstants.FIELD_ROOT_ID, String.class));
                entity.setBomId(x.getId());
                entity.setPackageFlag(true);
                if (StringUtils.isNotBlank(x.get(BomConstants.FIELD_PARENT_BOM_ID, String.class))) {
                    entity.setEnabledStatus(x.get(BomConstants.FIELD_ENABLED_STATUS, Boolean.class, true));
                    entity.setPriceEditable(x.get(BomConstants.FIELD_PRICE_EDITABLE, Boolean.class, true));
                }
                newNodeList.add(entity);
            });
            nodeList = Lists.newArrayList(newNodeList);
        } else {
            List<String> productIdList = CollectionUtils.nullToEmpty(nodeList).stream().filter(x -> !x.isPackageFlag()).map(BomTreeModel.SubParam::getProductId).collect(Collectors.toList());
            List<String> packageProductList = CollectionUtils.nullToEmpty(nodeList).stream().filter(BomTreeModel.SubParam::isPackageFlag).map(BomTreeModel.SubParam::getProductId).collect(Collectors.toList());
            BomTreeModel.SubParam createNode;
            if (CollectionUtils.notEmpty(packageProductList)) {
                List<IObjectData> boms = findBomByRootProductId(user, packageProductList);
                if (CollectionUtils.notEmpty(boms)) {
                    List<String> productIds = boms.stream().map(x -> x.get("product_id", String.class)).collect(Collectors.toList());
                    packageProductList.removeIf(productIds::contains);
                    productIdList.addAll(packageProductList);
                    for (IObjectData bom : boms) {
                        createNode = new BomTreeModel.SubParam();
                        createNode.setBomId(bom.getId());
                        createNode.setRootId(bom.get(BomConstants.FIELD_ROOT_ID, String.class));
                        createNode.setProductId(bom.get(BomConstants.FIELD_PRODUCT_ID, String.class));
                        createNode.setPackageFlag(true);
                        nodeList.add(createNode);
                    }
                }
            }
            if (CollectionUtils.notEmpty(productIdList)) {
                for (String productId : productIdList) {
                    String rootBomId = serviceFacade.generateId();
                    createNode = new BomTreeModel.SubParam();
                    createNode.setBomId(rootBomId);
                    createNode.setRootId(rootBomId);
                    createNode.setProductId(productId);
                    createNode.setPackageFlag(false);
                    nodeList.add(createNode);
                }
            }

            if (CollectionUtils.notEmpty(nodeList)) {
                nodeList.removeIf(x -> StringUtils.isBlank(x.getBomId()));
            }
        }
        if (CollectionUtils.empty(nodeList)) {
            return Lists.newArrayList();
        }
        if (nodeList.size() > 20) {
            //超过20条发送异步任务
            return syncBomSendMQ(user, nodeList, node, same);
        }
        List<String> productIds = originNodes.stream().map(x -> x.get(BomConstants.FIELD_PRODUCT_ID, String.class)).collect(Collectors.toList());
        List<String> groupIds = originNodes.stream().map(x -> x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<IObjectData> groupList = queryGroupListByCond(user, groupIds);
        List<IObjectData> deleteList = Lists.newCopyOnWriteArrayList();
        List<IObjectData> addList = Lists.newCopyOnWriteArrayList();
        List<IObjectData> groupAddList = Lists.newCopyOnWriteArrayList();
        List<String> nonSyncNode = Lists.newCopyOnWriteArrayList();
        List<String> productList = Lists.newCopyOnWriteArrayList();
        nodeList.parallelStream().forEach(x -> {
            if (!x.isPackageFlag()) {
                if (productIds.contains(x.getProductId())) {
                    nonSyncNode.add(x.getProductId());
                } else {
                    IObjectData bomData = new ObjectData();
                    bomData.set(BomConstants.FIELD_PRODUCT_ID, x.getProductId());
                    bomData.set(IObjectData.DESCRIBE_API_NAME, Utils.BOM_API_NAME);
                    bomData.setTenantId(user.getTenantId());
                    bomData.setLastModifiedTime(System.currentTimeMillis());
                    bomData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
                    bomData.setCreateTime(System.currentTimeMillis());
                    bomData.setCreatedBy(user.getUpstreamOwnerIdOrUserId());
                    bomData.setId(x.getBomId());
                    bomData.set("root_id", x.getRootId());
                    bomData.set("bom_path", x.getRootId());
                    bomData.set(BomConstants.FIELD_AMOUNT, "1");
                    addList.add(bomData);
                    productList.add(x.getProductId());
                    createBomAndGroup(node, originNodes, groupList, addList, groupAddList, x, x.getRootId());
                }
            } else {
                List<IObjectData> bomNode = queryNodeListByCond(user, x);
                //bom结构同步子节点不能和根节点是同一个产品
                Set<String> tmpSet = bomNode.stream().filter(d -> StringUtils.isBlank(d.get(BomConstants.FIELD_PARENT_BOM_ID, String.class)))
                        .map(d -> d.get(BomConstants.FIELD_PRODUCT_ID, String.class))
                        .filter(productIds::contains).collect(Collectors.toSet());
                nonSyncNode.addAll(tmpSet);
                bomNode.stream().filter(d -> StringUtils.isBlank(d.get(BomConstants.FIELD_PARENT_BOM_ID, String.class)))
                        .filter(d -> !productIds.contains(d.get(BomConstants.FIELD_PRODUCT_ID))).findFirst().ifPresent(r -> {
                    deleteList.addAll(bomNode.stream().filter(d -> d.get(BomConstants.FIELD_BOM_PATH, String.class).contains(x.getBomId().concat("."))).collect(Collectors.toList()));
                    //要同步的路径
                    Optional<String> path = bomNode.stream().filter(n -> n.getId().equals(x.getBomId())).map(n -> n.get(BomConstants.FIELD_BOM_PATH, String.class)).findFirst();
                    path.ifPresent(p -> createBomAndGroup(node, originNodes, groupList, addList, groupAddList, x, p));
                });
            }
        });
        handleData(nodeList, nonSyncNode, user, deleteList, addList, groupAddList, productList);
        return ProductConstraintUtil.queryProductName(nonSyncNode, user);
    }

    private List<String> syncBomSendMQ(User user, List<BomTreeModel.SubParam> nodeList, BomTreeModel.SubParam node, boolean same) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("node", node);
        param.put("nodeList", nodeList);
        param.put("same", same);
        param.put("tenantId", user.getTenantId());
        param.put("userId", user.getUpstreamOwnerIdOrUserId());
        asyncTaskProducer.create("sync_bom_to_others", JSON.toJSONString(param), user.getTenantId().concat("@").concat(node.getBomId()));
        return Lists.newArrayList();
    }

    private void handleData(List<BomTreeModel.SubParam> nodeList, List<String> nonSyncNode, User user, List<IObjectData> deleteBomList, List<IObjectData> addBomList, List<IObjectData> groupAddList, List<String> productList) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(deleteBomList)) {
            if (deleteBomList.size() > MAX_SIZE) {
                Lists.partition(deleteBomList, MAX_SIZE).stream().forEach(x -> serviceFacade.bulkDeleteDirect(x, user));
            } else {
                serviceFacade.bulkDeleteDirect(deleteBomList, user);
            }
        }
        List<String> groupIdList = deleteBomList.stream().filter(x -> StringUtils.isNotBlank(x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)))
                .map(x -> x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)).distinct().collect(Collectors.toList());
        List<IObjectData> deleteGroupList = queryGroupListByCond(user, groupIdList);
        deleteGroupList.addAll(queryGroupListByBomId(user, nodeList.stream().filter(x -> !nonSyncNode.contains(x.getProductId())).map(BomTreeModel.SubParam::getBomId).collect(Collectors.toList())));
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(deleteGroupList)) {
            serviceFacade.bulkDeleteDirect(deleteGroupList, user);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(groupAddList)) {
            serviceFacade.bulkSaveObjectData(groupAddList, user);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(addBomList)) {
            if (addBomList.size() > MAX_SIZE) {
                log.warn("syncToOtherNode: limit {} exceed max value {} ", addBomList.size(), MAX_SIZE);
                Lists.partition(addBomList, MAX_SIZE).stream().forEach(x -> serviceFacade.bulkSaveObjectData(x, user));
            } else {
                serviceFacade.bulkSaveObjectData(addBomList, user);
            }
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(productList)) {
            updateProductIsPackage(user, productList, Boolean.TRUE);
        }
    }

    private void createBomAndGroup(BomTreeModel.SubParam node, List<IObjectData> objectDatas, List<IObjectData> groupList, List<IObjectData> addList, List<IObjectData> groupAddList, BomTreeModel.SubParam x, String p) {
        Map<String, String> parentIdMap = Maps.newHashMap();
        Map<String, String> parentBomPathMap = Maps.newHashMap();
        Map<String, String> groupIdMap = Maps.newHashMap();
        List<IObjectData> dataList = ObjectDataExt.copyList(objectDatas);
        for (IObjectData d : dataList) {
            d.setId(serviceFacade.generateId());
            d.set(BomConstants.FIELD_BOM_PATH, StringUtils.substringAfterLast(d.get(BomConstants.FIELD_BOM_PATH, String.class), node.getBomId()));
            parentIdMap.put(d.get(BomConstants.FIELD_BOM_PATH, String.class), d.getId());
            String preBomPath = StringUtils.substringBeforeLast(d.get(BomConstants.FIELD_BOM_PATH, String.class), ".");
            d.set(BomConstants.FIELD_ROOT_ID, x.getRootId());
            if (node.getBomId().equals(d.get(BomConstants.FIELD_PARENT_BOM_ID, String.class))) {
                d.set(BomConstants.FIELD_PARENT_BOM_ID, x.getBomId());
                d.set(BomConstants.FIELD_BOM_PATH, p.concat(".").concat(d.getId()));
                parentBomPathMap.put(d.getId(), p.concat(".").concat(d.getId()));
            } else {
                String parentBomPath = parentBomPathMap.get(parentIdMap.get(preBomPath));
                if (StringUtils.isBlank(parentBomPath)) {
                    continue;
                }
                d.set(BomConstants.FIELD_PARENT_BOM_ID, parentIdMap.get(preBomPath));
                parentBomPath = parentBomPath.concat(".").concat(d.getId());
                d.set(BomConstants.FIELD_BOM_PATH, parentBomPath);
                parentBomPathMap.put(d.getId(), parentBomPath);
            }
            if (BooleanUtils.isFalse(x.isEnabledStatus())) {
                d.set(BomConstants.FIELD_ENABLED_STATUS, false);
            }
            if (BooleanUtils.isFalse(x.isPriceEditable())) {
                d.set(BomConstants.FIELD_PRICE_EDITABLE, false);
            }
            String groupId = d.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class);
            if (StringUtils.isNotBlank(groupId)) {
                groupList.stream().filter(g -> groupId.equals(g.getId())).findFirst().ifPresent(g -> {
                    String oldValue = groupIdMap.putIfAbsent(groupId, serviceFacade.generateId());
                    if (StringUtils.isBlank(oldValue)) {
                        IObjectData newGroup = ObjectDataExt.of(g).copy();
                        newGroup.setId(groupIdMap.get(groupId));
                        newGroup.set(BomConstants.FIELD_PARENT_BOM_ID, d.get(BomConstants.FIELD_PARENT_BOM_ID));
                        groupAddList.add(newGroup);
                    }
                    d.set(BomConstants.FIELD_PRODUCT_GROUP_ID, groupIdMap.get(groupId));
                });
            }
            addList.add(d);
        }
    }

    private List<IObjectData> queryNodeListByCond(User user, BomTreeModel.SubParam x) {
        return queryNodeListByCond(user, x.getRootId());
    }

    private List<IObjectData> queryNodeListByProductId(User user, BomTreeModel.SubParam x) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(2000);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, BomConstants.FIELD_PRODUCT_ID, x.getProductId());
        SearchUtil.fillFilterNotEq(filters, DBRecord.ID, x.getBomId());
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, 0);
        searchTemplateQuery.setFilters(filters);
        return serviceFacade.findBySearchQueryIgnoreAll(user, Utils.BOM_API_NAME, searchTemplateQuery).getData();
    }

    private List<IObjectData> queryGroupListByCond(User user, List<String> ids) {
        if (CollectionUtils.empty(ids)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(500);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, DBRecord.ID, ids);
        searchTemplateQuery.setFilters(filters);
        return serviceFacade.findBySearchQueryIgnoreAll(user, Utils.PRODUCT_GROUP_API_NAME, searchTemplateQuery).getData();
    }

    private List<IObjectData> queryGroupListByBomId(User user, List<String> parentBomId) {
        if (CollectionUtils.empty(parentBomId)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(500);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, BomConstants.FIELD_PARENT_BOM_ID, parentBomId);
        searchTemplateQuery.setFilters(filters);
        return serviceFacade.findBySearchQueryIgnoreAll(user, Utils.PRODUCT_GROUP_API_NAME, searchTemplateQuery).getData();
    }

    private List<IObjectData> findBomByRootProductId(User user, List<String> productIds) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(2000);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIsNull(filters, BomConstants.FIELD_PARENT_BOM_ID);
        SearchUtil.fillFilterEq(filters, "life_status", "normal");
        SearchUtil.fillFilterEq(filters, "is_deleted", "0");
        SearchUtil.fillFilterIn(filters, BomConstants.FIELD_PRODUCT_ID, productIds);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(user, Utils.BOM_API_NAME, searchTemplateQuery);
        return CollectionUtils.nullToEmpty(queryResult.getData());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConfigFactory.getConfig("fs-crm-sales-config", config -> {
            String vipBomNodeCount = config.get("vip_bom_node_count", "");
            if (StringUtils.isNotBlank(vipBomNodeCount) && !StringUtils.equals("*", vipBomNodeCount)) {
                vipBomNodeMap = JSON.parseObject(vipBomNodeCount, Map.class);
            }
        });
    }

    @Override
    public void checkBomCycle(User user, IObjectData masterData, List<IObjectData> dataList, int depth) {
        if (depth > 50) {
            return;
        }
        String masterId = masterData.getId();
        if (CollectionUtils.empty(dataList) && StringUtils.isBlank(masterId)) {
            return;
        }
        dataList.removeIf(x -> StringUtils.isBlank(x.get(BomConstants.FIELD_RELATED_CORE_ID, String.class)));
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        Map<String, String> pathMap = Maps.newHashMap();
        dataList.forEach(x -> {
            String path = x.get(BomConstants.FIELD_RELATED_CORE_PATH, String.class);
            if (StringUtils.isBlank(path)) {
                x.set(BomConstants.FIELD_RELATED_CORE_PATH, x.get(BomConstants.FIELD_RELATED_CORE_ID, String.class));
            } else {
                x.set(BomConstants.FIELD_RELATED_CORE_PATH, path.concat(".").concat(x.get(BomConstants.FIELD_CORE_ID, String.class)));
            }
            pathMap.put(x.get(BomConstants.FIELD_RELATED_CORE_ID, String.class), x.get(BomConstants.FIELD_RELATED_CORE_PATH, String.class));
            if (Objects.equals(x.get(BomConstants.FIELD_RELATED_CORE_ID, String.class), masterId)) {
                String coreName = "";
                String coreId = StringUtils.substringBefore(x.get(BomConstants.FIELD_RELATED_CORE_PATH, String.class), ".");
                if (StringUtils.isNotBlank(coreId)) {
                    List<IObjectData> coreList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(coreId), Utils.BOM_CORE_API_NAME);
                    if (CollectionUtils.notEmpty(coreList)) {
                        coreName = coreList.get(0).getName();
                    }
                }
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_RELATED_BOM_CYCLE, coreName));
            }
        });
        Set<String> relatedCoreIds = dataList.stream().map(x -> x.get(BomConstants.FIELD_RELATED_CORE_ID, String.class)).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(2000);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, BomConstants.FIELD_CORE_ID, relatedCoreIds);
        SearchUtil.fillFilterIsNotNull(filters, BomConstants.FIELD_RELATED_CORE_ID);
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, "0");
        SearchUtil.fillFilterEq(filters, ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, Utils.BOM_API_NAME, searchTemplateQuery, BomConstants.FILTER_QUERY_FIELD);
        if (queryResult != null && CollectionUtils.notEmpty(queryResult.getData())) {
            queryResult.getData().forEach(d -> {
                d.set(BomConstants.FIELD_RELATED_CORE_PATH, pathMap.get(d.get(BomConstants.FIELD_CORE_ID, String.class)));
            });
            depth = depth + 1;
            checkBomCycle(user, masterData, queryResult.getData(), depth);
        }
    }

    @Override
    public void checkType(List<IObjectData> detailList, IObjectData objectData) {
        if (CollectionUtils.empty(detailList)) {
            return;
        }
        if (Objects.equals(objectData.get(BomCoreConstants.FIELD_CATEGORY, String.class), BomCoreConstants.category.standard.getValue())) {
            detailList.forEach(x -> {
                if (BooleanUtils.isFalse(x.get(BomConstants.FIELD_IS_REQUIRED, Boolean.class, false))) {
                    x.set(BomConstants.FIELD_IS_REQUIRED, true);
                }
                if (BooleanUtils.isFalse(x.get(BomConstants.FIELD_SELECTED_BY_DEFAULT, Boolean.class, false))) {
                    x.set(BomConstants.FIELD_SELECTED_BY_DEFAULT, true);
                }
                if (BooleanUtils.isTrue(x.get(BomConstants.FIELD_PRICE_EDITABLE, Boolean.class, true))) {
                    x.set(BomConstants.FIELD_PRICE_EDITABLE, false);
                }
                if (BooleanUtils.isTrue(x.get(BomConstants.FIELD_AMOUNT_EDITABLE, Boolean.class, true))) {
                    x.set(BomConstants.FIELD_AMOUNT_EDITABLE, false);
                }
                if (StringUtils.isNotBlank(x.get(BomConstants.FIELD_INCREMENT, String.class, ""))) {
                    x.set(BomConstants.FIELD_INCREMENT, "");
                }
                if (Objects.equals(x.get(BomConstants.FIELD_NODE_BOM_CORE_TYPE, String.class), BomCoreConstants.category.configure.getValue())) {
                    throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_NODE_TYPE_WARN));
                }
            });
        }
    }

    @Override
    public void checkNonStandardProduct(IObjectData objectData, User user) {
        String productId = objectData.get(BomConstants.FIELD_PRODUCT_ID, String.class);
        if (StringUtils.isBlank(productId) || !bizConfigThreadLocalCacheService.isOpenNonStandardProduct(user.getTenantId())) {
            return;
        }
        IObjectData productData = serviceFacade.findObjectDataIgnoreAll(user, productId, Utils.PRODUCT_API_NAME);
        if (productData != null && Objects.equals(productData.get(ProductConstants.PRODUCT_TYPE, String.class), ProductConstants.ProductTypeEnum.NON_STANDARD.getValue())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_NON_STANDARD_PRODUCT_CANNOT_BOM));
        }
    }

    @Override
    public void checkSaleStrategy(IObjectDescribe objectDescribe, String tenantId, List<String> nodeCoreList, String saleStrategy) {
        SelectOneFieldDescribe fieldDescribe = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe(BomCoreConstants.SALE_STRATEGY);
        if (Objects.isNull(fieldDescribe) || StringUtils.isBlank(saleStrategy) || CollectionUtils.empty(nodeCoreList)) {
            return;
        }
        if (CollectionUtils.notEmpty(fieldDescribe.getSelectOptions()) && fieldDescribe.getSelectOptions().size() > 1) {
            List<IObjectData> coreList = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, nodeCoreList, saleStrategy);
            if (CollectionUtils.empty(coreList)) {
                return;
            }
            coreList.forEach(x -> {
                String val = x.get(BomCoreConstants.SALE_STRATEGY, String.class);
                if (StringUtils.isNotBlank(val) && !Objects.equals(saleStrategy, val)) {
                    throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CORE_SALE_STRATEGY_CHECK, x.getName()));
                }
            });
        }
    }
}
