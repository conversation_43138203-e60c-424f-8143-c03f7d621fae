package com.facishare.crm.sfa.utilities.util;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.AvailableRangeCoreService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.common.UseRangeFieldDataRender;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.QueryDeptByName;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_OBJECT_DISPLAY_NAME_NOTNULL;

/**
 * <AUTHOR>
 * @since 2022/8/18
 */
@Slf4j
public class PriceBookImportUtils {

    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);
    private static final AvailableRangeCoreService availableRangeCoreService = SpringUtil.getContext().getBean(AvailableRangeCoreService.class);
    private static final BizConfigThreadLocalCacheService configService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    private static final Set<String> DETAIL_API_NAMES = Sets.newHashSet(SFAPreDefineObject.PriceBookAccount.getApiName());
    private static final int BATCH_MAX_LIMIT = 1000;
    private static final List<String> removeFields = Lists.newArrayList("mc_currency", "mc_exchange_rate");

    public static void removeAndAddFields(String tenantId, List<IFieldDescribe> fieldDescribes) {
        if (!GrayUtil.isPriceBookReform(tenantId)) {
            return;
        }
        Set<String> toRemoveFields = Sets.newHashSet(PriceBookConstants.Field.APPLY_ACCOUNT_RANGE.getApiName(),
                PriceBookConstants.Field.APPLY_ORG_RANGE.getApiName(),
                PriceBookConstants.Field.ORG_IDS.getApiName(),
                PriceBookConstants.Field.PRIORITY.getApiName(),
                PriceBookConstants.Field.CALCULATE_STATUS.getApiName(),
                PriceBookConstants.Field.APPLY_PARTNER_RANGE.getApiName(),
                PriceBookConstants.Field.PARTNER_IDS.getApiName());
        boolean hasPartner = fieldDescribes.stream()
                .anyMatch(f -> PriceBookConstants.Field.APPLY_PARTNER_RANGE.getApiName().equals(f.getApiName()));
        fieldDescribes.removeIf(f -> toRemoveFields.contains(f.getApiName()));
        addVirtualFields(fieldDescribes, hasPartner);
    }

    public static void removeFields(String tenantId, List<IFieldDescribe> fieldDescribes) {
        if (!GrayUtil.isPriceBookReform(tenantId)) {
            return;
        }
        Set<String> toRemoveFields = Sets.newHashSet(PriceBookConstants.Field.APPLY_ACCOUNT_RANGE.getApiName(),
                PriceBookConstants.Field.APPLY_ORG_RANGE.getApiName(),
                PriceBookConstants.Field.ORG_IDS.getApiName(),
                PriceBookConstants.Field.PRIORITY.getApiName(),
                PriceBookConstants.Field.CALCULATE_STATUS.getApiName(),
                PriceBookConstants.Field.APPLY_PARTNER_RANGE.getApiName(),
                PriceBookConstants.Field.PARTNER_IDS.getApiName());
        fieldDescribes.removeIf(f -> toRemoveFields.contains(f.getApiName()));
    }

    private static void addVirtualFields(List<IFieldDescribe> fieldDescribes, boolean hasPartner) {
        addOrgRangeField(fieldDescribes);
        addAccountRangeField(fieldDescribes);
        addPartnerRangeField(fieldDescribes, hasPartner);
    }

    public static void addVirtualFields(String tenantId, IObjectDescribe objectDescribe) {
        if (!GrayUtil.isPriceBookReform(tenantId)) {
            return;
        }
        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes();
        boolean hasPartner = fieldDescribes.stream()
                .anyMatch(f -> PriceBookConstants.Field.APPLY_PARTNER_RANGE.getApiName().equals(f.getApiName()));
        addVirtualFields(fieldDescribes, hasPartner);
        objectDescribe.setFieldDescribes(fieldDescribes);
    }

    private static void addOrgRangeField(List<IFieldDescribe> fieldDescribes) {
        SelectOneFieldDescribe selectOneFieldDescribe = new SelectOneFieldDescribe();
        selectOneFieldDescribe.setRequired(Boolean.TRUE);
        selectOneFieldDescribe.setApiName(PriceBookConstants.Field.VIRTUAL_APPLY_ORG_RANGE.getApiName());
        selectOneFieldDescribe.setActive(Boolean.TRUE);
        selectOneFieldDescribe.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        selectOneFieldDescribe.setLabel(I18N.text("PriceBookObj.field.virtual_apply_org_range.label"));
        selectOneFieldDescribe.setCreateTime(System.currentTimeMillis());
        List<ISelectOption> options = Lists.newArrayList();
        ISelectOption optionAll = new SelectOption();
        optionAll.setValue(PriceBookConstants.VirtualFieldOption.ALL.getValue());
        optionAll.setLabel(I18N.text("PriceBookObj.field.virtual_apply_org_range.option.all"));
        options.add(optionAll);
        ISelectOption optionOrg = new SelectOption();
        optionOrg.setValue(PriceBookConstants.VirtualFieldOption.ORGANIZATION.getValue());
        optionOrg.setLabel(I18N.text("PriceBookObj.field.virtual_apply_org_range.option.org"));
        options.add(optionOrg);
        selectOneFieldDescribe.setSelectOptions(options);
        fieldDescribes.add(selectOneFieldDescribe);
        TextFieldDescribe employeeField = new TextFieldDescribe();
        employeeField.setApiName(PriceBookConstants.Field.VIRTUAL_EMPLOYEE_NAME.getApiName());
        employeeField.setRequired(Boolean.FALSE);
        employeeField.setActive(Boolean.TRUE);
        employeeField.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        employeeField.setLabel(I18N.text("PriceBookObj.field.virtual_employee_name.label"));
        employeeField.setCreateTime(System.currentTimeMillis());
        fieldDescribes.add(employeeField);
        TextFieldDescribe deptField = new TextFieldDescribe();
        deptField.setApiName(PriceBookConstants.Field.VIRTUAL_DEPT_NAME.getApiName());
        deptField.setRequired(Boolean.FALSE);
        deptField.setActive(Boolean.TRUE);
        deptField.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        deptField.setLabel(I18N.text("PriceBookObj.field.virtual_dept_name.label"));
        deptField.setCreateTime(System.currentTimeMillis());
        fieldDescribes.add(deptField);
    }

    private static void addAccountRangeField(List<IFieldDescribe> fieldDescribes) {
        SelectOneFieldDescribe selectOneFieldDescribe = new SelectOneFieldDescribe();
        selectOneFieldDescribe.setRequired(Boolean.TRUE);
        selectOneFieldDescribe.setApiName(PriceBookConstants.Field.VIRTUAL_APPLY_ACCOUNT_RANGE.getApiName());
        selectOneFieldDescribe.setActive(Boolean.TRUE);
        selectOneFieldDescribe.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        selectOneFieldDescribe.setLabel(I18N.text("PriceBookObj.field.virtual_apply_account_range.label"));
        selectOneFieldDescribe.setCreateTime(System.currentTimeMillis());
        List<ISelectOption> options = Lists.newArrayList();
        ISelectOption optionAll = new SelectOption();
        optionAll.setValue(PriceBookConstants.VirtualFieldOption.ALL.getValue());
        optionAll.setLabel(I18N.text("PriceBookObj.field.virtual_apply_org_range.option.all"));
        options.add(optionAll);
        ISelectOption optionSpecified = new SelectOption();
        optionSpecified.setValue(PriceBookConstants.VirtualFieldOption.SPECIFIED.getValue());
        optionSpecified.setLabel(I18N.text("PriceBookObj.field.virtual_apply_account_range.option.specified"));
        options.add(optionSpecified);
        selectOneFieldDescribe.setSelectOptions(options);
        fieldDescribes.add(selectOneFieldDescribe);
    }

    private static void addPartnerRangeField(List<IFieldDescribe> fieldDescribes, boolean hasPartner) {
        if (!hasPartner) {
            return;
        }
        SelectOneFieldDescribe selectOneFieldDescribe = new SelectOneFieldDescribe();
        selectOneFieldDescribe.setRequired(Boolean.TRUE);
        selectOneFieldDescribe.setApiName(PriceBookConstants.Field.VIRTUAL_APPLY_PARTNER_RANGE.getApiName());
        selectOneFieldDescribe.setActive(Boolean.TRUE);
        selectOneFieldDescribe.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        selectOneFieldDescribe.setLabel(I18N.text("PriceBookObj.field.virtual_apply_partner_range.label"));
        selectOneFieldDescribe.setCreateTime(System.currentTimeMillis());
        List<ISelectOption> options = Lists.newArrayList();
        ISelectOption optionAll = new SelectOption();
        optionAll.setValue(PriceBookConstants.VirtualFieldOption.ALL.getValue());
        optionAll.setLabel(I18N.text("PriceBookObj.field.virtual_apply_org_range.option.all"));
        options.add(optionAll);
        ISelectOption optionNone = new SelectOption();
        optionNone.setValue(PriceBookConstants.VirtualFieldOption.NONE.getValue());
        optionNone.setLabel(I18N.text("PriceBookObj.field.virtual_apply_partner_range.option.none"));
        options.add(optionNone);
        selectOneFieldDescribe.setSelectOptions(options);
        fieldDescribes.add(selectOneFieldDescribe);
    }

    public static void removeDetailFields(String tenantId, List<IFieldDescribe> detailFieldList) {
        if (!GrayUtil.isPriceBookReform(tenantId)) {
            return;
        }
        if (CollectionUtils.notEmpty(detailFieldList) && DETAIL_API_NAMES.contains(detailFieldList.get(0).getDescribeApiName())) {
            List<String> toRemoveFields = Lists.newArrayList("apply_range");
            detailFieldList.removeIf(f -> toRemoveFields.contains(f.getApiName()));
        }
    }

    public static void validateOrgRange(String tenantId, List<BaseImportDataAction.ImportData> dataList, List<BaseImportAction.ImportError> errorList) {
        if (!GrayUtil.isPriceBookReform(tenantId)) {
            return;
        }
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        for (BaseImportDataAction.ImportData importData : dataList) {
            String virtualApplyOrgRange = importData.getData().get(PriceBookConstants.Field.VIRTUAL_APPLY_ORG_RANGE.getApiName(), String.class);
            if (PriceBookConstants.VirtualFieldOption.ORGANIZATION.getValue().equals(virtualApplyOrgRange)) {
                String virtualEmpName = importData.getData().get(PriceBookConstants.Field.VIRTUAL_EMPLOYEE_NAME.getApiName(), String.class);
                String virtualDeptName = importData.getData().get(PriceBookConstants.Field.VIRTUAL_DEPT_NAME.getApiName(), String.class);
                if (Strings.isNullOrEmpty(virtualEmpName) && Strings.isNullOrEmpty(virtualDeptName)) {
                    String msg = I18N.text("PriceBookObj.field.virtual_employee_name.label") + " "
                            + I18N.text("PriceBookObj.field.virtual_dept_name.label");
                    errorList.add(new BaseImportAction.ImportError(importData.getRowNo(),
                            String.format(I18N.text(SFA_OBJECT_DISPLAY_NAME_NOTNULL), msg)));
                }
            }
        }
    }

    public static List<BaseImportAction.ImportError> convertVirtualFields2ActualFields(ActionContext actionContext, List<BaseImportDataAction.ImportData> dataList) {
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();
        if (!GrayUtil.isPriceBookReform(actionContext.getTenantId())) {
            return errorList;
        }
        if (CollectionUtils.empty(dataList)) {
            return errorList;
        }
        Set<String> empNameSet = Sets.newHashSet();
        Set<String> deptNameSet = Sets.newHashSet();
        for (BaseImportDataAction.ImportData importData : dataList) {
            IObjectData objectData = importData.getData();
            String virtualEmpName = objectData.get(PriceBookConstants.Field.VIRTUAL_EMPLOYEE_NAME.getApiName(), String.class);
            String virtualDeptName = objectData.get(PriceBookConstants.Field.VIRTUAL_DEPT_NAME.getApiName(), String.class);
            if (!Strings.isNullOrEmpty(virtualEmpName)) {
                empNameSet.addAll(splitAndTrim(virtualEmpName));
            }
            if (!Strings.isNullOrEmpty(virtualDeptName)) {
                deptNameSet.addAll(splitAndTrim(virtualDeptName));
            }
        }
        Map<String, String> empNameIdMap = Maps.newHashMap();
        Map<String, String> deptNameIdMap = Maps.newHashMap();
        putAllEmpName2Map(actionContext.getTenantId(), empNameSet, empNameIdMap);
        putAllDeptName2Map(actionContext.getTenantId(), deptNameSet, deptNameIdMap);
        for (BaseImportDataAction.ImportData importData : dataList) {
            IObjectData objectData = importData.getData();
            convertOrgRangeField(importData, empNameIdMap, deptNameIdMap, errorList);
            convertAccountRangeField(objectData);
            convertPartnerRangeField(objectData);
        }
        return errorList;
    }

    private static void putAllEmpName2Map(String tenantId, Set<String> empNameSet, Map<String, String> empNameIdMap) {
        if (CollectionUtils.notEmpty(empNameSet)) {
            List<UserInfo> userList = serviceFacade.getUserByName(tenantId, User.SUPPER_ADMIN_USER_ID,
                    Lists.newArrayList(empNameSet));
            if (CollectionUtils.notEmpty(userList)) {
                userList.forEach(u -> empNameIdMap.put(u.getName(), u.getId()));
            }
        }
    }

    private static void putAllDeptName2Map(String tenantId, Set<String> deptNameSet, Map<String, String> deptNameIdMap) {
        if (CollectionUtils.notEmpty(deptNameSet)) {
            List<QueryDeptByName.DeptInfo> deptList = serviceFacade.getDeptByName(tenantId, User.SUPPER_ADMIN_USER_ID,
                    Lists.newArrayList(deptNameSet));
            if (CollectionUtils.notEmpty(deptList)) {
                deptList.forEach(u -> deptNameIdMap.put(u.getName(), u.getId()));
            }
        }
    }

    private static void convertOrgRangeField(BaseImportDataAction.ImportData importData, Map<String, String> empNameIdMap,
                                             Map<String, String> deptNameIdMap, List<BaseImportAction.ImportError> errorList) {
        IObjectData objectData = importData.getData();
        String virtualApplyOrgRange = objectData.get(PriceBookConstants.Field.VIRTUAL_APPLY_ORG_RANGE.getApiName(), String.class);
        if (PriceBookConstants.VirtualFieldOption.ALL.getValue().equals(virtualApplyOrgRange)) {
            objectData.set(PriceBookConstants.Field.APPLY_ORG_RANGE.getApiName(), "{\"type\":\"ALL\"}");
        } else if (PriceBookConstants.VirtualFieldOption.ORGANIZATION.getValue().equals(virtualApplyOrgRange)) {
            List<String> empIdList = Lists.newArrayList();
            List<String> deptIdList = Lists.newArrayList();
            String virtualEmpName = objectData.get(PriceBookConstants.Field.VIRTUAL_EMPLOYEE_NAME.getApiName(), String.class);
            if (!checkAndConvertNames2Ids(virtualEmpName, empNameIdMap, empIdList, importData.getRowNo(),
                    "paas.udobj.member_not_find", errorList)) {
                return;
            }
            String virtualDeptName = objectData.get(PriceBookConstants.Field.VIRTUAL_DEPT_NAME.getApiName(), String.class);
            if (!checkAndConvertNames2Ids(virtualDeptName, deptNameIdMap, deptIdList, importData.getRowNo(),
                    "paas.udobj.department_not_find", errorList)) {
                return;
            }
            String empCondition = "";
            String deptCondition = "";
            if (CollectionUtils.notEmpty(empIdList)) {
                empCondition = "\"" + String.join("\",\"", empIdList) + "\"";
            }
            if (CollectionUtils.notEmpty(deptIdList)) {
                deptCondition = "\"" + String.join("\",\"", deptIdList) + "\"";
            }
            objectData.set(PriceBookConstants.Field.APPLY_ORG_RANGE.getApiName(), "{\"type\":\"ORG\", \"value\":{\"employees\":["
                    + empCondition + "],\"departments\":[" + deptCondition + "]}}");
        }
    }

    private static boolean checkAndConvertNames2Ids(String virtualFieldValue, Map<String, String> nameIdMap,
                                                    List<String> idList, int rowNo, String errorKey, List<BaseImportAction.ImportError> errorList) {
        List<String> nameList = splitAndTrim(virtualFieldValue);
        List<String> notExistName = Lists.newArrayList();
        for (String name : nameList) {
            if (nameIdMap.containsKey(name)) {
                idList.add(nameIdMap.get(name));
            } else {
                notExistName.add(name);
            }
        }
        if (CollectionUtils.notEmpty(notExistName)) {
            errorList.add(new BaseImportAction.ImportError(rowNo, I18N.text(errorKey, Joiner.on("','").join(notExistName))));
            return false;
        }
        return true;
    }

    private static List<String> splitAndTrim(String value) {
        List<String> nameList = Lists.newArrayList();
        if (!Strings.isNullOrEmpty(value)) {
            String[] nameArray = value
                    .replace("，", ",")
                    .replace("；", ";")
                    .split(",|;");
            for (String name : nameArray) {
                nameList.add(name.trim());
            }
        }
        return nameList;
    }

    private static void convertAccountRangeField(IObjectData objectData) {
        String virtualApplyOrgRange = objectData.get(PriceBookConstants.Field.VIRTUAL_APPLY_ACCOUNT_RANGE.getApiName(), String.class);
        if (PriceBookConstants.VirtualFieldOption.ALL.getValue().equals(virtualApplyOrgRange)) {
            objectData.set(PriceBookConstants.Field.APPLY_ACCOUNT_RANGE.getApiName(), "{\"type\":\"ALL\"}");
        } else if (PriceBookConstants.VirtualFieldOption.SPECIFIED.getValue().equals(virtualApplyOrgRange)) {
            objectData.set(PriceBookConstants.Field.APPLY_ACCOUNT_RANGE.getApiName(), "{\"type\":\"FIXED\"}");
        }
    }

    private static void convertPartnerRangeField(IObjectData objectData) {
        String virtualApplyOrgRange = objectData.get(PriceBookConstants.Field.VIRTUAL_APPLY_PARTNER_RANGE.getApiName(), String.class);
        if (PriceBookConstants.VirtualFieldOption.ALL.getValue().equals(virtualApplyOrgRange)) {
            objectData.set(PriceBookConstants.Field.APPLY_PARTNER_RANGE.getApiName(), "{\"type\":\"ALL\"}");
        } else if (PriceBookConstants.VirtualFieldOption.NONE.getValue().equals(virtualApplyOrgRange)) {
            objectData.set(PriceBookConstants.Field.APPLY_PARTNER_RANGE.getApiName(), "{\"type\":\"NONE\"}");
        }
    }

    public static void resetCalculateStatus(User user, List<IObjectData> objectDataList) {
        if (!GrayUtil.isPriceBookReform(user.getTenantId())) {
            return;
        }
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        Set<String> priceBookIds = Sets.newHashSet();
        objectDataList.forEach(r -> priceBookIds.add(r.get(PriceBookConstants.Field.PRICE_BOOK_ID.getApiName(), String.class)));
        availableRangeCoreService.resetCalculateStatus(user, SFAPreDefineObject.PriceBook.getApiName(), Lists.newArrayList(priceBookIds));
    }

    public static void validateAccount(String tenantId, List<BaseImportDataAction.ImportData> dataList,
                                       List<BaseImportAction.ImportError> errorList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        validateDuplicated(tenantId, dataList, errorList, SFAPreDefineObject.PriceBookAccount.getApiName(),
                PriceBookConstants.ACCOUNT_ID);
        String errorMsg = String.format(I18N.text("AvailableRangeObj.account_product_not_match_fix_option"),
                I18N.text("PriceBookObj.field.virtual_apply_account_range.label"),
                I18N.text("PriceBookObj.field.virtual_apply_account_range.option.specified"));
        validateFixedCondition(tenantId, dataList, errorList, PriceBookConstants.Field.APPLY_ACCOUNT_RANGE.getApiName(), errorMsg);
    }

    private static void validateFixedCondition(String tenantId, List<BaseImportDataAction.ImportData> dataList,
                                               List<BaseImportAction.ImportError> errorList, String rangeFieldName,
                                               String errorMsg) {
        Set<String> priceBookIds = getMasterIds(dataList);
        Map<String, String> fixedOption = getAllFixedOption(tenantId, priceBookIds, rangeFieldName);
        for (BaseImportDataAction.ImportData importData : dataList) {
            String priceBookId = importData.getData().get(PriceBookConstants.Field.PRICE_BOOK_ID.getApiName(), String.class);
            if (StringUtils.isNotEmpty(priceBookId) && !fixedOption.containsKey(priceBookId)) {
                continue;
            }
            if (Strings.isNullOrEmpty(priceBookId) ||
                    (fixedOption.containsKey(priceBookId) &&
                            UseRangeFieldDataRender.UseRangeType.FIXED.toString().equals(fixedOption.get(priceBookId)))) {
                continue;
            }
            errorList.add(new BaseImportAction.ImportError(importData.getRowNo(), errorMsg));
        }
    }

    private static void validateDuplicated(String tenantId, List<BaseImportDataAction.ImportData> dataList,
                                           List<BaseImportAction.ImportError> errorList, String detailDescribeName,
                                           String fieldName) {
        int capacity = dataList.size();
        Set<String> masterIds = Sets.newHashSetWithExpectedSize(capacity);
        Set<String> detailIds = Sets.newHashSetWithExpectedSize(capacity);
        Map<String, List<Integer>> idRowMap = Maps.newHashMap();
        for (BaseImportDataAction.ImportData importData : dataList) {
            String priceBookId = importData.getData().get(PriceBookConstants.Field.PRICE_BOOK_ID.getApiName(), String.class);
            if (!Strings.isNullOrEmpty(priceBookId)) {
                masterIds.add(priceBookId);
            }
            String detailId = importData.getData().get(fieldName, String.class);
            if (!Strings.isNullOrEmpty(detailId)) {
                detailIds.add(detailId);
                if (!Strings.isNullOrEmpty(priceBookId)) {
                    idRowMap.computeIfAbsent(priceBookId.concat(detailId), v -> Lists.newArrayList()).add(importData.getRowNo());
                }
            }
        }
        if (CollectionUtils.empty(idRowMap)) {
            return;
        }
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        List<IObjectData> existDetailList = getRelatedDetailByIds(user, masterIds, detailDescribeName, fieldName, detailIds);
        Set<String> existId = existDetailList.stream()
                .map(r -> r.get(PriceBookConstants.Field.PRICE_BOOK_ID.getApiName(), String.class).concat(r.get(fieldName, String.class)))
                .collect(Collectors.toSet());
        String existMsg = I18N.text("AvailableRangeObj.detail_data_exist");
        String duplicateMsg = I18N.text("paas.metadata.duplicate_data");
        for (Map.Entry<String, List<Integer>> entry : idRowMap.entrySet()) {
            if (existId.contains(entry.getKey())) {
                entry.getValue().forEach(r -> errorList.add(new BaseImportAction.ImportError(r, existMsg)));
            }
            if (entry.getValue().size() > 1) {
                entry.getValue().forEach(r -> errorList.add(new BaseImportAction.ImportError(r, duplicateMsg)));
            }
        }
    }

    private static Set<String> getMasterIds(List<BaseImportDataAction.ImportData> dataList) {
        Set<String> priceBookIds = Sets.newHashSet();
        for (BaseImportDataAction.ImportData importData : dataList) {
            String priceBookId = importData.getData().get(PriceBookConstants.Field.PRICE_BOOK_ID.getApiName(), String.class);
            if (!Strings.isNullOrEmpty(priceBookId)) {
                priceBookIds.add(priceBookId);
            }
        }
        return priceBookIds;
    }

    private static Map<String, String> getAllFixedOption(String tenantId, Set<String> priceBookIds, String fieldName) {
        Map<String, String> fixedOption = Maps.newHashMapWithExpectedSize(priceBookIds.size());
        if (CollectionUtils.empty(priceBookIds)) {
            return fixedOption;
        }
        List<IObjectData> rangeDataList = serviceFacade.findObjectDataByIdsIgnoreRelevantTeam(tenantId,
                Lists.newArrayList(priceBookIds), SFAPreDefineObject.PriceBook.getApiName());
        for (IObjectData objectData : rangeDataList) {
            String rangeValue = objectData.get(fieldName, String.class);
            if (Strings.isNullOrEmpty(rangeValue)) {
                continue;
            }
            UseRangeFieldDataRender.UseRangeInfo useRangeInfo = JSON.parseObject(rangeValue, UseRangeFieldDataRender.UseRangeInfo.class);
            fixedOption.put(objectData.getId(), useRangeInfo.getType());
        }
        return fixedOption;
    }

    private static List<IObjectData> getRelatedDetailByIds(User user, Set<String> masterIds, String detailDescribeName,
                                                           String detailFieldName, Set<String> detailIds) {
        if (CollectionUtils.empty(masterIds) || CollectionUtils.empty(detailIds)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery templateQuery = buildExistDetailQueryByIds(masterIds, detailFieldName, detailIds);
        return findDataBySearchQuery(user, detailDescribeName, templateQuery);
    }

    private static List<IObjectData> findDataBySearchQuery(User user, String describeName, SearchTemplateQuery searchTemplateQuery) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        int offset = 0;
        int loopCount = 0;
        int maxCount = 1000;
        while (loopCount < maxCount) {
            loopCount++;
            if (loopCount == maxCount) {
                log.warn("findDataBySearchQuery loop limit, limit:{}, tenantId:{}", maxCount, user.getTenantId());
                SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder()
                        .action("sfa_loop_limit")
                        .objectApiNames(describeName)
                        .message("PriceBookImportUtils.findDataBySearchQuery")
                        .build(), user);
                break;
            }
            searchTemplateQuery.setOffset(offset);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, describeName, searchTemplateQuery);
            if (null == queryResult || CollectionUtils.empty(queryResult.getData())) {
                break;
            }
            objectDataList.addAll(queryResult.getData());
            if (queryResult.getData().size() < BATCH_MAX_LIMIT) {
                break;
            }
            offset += BATCH_MAX_LIMIT;
        }
        return objectDataList;
    }

    private static SearchTemplateQuery buildExistDetailQueryByIds(Set<String> masterIds, String detailFieldName,
                                                                  Set<String> detailIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(BATCH_MAX_LIMIT);
        query.setOffset(0);
        query.setPermissionType(0);
        query.setNeedReturnQuote(Boolean.FALSE);
        query.setNeedReturnCountNum(Boolean.FALSE);
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName(PriceBookConstants.Field.PRICE_BOOK_ID.getApiName());
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList(masterIds));
        filters.add(filter);
        IFilter detailFilter = new Filter();
        detailFilter.setFieldName(detailFieldName);
        detailFilter.setOperator(Operator.IN);
        detailFilter.setFieldValues(Lists.newArrayList(detailIds));
        filters.add(detailFilter);
        query.setFilters(filters);
        return query;
    }

    public static void removeCurrencyFields(String tenantId, List<IFieldDescribe> headerFieldList) {
        if (CollectionUtils.empty(headerFieldList)) {
            return;
        }
        if (!configService.isCurrencyEnabled(tenantId)) {
            return;
        }
        headerFieldList.removeIf(f -> removeFields.contains(f.getApiName()));
    }

    public static void handleCurrency(String tenantId, List<IObjectData> validList) {
        if (!configService.isCurrencyEnabled(tenantId)) {
            return;
        }
        if (CollectionUtils.empty(validList)) return;
        Map<String, IObjectData> priceBookMap = serviceFacade
                .findObjectDataByIdsIgnoreAll(
                        tenantId,
                        validList.stream()
                                .map(obj -> obj.get("pricebook_id", String.class))
                                .filter(StringUtils::isNotBlank)
                                .distinct()
                                .collect(Collectors.toList()),
                        SFAPreDefineObject.PriceBook.getApiName())
                .stream()
                .collect(Collectors.toMap(DBRecord::getId, x -> x, (a, b) -> a));
        if (priceBookMap.isEmpty()) return;
        validList.forEach(obj -> {
            String priceBookId = obj.get("pricebook_id", String.class);
            IObjectData priceBook = StringUtils.isNotBlank(priceBookId) ? priceBookMap.get(priceBookId) : null;
            if (priceBook != null) {
                obj.set("mc_currency", priceBook.get("mc_currency"));
                obj.set("mc_exchange_rate", priceBook.get("mc_exchange_rate"));
            }
        });
    }
}
