package com.facishare.crm.sfa.predefine.service.proposal.service;

import com.facishare.crm.platform.AssertValidator;
import com.facishare.crm.platform.async.executor.AsyncBootstrap;
import com.facishare.crm.sfa.predefine.service.proposal.access.GeneratorCacheAccess;
import com.facishare.crm.sfa.predefine.service.proposal.dto.AiTaskDTO;
import com.facishare.crm.sfa.predefine.service.proposal.dto.ProposalDTO;
import com.facishare.crm.sfa.predefine.service.proposal.dto.TaskResult;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.*;
import com.facishare.crm.sfa.predefine.service.proposal.enums.AiTaskStatus;
import com.facishare.crm.sfa.predefine.service.proposal.llm.CaseOverviewLLMService;
import com.facishare.crm.sfa.predefine.service.proposal.llm.ProposalLLMService;
import com.facishare.crm.sfa.predefine.service.proposal.proxy.OneFlowProxy;
import com.facishare.crm.sfa.predefine.service.proposal.vo.CaseOverviewVO;
import com.facishare.crm.sfa.predefine.service.proposal.vo.ProposalVO;
import com.facishare.crm.sfa.utilities.proxy.model.OneFlowOfRemote;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.facishare.crm.sfa.predefine.service.proposal.access.CaseOverviewAccess.CASE_OVERVIEW_OBJ;
import static com.facishare.crm.sfa.predefine.service.proposal.access.ProposalAccess.PROPOSAL_OBJ;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_PARAMET_ERERROR;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-25
 * ============================================================
 */
@Service
@Slf4j
public class ProposalGenerateService {
    @Resource
    private CaseOverviewLLMService caseOverviewLLMService;
    @Resource
    private AssertValidator assertValidator;
    @Resource
    private GeneratorCacheAccess generatorCacheAccess;
    @Resource
    private CaseOverviewService caseOverviewService;
    @Resource
    private ProposalService proposalService;
    @Resource
    private ProposalLLMService proposalLLMService;
    @Resource
    private AsyncLLMService asyncLLMService;
    @Resource
    private OneFlowProxy oneFlowProxy;

    public String overviewLLM(User user, OverviewLLMRequest arg) {
        String relatedObjectDescribe = arg.getRelatedObjectDescribe();
        String relatedDataId = arg.getRelatedDataId();
        List<Object> promptAttachment = arg.getPromptAttachment();
        boolean byFile = promptAttachment != null;
        if (byFile && CollectionUtils.isEmpty(promptAttachment)) {
            throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
        }
        assertValidator.assertAllNotBlank(relatedObjectDescribe, relatedDataId);
        QuickCreateCaseOverviewRequest caseOverview = new QuickCreateCaseOverviewRequest();
        caseOverview.setRelatedObjectDescribe(relatedObjectDescribe);
        caseOverview.setRelatedDataId(relatedDataId);
        caseOverview.setPromptAttachment(promptAttachment);
        String taskId = caseOverviewService.quickCreate(user, caseOverview);
        generatorCacheAccess.llmTask(CASE_OVERVIEW_OBJ, taskId, AiTaskStatus.PENDING, 0);
        asyncLLMService.sendCaseOverviewMessage(user, taskId, byFile);
        AsyncBootstrap.runAsyncTask(() -> {
            generatorCacheAccess.llmTask(CASE_OVERVIEW_OBJ, taskId, AiTaskStatus.RUNNING, 0);
            long startTime = System.currentTimeMillis();
            String generate = caseOverviewLLMService.generate(user, relatedObjectDescribe, relatedDataId);
            if (StringUtils.isBlank(generate)) {
                long endTime = System.currentTimeMillis();
                generatorCacheAccess.llmTask(CASE_OVERVIEW_OBJ, taskId, AiTaskStatus.FAILED, endTime - startTime);
                return;
            }
            UpdateCaseOverviewRequest updateCaseOverviewRequest = new UpdateCaseOverviewRequest();
            updateCaseOverviewRequest.setContent(generate);
            updateCaseOverviewRequest.setId(taskId);
            caseOverviewService.update(user, updateCaseOverviewRequest);
            long endTime = System.currentTimeMillis();
            generatorCacheAccess.llmTask(CASE_OVERVIEW_OBJ, taskId, AiTaskStatus.SUCCEEDED, endTime - startTime);
        });
        return taskId;
    }

    public AiTaskDTO queryTask(String objectDescribeApiName, String dataId) {
        return generatorCacheAccess.queryTask(objectDescribeApiName, dataId);
    }

    public TaskResult queryTaskResult(User user, AiTaskDTO aiTask, String objectDescribeApiName, String dataId) {
        if (AiTaskStatus.valueOf(aiTask.getStatus()) != AiTaskStatus.SUCCEEDED) {
            return null;
        }
        TaskResult taskResult = TaskResult.builder().duration(aiTask.getDuration()).build();
        if (CASE_OVERVIEW_OBJ.equals(objectDescribeApiName)) {
            CaseOverviewVO caseOverviewVO = caseOverviewService.queryCaseOverview(user, dataId);
            assertValidator.assertNotNull(caseOverviewVO);
            taskResult.setText(caseOverviewVO.getContent());
        } else if (PROPOSAL_OBJ.equals(objectDescribeApiName)) {
            ProposalVO proposal = proposalService.queryProposal(user, dataId);
            assertValidator.assertNotNull(proposal);
            taskResult.setText(proposal.getContent());
        } else {
            throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
        }
        return taskResult;
    }

    public String proposalLLM(User user, ProposalLLMRequest arg) {
        assertValidator.assertAllNotBlank(arg.getCaseOverviewId(), arg.getRelatedObjectDescribe(), arg.getRelatedDataId());
        ProposalDTO proposalDTO = new ProposalDTO();
        proposalDTO.setRelatedDataId(arg.getRelatedDataId());
        proposalDTO.setRelatedObjectDescribe(arg.getRelatedObjectDescribe());
        proposalDTO.setCaseOverviewId(arg.getCaseOverviewId());
        String taskId = proposalService.quickCreate(user, proposalDTO);
        generatorCacheAccess.llmTask(PROPOSAL_OBJ, taskId, AiTaskStatus.PENDING, 0);
//        asyncLLMService.sendProposalMessage(user, taskId, arg.getCustomPrompt());
        AsyncBootstrap.runAsyncTask(() -> {
            generatorCacheAccess.llmTask(PROPOSAL_OBJ, taskId, AiTaskStatus.RUNNING, 0);
            long startTime = System.currentTimeMillis();
            String generate = proposalLLMService.generate(user, arg.getCaseOverviewId(), arg.getCustomPrompt());
            if (StringUtils.isBlank(generate)) {
                long endTime = System.currentTimeMillis();
                generatorCacheAccess.llmTask(PROPOSAL_OBJ, taskId, AiTaskStatus.FAILED, endTime - startTime);
                return;
            }
            UpdateProposalRequest updateArg = new UpdateProposalRequest();
            updateArg.setContent(generate);
            updateArg.setId(taskId);
            proposalService.updateProposalContent(user, updateArg);
            long endTime = System.currentTimeMillis();
            generatorCacheAccess.llmTask(PROPOSAL_OBJ, taskId, AiTaskStatus.SUCCEEDED, endTime - startTime);
        });
        return taskId;
    }

    public Map<String, OneFlowOfRemote.OutputResult> triggerFlow(User user, TriggerFlowRequest arg) {
        assertValidator.assertAllNotBlank(arg.getFlowId(), arg.getRelatedObjectDescribe(), arg.getRelatedDataId());
        Map<String, Object> inputs = arg.getInputs();
        if (inputs == null) {
            inputs = new HashMap<>();
        }
        inputs.put("custom_variable##dataId", arg.getRelatedDataId());
        OneFlowOfRemote.TriggerResult triggerResult = oneFlowProxy.trigger(user, arg.getFlowId(), inputs);
        if (triggerResult.getCurrentTask() == null) {
            return Maps.newHashMap();
        }
        return triggerResult.getCurrentTask().getOutputs();
    }
}
