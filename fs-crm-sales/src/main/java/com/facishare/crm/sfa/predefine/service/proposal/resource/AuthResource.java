package com.facishare.crm.sfa.predefine.service.proposal.resource;

import com.facishare.crm.sfa.prm.platform.model.ApiResponse;
import com.facishare.crm.sfa.predefine.service.proposal.dto.RequestId;
import com.facishare.crm.sfa.predefine.service.proposal.proxy.AiPPTProxy;
import com.facishare.crm.sfa.predefine.service.proposal.service.AiPPTAuthService;
import com.facishare.crm.sfa.predefine.service.proposal.vo.AiPPTAuthInfo;
import com.facishare.crm.sfa.predefine.service.proposal.vo.CaseOverviewVO;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-19
 * ============================================================
 */
@ServiceModule("ai-ppt-auth")
@Component
@Slf4j
public class AuthResource {
    @Resource
    private AiPPTAuthService aiPPTAuthService;

    @ServiceMethod("generate")
    public ApiResponse<AiPPTAuthInfo> generate(ServiceContext serviceContext) {
        AiPPTAuthInfo authInfo = aiPPTAuthService.generate(serviceContext.getUser());
        return ApiResponse.success(authInfo);
    }

    @ServiceMethod("getOrCache")
    public ApiResponse<AiPPTAuthInfo> query(ServiceContext serviceContext) {
        AiPPTAuthInfo authInfo = aiPPTAuthService.getOrCache(serviceContext.getUser());
        return ApiResponse.success(authInfo);
    }
    @ServiceMethod("delete")
    public ApiResponse<Boolean> delete(ServiceContext serviceContext, String key) {
        aiPPTAuthService.delete(serviceContext.getUser(), key);
        return ApiResponse.success(true);
    }
}
