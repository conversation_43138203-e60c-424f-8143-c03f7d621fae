package com.facishare.crm.sfa.predefine.service.proposal.service;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.platform.AssertValidator;
import com.facishare.crm.platform.converter.Converter;
import com.facishare.crm.sfa.predefine.service.proposal.access.ObjectDataAccess;
import com.facishare.crm.sfa.predefine.service.proposal.access.ProposalAccess;
import com.facishare.crm.sfa.predefine.service.proposal.dto.CaseOverviewDTO;
import com.facishare.crm.sfa.predefine.service.proposal.dto.ProposalDTO;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.RenameRequest;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.UpdatePPTIdRequest;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.UpdateProposalRequest;
import com.facishare.crm.sfa.predefine.service.proposal.llm.ProposalLLMService;
import com.facishare.crm.sfa.predefine.service.proposal.vo.ProposalListVO;
import com.facishare.crm.sfa.predefine.service.proposal.vo.ProposalVO;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_PARAM_ERROR;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Service
@Slf4j
public class ProposalService {
    @Resource
    private ProposalAccess proposalAccess;
    @Resource(name = "objectConverter")
    private Converter converter;
    @Resource
    private CaseOverviewService caseOverviewService;
    @Resource
    private ObjectDataAccess objectDataAccess;
    @Resource
    private AssertValidator assertValidator;
    @Resource
    private ProposalLLMService proposalLLMService;

    public List<ProposalListVO> list(User user, String objectApiName, String id) {
        assertValidator.assertAllNotBlank(objectApiName, id);
        List<IObjectData> dataList = proposalAccess.limitList(user, objectApiName, id);
        return converter.convertInPutDataList(dataList, ProposalListVO.class);
    }

    public boolean rename(User user, RenameRequest renameRequest) {
        assertValidator.assertAllNotBlank(renameRequest.getId());
        IObjectData data = proposalAccess.queryById(user, renameRequest.getId());
        assertValidator.assertNotNull(data);
        if (renameRequest.getName().equals(data.getName())) {
            return true;
        }
        proposalAccess.updateByFields(user, data, Sets.newHashSet(ObjectDataExt.NAME));
        return true;
    }

    public boolean deleteProposal(User user, String id) {
        assertValidator.assertAllNotBlank(id);
        proposalAccess.delete(user, id);
        return true;
    }

    public ProposalVO queryProposal(User user, String id) {
        assertValidator.assertAllNotBlank(id);
        IObjectData objectData = proposalAccess.queryById(user, id);
        assertValidator.assertNotNull(objectData);
        return converter.convertDTO(objectData, ProposalVO.class);
    }

    public boolean updateProposalContent(User user, UpdateProposalRequest updateArg) {
        assertValidator.assertAllNotBlank(updateArg.getId());
        IObjectData objectData = proposalAccess.queryById(user, updateArg.getId());
        assertValidator.assertNotNull(objectData);
        objectData.set("content", updateArg.getContent());
        proposalAccess.updateByFields(user, objectData, Sets.newHashSet("content"));
        return true;
    }

    public boolean setPPT(User user, UpdatePPTIdRequest updateArg) {
        assertValidator.assertAllNotBlank(updateArg.getId());
        IObjectData objectData = proposalAccess.queryById(user, updateArg.getId());
        assertValidator.assertNotNull(objectData);
        objectData.set("ptt_id", updateArg.getPptId());
        proposalAccess.updateByFields(user, objectData, Sets.newHashSet("ptt_id"));
        return true;
    }

    public String quickCreate(User user, ProposalDTO proposalDTO) {
        assertValidator.assertAllNotBlank(proposalDTO.getRelatedDataId(), proposalDTO.getRelatedObjectDescribe(), proposalDTO.getCaseOverviewId());
        IObjectData caseOverviewData = caseOverviewService.queryCaseOverviewRelatedInfo(user, proposalDTO.getCaseOverviewId());
        assertValidator.assertNotNull(caseOverviewData);
        CaseOverviewDTO caseOverviewDTO = converter.convertDTO(caseOverviewData, CaseOverviewDTO.class);
        if (!caseOverviewDTO.getRelatedDataId().equals(proposalDTO.getRelatedDataId()) || !caseOverviewDTO.getRelatedObjectDescribe().equals(proposalDTO.getRelatedObjectDescribe())) {
            throw new ValidateException(SFA_PARAM_ERROR);
        }
        String relatedObjectDescribe = proposalDTO.getRelatedObjectDescribe();
        switch (relatedObjectDescribe) {
            case "AccountObj":
                proposalDTO.setAccountId(proposalDTO.getRelatedDataId());
                break;
            case "LeadObj":
                proposalDTO.setLeadsId(proposalDTO.getRelatedDataId());
                break;
            case "NewOpportunityObj":
                proposalDTO.setNewOpportunityId(proposalDTO.getRelatedDataId());
                break;
            default:
                //ignore
        }
        IObjectData data = converter.convertObjectData(proposalDTO);
        String owner = objectDataAccess.getOwner(user);
        assertValidator.assertAllNotBlank(owner);
        data.setOwner(Lists.newArrayList(owner));
        String name = proposalLLMService.generateName(user, caseOverviewDTO.getContent());
        data.setName(name);
        return proposalAccess.create(user, data).getId();
    }
}
