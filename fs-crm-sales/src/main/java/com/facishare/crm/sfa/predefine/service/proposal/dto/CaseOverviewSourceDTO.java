package com.facishare.crm.sfa.predefine.service.proposal.dto;

import com.facishare.crm.platform.annotation.Convertible;
import com.facishare.crm.platform.annotation.Diff;
import com.facishare.crm.platform.annotation.Mappable;
import lombok.Data;

import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Data
@Convertible
@Diff
public class CaseOverviewSourceDTO {
    @Mappable(fieldApiName = "_id")
    private String id;
    private String relatedObjectDescribe;
    private List<String> relatedFields;
}
