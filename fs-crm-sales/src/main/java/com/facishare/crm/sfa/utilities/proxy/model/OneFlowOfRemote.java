package com.facishare.crm.sfa.utilities.proxy.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-30
 * ============================================================
 */
public interface OneFlowOfRemote {
    @Data
    class TriggerSource {
        String businessId;
        String businessType;
    }

    @Data
    class Trigger {
        private String sourceWorkflowId;
        private Map<String, Object> inputs;
        private TriggerSource source;
    }

    @Data
    class TriggerResult {
        String workflowInstanceId;
        OneFlowOfRemote.FlowOneTaskBase currentTask;
    }

    /**
     * One flow 任务基本信息
     */
    @Data
    class FlowOneTaskBase {
        // 流程id
        private String workflowId;
        // 流程名称
        private String workflowName;
        // 流程apiName
        private String sourceWorkflowId;
        // 流程实例id
        private String workflowInstanceId;
        private Map<String, OutputResult> outputs;
    }

    @Data
    class OutputResult {
        private String id;
        private String label;
        private String objectApiName;
        private String value;
        private String type;
    }


    @Data
    class TriggerResponse {
        private String code;
        private String message;
        private TriggerResult data;
    }

    @Data
    class ListArg {
        private String name;
        private Boolean enable;
        private String subType;
        // 页数 从 1 开始 5
        private Integer pageNumber;
        // 每页大小 最大100，如果不传默认20
        private Integer pageSize;
    }

    @Data
    class FlowListResponse {
        private String code;
        private String message;
        private ListResult data;
    }

    @Data
    class ListResult {
        List<Flow> data;
    }

    @Data
    class Flow {
        private String name;
        private String nameI18N;
        private String sourceWorkflowId;
        private String flowId;
        private Map<String, String> nameTranslateInfo;
        private Map<String, String> descTranslateInfo;
        private List<FlowNode> outputs;
    }

    @Data
    class FlowNode {
        private String id;
        private String label;
        private String type;
        private String objectApiName;
    }
}
