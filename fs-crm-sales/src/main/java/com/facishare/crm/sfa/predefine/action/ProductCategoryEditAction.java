package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.service.ProductCategoryBizService;
import com.facishare.crm.sfa.predefine.service.objectbo.ProductCategoryBO;
import com.facishare.crm.sfa.predefine.service.resource.ProductCategoryServiceResource;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel;
import com.facishare.crm.sfa.utilities.util.ProductCategoryUtils;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryV2Validator;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject.ORDER_FIELD;

/**
 * <AUTHOR>
 * @date 2021/12/1 16:56
 */
@Slf4j
public class ProductCategoryEditAction extends StandardEditAction {
    private final ProductCategoryV2Validator productCategoryV2Validator = SpringUtil.getContext().getBean(ProductCategoryV2Validator.class);
    private final ProductCategoryBizService productCategoryBizService = SpringUtil.getContext().getBean(ProductCategoryBizService.class);
    private final ProductCategoryServiceResource productCategoryServiceResource = SpringUtil.getContext().getBean(ProductCategoryServiceResource.class);
    private final ProductCategoryUtils productCategoryUtils = SpringUtil.getContext().getBean(ProductCategoryUtils.class);
    private boolean isNeedChangePath = false;
    private boolean isNeedSort = false;
    private boolean isNeedSyncDescribe = false;

    @Override
    protected void before(Arg arg) {
        productCategoryBizService.fillOldCategoryMustField(arg.getObjectData());
        super.before(arg);
        IObjectData oldData = dataList.get(0);
        IObjectData newData = arg.getObjectData().toObjectData();
        isNeedChangePath = checkNeedChangePath(oldData, newData);
        isNeedSort = checkNeedSort(oldData, newData);
        isNeedSyncDescribe = checkNeedSyncDescribe(oldData, newData);
        if (productCategoryUtils.isCloseOldProductCategory(actionContext.getTenantId())) {
            editCategoryValidate(oldData, newData);
        } else {
            Object orderField = newData.get(ORDER_FIELD);
            if (orderField == null) {
                newData.set(ORDER_FIELD, oldData.get(ORDER_FIELD));
            }
            List<IObjectData> allCategory = productCategoryUtils.findAllCategory(actionContext.getUser()).getData();
            List<IObjectData> categoryExceptSelf = allCategory.stream().filter(a -> !a.getId().equals(arg.getObjectData().getId())).collect(Collectors.toList());
            productCategoryServiceResource.savaCategoryCheckAsync(actionContext.getUser(), "Edit", arg.getObjectData(), categoryExceptSelf);
        }
    }

    private boolean checkNeedSyncDescribe(IObjectData oldData, IObjectData newData) {
        String oldName = ObjectDataUtils.getValueOrDefault(oldData, "name", "");
        String newName = ObjectDataUtils.getValueOrDefault(newData, "name", "");
        if (!oldName.equals(newName)) {
            return true;
        }
        String oldOrderField = ObjectDataUtils.getValueOrDefault(oldData, ProductCategoryModel.Filed.ORDER_FIELD, "");
        String newOrderField = ObjectDataUtils.getValueOrDefault(newData, ProductCategoryModel.Filed.ORDER_FIELD, "");
        if (!oldOrderField.equals(newOrderField)) {
            return true;
        }
        String oldPid = ObjectDataUtils.getValueOrDefault(oldData, ProductCategoryModel.Filed.PID, "");
        String newPid = ObjectDataUtils.getValueOrDefault(newData, ProductCategoryModel.Filed.PID, "");
        return !oldPid.equals(newPid);
    }

    private boolean checkNeedSort(IObjectData oldData, IObjectData newData) {
        if (!RequestUtil.isCepRequest()) {
            return false;
        }
        String newOrderField = ObjectDataUtils.getValueOrDefault(newData, ProductCategoryModel.Filed.ORDER_FIELD, "");
        String oldOrderField = ObjectDataUtils.getValueOrDefault(oldData, ProductCategoryModel.Filed.ORDER_FIELD, "");
        if (StringUtils.isBlank(newOrderField)) {
            return false;
        }
        return !oldOrderField.equals(newOrderField);
    }

    private boolean checkNeedChangePath(IObjectData oldData, IObjectData newData) {
        if (!productCategoryUtils.isCloseOldProductCategory(actionContext.getTenantId())) {
            return false;
        }
        String newPid = ObjectDataUtils.getValueOrDefault(newData, ProductCategoryModel.Filed.PID, "");
        String oldPid = ObjectDataUtils.getValueOrDefault(oldData, ProductCategoryModel.Filed.PID, "");
        return !oldPid.equals(newPid);
    }

    private void editCategoryValidate(IObjectData oldData, IObjectData newData) {
        User user = actionContext.getUser();
        String oldPid = ProductCategoryBO.of(oldData).getPid();
        String newPid = ProductCategoryBO.of(newData).getPid();
        productCategoryV2Validator.checkCategoryIsLoop(user, newData.getId(), newPid);
        String categoryCode = ObjectDataUtils.getValueOrDefault(newData, ProductCategoryModel.Filed.CATEGORY_CODE, "");
        String code = ObjectDataUtils.getValueOrDefault(newData, ProductCategoryModel.Filed.CODE, "");
        String categoryName = newData.getName();
        String id = newData.getId();
        CompletableFuture<Void> f1 = CompletableFuture.runAsync(() -> productCategoryV2Validator.checkCategoryDepth(user, newPid));
        CompletableFuture<Void> f2 = CompletableFuture.runAsync(() -> productCategoryV2Validator.checkPartnerCategoryId(user, newPid));
        CompletableFuture<Void> f3 = CompletableFuture.runAsync(() -> productCategoryV2Validator.checkNameUniqueInSameLevel(user, categoryName, newPid, id));
        CompletableFuture<Void> f4 = CompletableFuture.runAsync(() -> productCategoryV2Validator.checkCategoryCodeUnique(user, categoryCode, id));
        CompletableFuture<Void> f5 = CompletableFuture.runAsync(() -> productCategoryV2Validator.checkCodeChange(user, code, id));
        CompletableFuture<Void> f6 = CompletableFuture.runAsync(() -> productCategoryV2Validator.checkCategoryParam(user, newData));
        CompletableFuture<Void> f7 = CompletableFuture.runAsync(() -> productCategoryV2Validator.checkCategoryPathChanged(oldData, newData));
        if (oldPid.equals(newPid)) {
            productCategoryV2Validator.doCategoryParamCheckByFuture(actionContext.getUser(), f1, f2, f3, f4, f5, f6, f7);
        } else {
            CompletableFuture<Void> f8 = CompletableFuture.runAsync(() -> productCategoryV2Validator.checkCategoryIsRelated(user, newPid, objectDescribe));
            productCategoryV2Validator.doCategoryParamCheckByFuture(actionContext.getUser(), f1, f2, f3, f4, f5, f6, f7, f8);
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        Result result = super.doAct(arg);
        if (isNeedChangePath) {
            String pid = ObjectDataUtils.getValueOrDefault(arg.getObjectData().toObjectData(), ProductCategoryModel.Filed.PID, "");
            String id = objectData.getId();
            productCategoryBizService.changePathByParentId(actionContext.getUser(), id, pid);
        }
        if (isNeedSort) {
            String orderField = ObjectDataUtils.getValueOrDefault(objectData, ProductCategoryModel.Filed.ORDER_FIELD, "1");
            String pid = ObjectDataUtils.getValueOrDefault(objectData, ProductCategoryModel.Filed.PID, "");
            productCategoryBizService.bulkUpdateOrderField(actionContext.getUser(), orderField, pid, objectData.getId());
        }
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (isNeedSyncDescribe) {
            productCategoryBizService.sendSynchronizeDescribeMqByGray(actionContext.getUser());
        }
        return newResult;
    }
}
