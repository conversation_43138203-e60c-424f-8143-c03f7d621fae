package com.facishare.crm.sfa.predefine.service.proposal.service;

import com.facishare.crm.platform.signature.SignatureGenerator;
import com.facishare.crm.sfa.predefine.service.proposal.access.GeneratorCacheAccess;
import com.facishare.crm.sfa.predefine.service.proposal.config.AiPPTConfig;
import com.facishare.crm.sfa.predefine.service.proposal.dto.GrantCodeData;
import com.facishare.crm.sfa.predefine.service.proposal.proxy.AiPPTProxy;
import com.facishare.crm.sfa.predefine.service.proposal.vo.AiPPTAuthInfo;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.time.Instant;
import java.util.concurrent.TimeUnit;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_SYS_ERROR_MSG;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-19
 * ============================================================
 */
@Service
@Slf4j
public class AiPPTAuthService {
    @Resource
    private AiPPTConfig aiPPTConfig;
    @Resource
    private RedissonClient redissonClient;

    private static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";


    @Resource
    private SignatureGenerator signatureGenerator;
    @Resource
    private AiPPTProxy aiPPTProxy;
    @Resource
    private GeneratorCacheAccess generatorCacheAccess;


    public AiPPTAuthInfo generate(User user) {
        String token = resolveGrantCode(user, true);
        AiPPTAuthInfo aiPPTAuthInfo = new AiPPTAuthInfo();
        aiPPTAuthInfo.setToken(token);
        aiPPTAuthInfo.setApiKey(aiPPTConfig.getApiKey());
        aiPPTAuthInfo.setChannel(AiPPTProxy.CHANNEL);
        return aiPPTAuthInfo;
    }

    private GrantCodeData createGrantCodeData(User user) {
        if (StringUtils.isAnyBlank(aiPPTConfig.getApiKey(), aiPPTConfig.getSecretKey())) {
            throw new ValidateException(I18N.text(SFA_SYS_ERROR_MSG));
        }
        long epochSecond = Instant.now().getEpochSecond();
        String signMessage = createSignMessage(epochSecond);
        String signature = signatureGenerator
                .algorithm(HMAC_SHA1_ALGORITHM)
                .secret(aiPPTConfig.getSecretKey())
                .message(signMessage)
                .sign();
        GrantCodeData grantCodeData = aiPPTProxy.grant(user, signature, aiPPTConfig.getApiKey(), epochSecond);
        if (grantCodeData == null) {
            throw new ValidateException(SFA_SYS_ERROR_MSG);
        }
        return grantCodeData;
    }

    private String createSignMessage(long epochSecond) {
        String data = "GET@/api/grant/code/@%s";
        return String.format(data, epochSecond);
    }

    public AiPPTAuthInfo getOrCache(User user) {
        AiPPTAuthInfo authInfo = new AiPPTAuthInfo();
        authInfo.setChannel(AiPPTProxy.CHANNEL);
        authInfo.setApiKey(aiPPTConfig.getApiKey());
        String cacheCode = generatorCacheAccess.getCacheCode(user);
        if (StringUtils.isBlank(cacheCode)) {
            cacheCode = resolveGrantCode(user);
        }
        authInfo.setToken(cacheCode);
        return authInfo;
    }

    private String resolveGrantCode(User user, boolean refreshCache) {
        RLock lock = redissonClient.getLock("proposal_lock:" + getLockKey(user));
        lock.lock(15, TimeUnit.SECONDS);
        try {
            if (refreshCache) {
                GrantCodeData grantCodeData = createGrantCodeData(user);
                return generatorCacheAccess.cacheCode(user, grantCodeData.getCode(), Math.toIntExact(grantCodeData.getTimeExpire()));
            } else {
                String cacheCode = generatorCacheAccess.getCacheCode(user);
                if (StringUtils.isNotBlank(cacheCode)) {
                    return cacheCode;
                } else {
                    GrantCodeData grantCodeData = createGrantCodeData(user);
                    return generatorCacheAccess.cacheCode(user, grantCodeData.getCode(), Math.toIntExact(grantCodeData.getTimeExpire()));
                }
            }
        } finally {
            lock.unlock();
        }
    }


    private String resolveGrantCode(User user) {
        return resolveGrantCode(user, false);
    }

    public void delete(User user, String key) {
        generatorCacheAccess.cleanCacheCode(user, key);
    }

    private String getLockKey(User user) {
        if (user.isOutUser()) {
            return user.getTenantId() + ":" + user.getOutUserId();
        } else {
            return user.getTenantId() + ":" + user.getUpstreamOwnerIdOrUserId();
        }
    }
}
