package com.facishare.crm.sfa.predefine.service.proposal.resource;

import com.facishare.crm.sfa.prm.platform.model.ApiResponse;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.FieldRequest;
import com.facishare.crm.sfa.predefine.service.proposal.service.CaseOverviewSourceService;
import com.facishare.crm.sfa.predefine.service.proposal.dto.CaseOverviewSourceDTO;
import com.facishare.crm.sfa.predefine.service.proposal.dto.RequestId;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.UpdateCaseSourceRequest;
import com.facishare.crm.sfa.predefine.service.proposal.vo.*;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@ServiceModule("case-source")
@Component
@Slf4j
public class CaseOverviewSourceResource {
    @Resource
    private CaseOverviewSourceService caseOverviewSourceService;

    @ServiceMethod("create")
    public ApiResponse<CaseOverviewSourceVO> create(ServiceContext serviceContext, CaseOverviewSourceDTO caseOverviewSource) {
        CaseOverviewSourceVO caseOverviewSourceVO = caseOverviewSourceService.createCaseOverviewSource(serviceContext.getUser(), caseOverviewSource);
        return ApiResponse.success(caseOverviewSourceVO);
    }

    @ServiceMethod("delete")
    public ApiResponse<OperationResultVO> delete(ServiceContext serviceContext, RequestId requestId) {
        boolean deleted = caseOverviewSourceService.deleteCaseOverviewSource(serviceContext.getUser(), requestId.getId());
        OperationResultVO operationResultVo = OperationResultVO
                .builder()
                .id(requestId.getId())
                .success(deleted)
                .build();
        return ApiResponse.success(operationResultVo);
    }

    @ServiceMethod("update")
    public ApiResponse<OperationResultVO> update(ServiceContext serviceContext, UpdateCaseSourceRequest updateCaseSourceRequest) {
        boolean updated = caseOverviewSourceService.updateCaseOverviewSource(serviceContext.getUser(), updateCaseSourceRequest);
        OperationResultVO operationResultVo = OperationResultVO
                .builder()
                .id(updateCaseSourceRequest.getId())
                .success(updated)
                .build();
        return ApiResponse.success(operationResultVo);
    }

    @ServiceMethod("query")
    public ApiResponse<CaseOverviewSourceVO> query(ServiceContext serviceContext, RequestId requestId) {
        CaseOverviewSourceVO caseOverviewSourceVO = caseOverviewSourceService.queryCaseOverviewSource(serviceContext.getUser(), requestId.getId());
        return ApiResponse.success(caseOverviewSourceVO);
    }

    @ServiceMethod("list")
    public ApiResponse<List<CaseOverviewSourceVO>> list(ServiceContext serviceContext) {
        List<CaseOverviewSourceVO> caseOverviewSources = caseOverviewSourceService.queryCaseOverviewSources(serviceContext.getUser());
        return ApiResponse.success(caseOverviewSources);
    }

    @ServiceMethod("objects")
    public ApiResponse<List<AbleObject>> objects(ServiceContext serviceContext) {
        List<AbleObject> caseOverviewSources = caseOverviewSourceService.queryAbleObjects(serviceContext);
        return ApiResponse.success(caseOverviewSources);
    }

    @ServiceMethod("fields")
    public ApiResponse<List<AbleField>> fields(ServiceContext serviceContext, FieldRequest fieldRequest) {
        List<AbleField> caseOverviewSources = caseOverviewSourceService.queryAbleFields(serviceContext.getUser(), fieldRequest.getObjectDescribeApiName());
        return ApiResponse.success(caseOverviewSources);
    }
}
