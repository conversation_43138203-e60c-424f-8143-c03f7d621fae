package com.facishare.crm.sfa.predefine.service.proposal.config;

import com.github.autoconf.ConfigFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-19
 * ============================================================
 */
@Service
public class AiPPTConfig {
    private String apiKey;
    private String secretKey;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-crm-sales-config", config -> {
            apiKey = config.getDecrypt("AiPPTApiKey");
            secretKey = config.getDecrypt("AiPPTSecretKey");
        });
    }

    public String getApiKey() {
        return apiKey;
    }

    public String getSecretKey() {
        return secretKey;
    }
}
