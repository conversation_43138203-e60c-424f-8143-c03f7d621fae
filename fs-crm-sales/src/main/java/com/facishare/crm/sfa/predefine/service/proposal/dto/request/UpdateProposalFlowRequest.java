package com.facishare.crm.sfa.predefine.service.proposal.dto.request;

import lombok.Data;

import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Data
public class UpdateProposalFlowRequest {
    private String id;
    private String name;
    private String flowId;
    private String describe;
    private List<String> outputNodes;
}
