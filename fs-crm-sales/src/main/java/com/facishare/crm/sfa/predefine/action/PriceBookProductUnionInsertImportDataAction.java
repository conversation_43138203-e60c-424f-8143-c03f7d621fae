package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.PriceBookImportUtils;
import com.facishare.crm.sfa.utilities.util.PriceBookUtil;
import com.facishare.crm.sfa.utilities.validator.PriceBookImportValidator;
import com.facishare.crm.sfa.utilities.validator.PriceBookValidator;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportDataAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;

public class PriceBookProductUnionInsertImportDataAction extends StandardUnionInsertImportDataAction {
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        List<ImportError> errorList = Lists.newArrayList();
        PriceBookImportValidator.pricebookSpecialDeal(actionContext.getUser(), dataList, errorList);
        if (PriceBookUtil.isAnyPricingFeatureEnabled(actionContext.getTenantId())) {
            PriceBookValidator.validatePriceBookProductImportRepeat(errorList, dataList, objectDescribe, actionContext);
        } else {
            PriceBookImportValidator.validateDuplicated(actionContext.getUser(), errorList, dataList);
        }
        PriceBookImportValidator.validatePriceRange(errorList, dataList);
        if (bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(actionContext.getTenantId())) {
            PriceBookImportValidator.validePriceBookProductUnit(actionContext.getUser(), errorList, dataList);
        }
        mergeErrorList(errorList);
    }

    @Override
    protected void customDefaultValue(List<IObjectData> validList) {
        PriceBookImportUtils.handleCurrency(actionContext.getTenantId(), validList);
        super.customDefaultValue(validList);
        validList.forEach(objectData -> objectData.set(ObjectDataExt.RECORD_TYPE, ObjectDataExt.RECORD_TYPE_DEFAULT));
    }
}