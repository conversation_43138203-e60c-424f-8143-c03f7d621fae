package com.facishare.crm.sfa.predefine.service.proposal.dto.request;

import lombok.Data;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Data
public class ApiCallLogRequest {
    private String requestId;
    private String platform;
    private String apiEndpoint;
    private String method;
    private Long requestTime;
    private Long responseTime;
    private Integer statusCode;
    private Boolean success;
    private Object requestParams;
    private Object responseBody;
    private Boolean hasArtifact;
    private String artifactType;
    private String artifactPath;
    private String sourceType;
    private String sourceId;
    private String responseMessage;
    private String bizSource;
}
