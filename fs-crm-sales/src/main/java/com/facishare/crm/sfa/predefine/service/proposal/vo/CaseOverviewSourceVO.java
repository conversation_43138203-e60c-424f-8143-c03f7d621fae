package com.facishare.crm.sfa.predefine.service.proposal.vo;

import com.facishare.crm.platform.annotation.Convertible;
import com.facishare.crm.platform.annotation.Mappable;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Data
@Convertible(allField = false)
@JsonInclude(JsonInclude.Include.ALWAYS)
public class CaseOverviewSourceVO {
    @Mappable(fieldApiName = "_id")
    private String id;
    @Mappable
    private String relatedObjectDescribe;
    private String relatedObjectDescribeName;
    private List<FieldVO> relatedFields;
}
