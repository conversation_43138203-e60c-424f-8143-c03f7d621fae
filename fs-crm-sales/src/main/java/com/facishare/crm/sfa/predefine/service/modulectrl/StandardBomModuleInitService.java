package com.facishare.crm.sfa.predefine.service.modulectrl;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @IgnoreI18nFile
 */
@Component
@Slf4j
public class StandardBomModuleInitService extends BomAbstractModuleInitService {

    private static final String FIELD_CONSTRAINT_CORE_ID = "{\"default_is_expression\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"is_required\":true,\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"life_status\",\"field_values\":[\"normal\"]}]}],\"define_type\":\"package\",\"is_extend\":false,\"is_single\":false,\"is_index\":true,\"is_active\":true,\"label\":\"产品组合\",\"target_api_name\":\"BomCoreObj\",\"target_related_list_name\":\"bom_core_attribute_constraint_list\",\"action_on_target_delete\":\"cascade_delete\",\"is_abstract\":null,\"field_num\":null,\"lookup_roles\":[],\"target_related_list_label\":\"产品属性约束\",\"is_need_convert\":false,\"api_name\":\"core_id\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"}";


    @Override
    public String getModuleCode() {
        return IModuleInitService.MODULE_CPQ;
    }

    @Override
    protected void initRelated(User user) {
        enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList("BOMObj"), user.getTenantId());
        initBomAttributeConstraintRelated(user.getTenantId());
        List<String> actionCodes = Lists.newArrayList();
        addFunc(user, actionCodes);
    }

    @Override
    protected void before(User user) {
        if (SFAConfigUtil.isSimpleBom(user.getTenantId())) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_FIXED_COMBINATION_PROMOTION_OPEN_UN_ALLOW_OPEN_BOM_WARN));
        }

        if(!bizConfigThreadLocalCacheService.isPriceBookEnabled(user.getTenantId())){
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_CPQ_CONFIG_PRICE_BOOK_WARN));
        }

        if(bizConfigThreadLocalCacheService.isMultiUnitEnabled(user.getTenantId())){
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_OPEN_MULTI_UNIT_NOT_OPEN_CPQ));
        }
    }

    private void initBomAttributeConstraintRelated(String tenantId) {
        enterpriseInitService.initDescribeForTenant(tenantId, getDescribeFromLocalResource(SFAPreDefineObject.BomAttributeConstraint.getApiName(),tenantId));
        enterpriseInitService.initDescribeForTenant(tenantId, SFAPreDefineObject.BomAttributeConstraintLines.getApiName());
        enterpriseInitService.initPrivilegeRelate(Lists.newArrayList(SFAPreDefineObject.BomAttributeConstraint.getApiName(), SFAPreDefineObject.BomAttributeConstraintLines.getApiName()), new User(tenantId, "-10000"), Lists.newArrayList("recordType"), null, null);
        enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(SFAPreDefineObject.BomAttributeConstraint.getApiName(), SFAPreDefineObject.BomAttributeConstraintLines.getApiName()), tenantId);
    }

    @Override
    protected ObjectDescribe getDescribeFromLocalResource(String apiName, String tenantId) {
        ObjectDescribe describe = super.getDescribeFromLocalResource(apiName, tenantId);
        if(Objects.equals(apiName,SFAPreDefineObject.BomAttributeConstraint.getApiName())){
            IFieldDescribe coreField = FieldDescribeFactory.newInstance(FIELD_CONSTRAINT_CORE_ID);
            if (describe.getFieldDescribe(coreField.getApiName()) == null) {
                describe.addFieldDescribe(coreField);
            } else {
                describe.getFieldDescribe(coreField.getApiName()).setActive(true);
            }
            IFieldDescribe productFieldDescribe = describe.getFieldDescribe(BomConstants.FIELD_PRODUCT_ID);
            if (productFieldDescribe != null) {
                productFieldDescribe.setStatus("deleted");
                productFieldDescribe.setActive(false);
            }
        }
        if (Objects.equals(apiName, Utils.BOM_CORE_API_NAME)) {
            SelectOneFieldDescribe field = (SelectOneFieldDescribe) describe.getFieldDescribe("sale_strategy");
            if (field != null) {
                ISelectOption option = new SelectOption();
                option.setValue("sub");
                option.setLabel("按子件灵活折扣售卖");
                List<ISelectOption> selectOptions = field.getSelectOptions();
                selectOptions.add(option);
                field.setSelectOptions(selectOptions);
                field.setDefaultValue("sub");
            }
        }
        return describe;
    }
}
