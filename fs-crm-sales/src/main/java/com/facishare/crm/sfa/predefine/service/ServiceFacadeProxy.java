package com.facishare.crm.sfa.predefine.service;

import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-04-26
 * ============================================================
 */
@Service
@Slf4j
public class ServiceFacadeProxy {
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private InfraServiceFacade infraServiceFacade;

    public String generateId() {
        return serviceFacade.generateId();
    }

    public QueryResult<IObjectData> findBySearchQuery(User user, String apiName, SearchTemplateQuery searchTemplateQuery) {
        return serviceFacade.findBySearchQuery(user, apiName, searchTemplateQuery);
    }

    public List<IObjectData> findObjectDataByIdsIgnoreAll(String tenantId, List<String> categoryIds, String apiName) {
        return serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, categoryIds, apiName);
    }

    public void batchUpdateByFields(User user, List<IObjectData> data, List<String> fields) {
        serviceFacade.batchUpdateByFields(user, data, fields);
    }

    public IObjectData findObjectData(User user, String objectDataId, String apiName) {
        return serviceFacade.findObjectData(user, objectDataId, apiName);
    }

    public FunctionLogicService getFunctionLogicService() {
        return serviceFacade.getFunctionLogicService();
    }

    public void fillQuoteValueVirtualField(User user, IObjectData data, Map<String, List<IObjectData>> map) {
        infraServiceFacade.fillQuoteValueVirtualField(user, data, map);
    }

    public BaseObjectSaveAction.Result triggerAction(ActionContext actionContext, BaseObjectSaveAction.Arg arg, Class<BaseObjectSaveAction.Result> resultClass) {
        return serviceFacade.triggerAction(actionContext, arg, resultClass);
    }

    public void bulkSaveObjectData(List<IObjectData> dataList, User user) {
        serviceFacade.bulkSaveObjectData(dataList, user);
    }

    public void updateObjectData(User user, IObjectData objectData) {
        serviceFacade.updateObjectData(user, objectData);
    }

    public List<IObjectData> findObjectDataByIds(String tenantId, ArrayList<String> ids, String apiName) {
        return serviceFacade.findObjectDataByIds(tenantId, ids, apiName);
    }

    public void bulkInvalidAndDeleteWithSuperPrivilege(List<IObjectData> data, User user) {
        serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(data, user);
    }

    public List<IObjectData> aggregateFindBySearchQueryWithGroupFields(User user,
                                                                       SearchTemplateQuery query,
                                                                       String describeApiName,
                                                                       List<String> groupFields,
                                                                       String aggFunction,
                                                                       String aggField) {
        return serviceFacade.aggregateFindBySearchQueryWithGroupFields(user, query, describeApiName, groupFields, aggFunction, aggField);
    }
}
