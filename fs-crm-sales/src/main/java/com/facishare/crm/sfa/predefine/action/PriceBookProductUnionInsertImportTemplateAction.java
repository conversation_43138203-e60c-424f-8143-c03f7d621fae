package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.PriceBookImportUtils;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/15
 */
public class PriceBookProductUnionInsertImportTemplateAction extends StandardUnionInsertImportTemplateAction {

    @Override
    protected void customDetailHeader(List<IFieldDescribe> headerFieldList) {
        super.customDetailHeader(headerFieldList);
        PriceBookImportUtils.removeCurrencyFields(actionContext.getTenantId(), headerFieldList);
    }
}
