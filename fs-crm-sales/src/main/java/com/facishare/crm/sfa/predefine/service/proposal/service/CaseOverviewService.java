package com.facishare.crm.sfa.predefine.service.proposal.service;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.platform.AssertValidator;
import com.facishare.crm.platform.converter.Converter;
import com.facishare.crm.sfa.predefine.service.proposal.access.CaseOverviewAccess;
import com.facishare.crm.sfa.predefine.service.proposal.access.ObjectDataAccess;
import com.facishare.crm.sfa.predefine.service.proposal.dto.CaseOverviewDTO;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.QuickCreateCaseOverviewRequest;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.UpdateCaseOverviewRequest;
import com.facishare.crm.sfa.predefine.service.proposal.vo.CaseOverviewVO;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.facishare.crm.sfa.predefine.service.proposal.access.CaseOverviewAccess.CASE_OVERVIEW_OBJ;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Service
@Slf4j
public class CaseOverviewService {
    @Resource
    private CaseOverviewAccess caseOverviewAccess;

    @Resource(name = "objectConverter")
    private Converter converter;
    @Resource
    private ObjectDataAccess objectDataAccess;
    @Resource
    private AssertValidator assertValidator;

    public CaseOverviewVO queryCaseOverview(User user, String id) {
        assertValidator.assertAllNotBlank(id);
        IObjectData objectData = caseOverviewAccess.queryContent(user, id);
        return converter.convertDTO(objectData, CaseOverviewVO.class);
    }

    public boolean exists(User user, String caseOverviewId) {
        assertValidator.assertAllNotBlank(caseOverviewId);
        return caseOverviewAccess.exists(user, caseOverviewId);
    }

    public IObjectData queryCaseOverviewRelatedInfo(User user, String caseOverviewId) {
        assertValidator.assertAllNotBlank(caseOverviewId);
        return objectDataAccess.queryByOwner(user, CASE_OVERVIEW_OBJ, caseOverviewId, Lists.newArrayList("_id", "related_object_describe", "related_data_id", "content"));
    }

    public boolean update(User user, UpdateCaseOverviewRequest request) {
        assertValidator.assertAllNotBlank(request.getId());
        IObjectData objectData = caseOverviewAccess.queryById(user, request.getId());
        assertValidator.assertNotNull(objectData);
        objectData.set("content", request.getContent());
        caseOverviewAccess.updateByFields(user, objectData, Sets.newHashSet("content"));
        return true;
    }


    public String quickCreate(User user, QuickCreateCaseOverviewRequest caseOverview) {
        CaseOverviewDTO caseOverviewDTO = converter.convertDTO(caseOverview, CaseOverviewDTO.class);
        assertValidator.assertAllNotBlank(caseOverview.getRelatedDataId(), caseOverview.getRelatedObjectDescribe());
        String relatedObjectDescribe = caseOverviewDTO.getRelatedObjectDescribe();
        switch (relatedObjectDescribe) {
            case "AccountObj":
                caseOverviewDTO.setAccountId(caseOverviewDTO.getRelatedDataId());
                break;
            case "LeadObj":
                caseOverviewDTO.setLeadsId(caseOverviewDTO.getRelatedDataId());
                break;
            case "NewOpportunityObj":
                caseOverviewDTO.setNewOpportunityId(caseOverviewDTO.getRelatedDataId());
                break;
            default:
                //ignore
        }
        IObjectData data = converter.convertObjectData(caseOverviewDTO);
        String owner = objectDataAccess.getOwner(user);
        assertValidator.assertAllNotBlank(owner);
        data.setOwner(Lists.newArrayList(owner));
        return caseOverviewAccess.create(user, data).getId();
    }
}
