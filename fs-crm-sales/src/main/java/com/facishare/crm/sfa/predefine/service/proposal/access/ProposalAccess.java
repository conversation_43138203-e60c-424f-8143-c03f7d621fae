package com.facishare.crm.sfa.predefine.service.proposal.access;

import com.facishare.crm.sfa.predefine.service.GoalValue.utilities.SearchUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Component
@Slf4j
public class ProposalAccess {
    @Resource
    private ObjectDataAccess objectDataAccess;

    public static final String PROPOSAL_OBJ = "ProposalObj";
    private static final String CREATE_TIME = "create_time";


    public List<IObjectData> limitList(User user, String relatedObjectDescribe, String relatedDataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.order(CREATE_TIME, false);
        query.setLimit(20);
        SearchUtil.fillFilterEq(query.getFilters(), "related_object_describe", relatedObjectDescribe);
        SearchUtil.fillFilterEq(query.getFilters(), "related_data_id", relatedDataId);
        return objectDataAccess.queryOwnerListByTemplate(user, PROPOSAL_OBJ, query, Lists.newArrayList(ObjectDataExt.ID, ObjectDataExt.NAME));
    }

    public IObjectData queryById(User user, String id) {
        return objectDataAccess.queryByOwner(user, PROPOSAL_OBJ, id);
    }

    public void updateByFields(User user, IObjectData originalData, Set<String> fieldApiNames) {
        objectDataAccess.updateByFields(user, originalData, fieldApiNames);
    }

    public void delete(User user, String id) {
        objectDataAccess.delete(user, PROPOSAL_OBJ, id);
    }

    public IObjectData create(User user, IObjectData objectData) {
        return objectDataAccess.create(user, objectData, PROPOSAL_OBJ);
    }
}
