package com.facishare.crm.sfa.predefine;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * SFA 预定义对象定义并注册
 * <p>
 * Created by liyiguang on 2017/7/9.
 */

@Slf4j
public enum SFAPreDefineObject implements PreDefineObject {

    AccountMainData("AccountMainDataObj"), Account("AccountObj"), AccountTreeRelation("AccountTreeRelationObj"), Contact("ContactObj"), SalesOrder("SalesOrderObj"), PriceBook("PriceBookObj"), PriceBookProduct("PriceBookProductObj"), Product("ProductObj"),
    Payment("PaymentObj"), Refund("RefundObj"), Opportunity("OpportunityObj"), Leads("LeadsObj"), LeadsTransferLog("LeadsTransferLogObj"), ReturnedGoodsInvoice("ReturnedGoodsInvoiceObj"),
    Quote("QuoteObj"), QuoteLines("QuoteLinesObj"), ReturnedGoodsInvoiceProduct("ReturnedGoodsInvoiceProductObj"),
    SalesOrderProduct("SalesOrderProductObj"), Cases("CasesObj"), Partner("PartnerObj"),
    //    GoalValue("GoalValueObj"),
//    Visiting("VisitingObj"),
    NewOpportunity("NewOpportunityObj"), NewOpportunityLines("NewOpportunityLinesObj"), NewOpportunityContacts("NewOpportunityContactsObj"), SPU("SPUObj"),
    Specification("SpecificationObj"), SpecificationValue("SpecificationValueObj"), Contract("ContractObj"),
    InvoiceApplication("InvoiceApplicationObj"), MarketingEvent("MarketingEventObj"), AccountAddr("AccountAddrObj"), AccountFinInfo("AccountFinInfoObj"),
    LeadsPoolAllocateRuleObj("LeadsPoolAllocateRuleObj"), LeadsPoolAllocateRuleMemberObj("LeadsPoolAllocateRuleMemberObj"),
    LeadsPoolAllocateRuleMemberRecordObj("LeadsPoolAllocateRuleMemberRecordObj"), RuleFilterObj("RuleFilterObj"), CrmFeedTag("crm_feed_tag"),
    SubProduct("SubProductObj"), CRMFeedObj("CRMFeedObj"), SaleEventObj("SaleEventObj"), UnitInfo("UnitInfoObj"), MultiUnitRelated("MultiUnitRelatedObj"),
    //    TieredPriceBook("TieredPriceBookObj"), TieredPriceBookProduct("TieredPriceBookProductObj"), TieredPriceBookRule("TieredPriceBookRuleObj"),
    ProductConstraint("ProductConstraintObj"), ProductConstraintLines("ProductConstraintLinesObj"), SubProductCatalog("SubProductCatalogObj"), BehaviorIntegralDetail("BehaviorIntegralDetailObj"),
    InvoiceApplicationLines("InvoiceApplicationLinesObj"), ProductCategory("ProductCategoryObj"), LeadsFlowRecord("LeadsFlowRecordObj"),
    Bom("BOMObj"),BomCore("BomCoreObj"), ProductGroup("ProductGroupObj"),
    AvailableRange("AvailableRangeObj"),
    AvailablePriceBook("AvailablePriceBookObj"),
    AvailableAccount("AvailableAccountObj"),
    AvailableProduct("AvailableProductObj"),
    AvailableAccountResult("AvailableAccountResultObj"),
    AvailableProductResult("AvailableProductResultObj"),
    CommonUnit("CommonUnitObj"),
    Attribute("AttributeObj"), AttributeValue("AttributeValueObj"), ProductAttribute("ProductAttributeObj"), ProductCategoryAttribute("ProductCategoryAttributeObj"),
    AttributePriceBook("AttributePriceBookObj"), AttributePriceBookLines("AttributePriceBookLinesObj"), AttributeApplicablePriceBook("AttributeApplicablePriceBookObj"),
    CampaignMembers("CampaignMembersObj"), EnterpriseInfo("EnterpriseInfoObj"),
    PricePolicy("PricePolicyObj"), PricePolicyAccount("PricePolicyAccountObj"), PricePolicyProduct("PricePolicyProductObj"),
    PricePolicyRule("PricePolicyRuleObj"), AggregateRule("AggregateRuleObj"), MarketingKeyword("MarketingKeywordObj"), KeywordServingPlan("KeywordServingPlanObj"), TermServingLines("TermServingLinesObj"), PricePolicyExcludeAccount("PricePolicyExcludeAccountObj"), PricePolicyLimitAccount("PricePolicyLimitAccountObj"),
    AmortizeInfo("AmortizeInfoObj"), LandingPage("LandingPageObj"), LandingPageVisitorDetail("LandingPageVisitorDetailObj"), Project("ProjectObj"),
    SaleContract("SaleContractObj"), SaleContractLine("SaleContractLineObj"), BehaviorRecord("BehaviorRecordObj"),
    MarketingAttribution("MarketingAttributionObj"), MarketingEventInfluence("MarketingEventInfluenceObj"),
    OrderOccupy("OrderOccupyObj"), PolicyOccupy("PolicyOccupyObj"), Rebate("RebateObj"), RebateDetail("RebateDetailObj"),
    RebateRule("RebateRuleObj"), RebatePolicy("RebatePolicyObj") ,RebatePolicyRule("RebatePolicyRuleObj") ,RebatePolicyLog("RebatePolicyLogObj") , CouponPlan("CouponPlanObj"), CouponInstance("CouponInstanceObj"), RFMRule("RFMRuleObj"), ProcurementAnalysis("ProcurementAnalysisObj"), ProcurementRule("ProcurementRuleObj"), ProcurementAccount("ProcurementAccountObj"),

    ProcurementSearch("ProcurementSearchObj"),ProcurementInfo("ProcurementInfoObj"),ProcurementEnterprise("ProcurementEnterpriseObj"),ProcurementContact("ProcurementContactObj"),ProcurementInfoLines("ProcurementInfoLinesObj"),ProcurementTransferLog("ProcurementTransferLogObj"),ProcurementComparison("ProcurementComparisonObj"),
    HistoricalBiddingImport("HistoricalBiddingImportObj"), BiddingSubscriptionRules("BiddingSubscriptionRulesObj"), BiddingSubscription("BiddingSubscriptionObj"),
    Competitor("CompetitorObj"), BomInstance("BomInstanceObj"),BomCoreInstance("BomCoreInstanceObj"),
    CompetitiveProducts("CompetitiveProductsObj"),EquityRelationship("EquityRelationshipObj"),
    AccountDepartment("AccountDepartmentObj"),Position("PositionObj"),ContactRelationship("ContactRelationshipObj"),
    CompetitiveLines("CompetitiveLinesObj"),BomAttributeConstraint("BomAttributeConstraintObj"),BomAttributeConstraintLines("BomAttributeConstraintLinesObj"),
    RFMAssessResult("RFMAssessResultObj"), ForecastRule("ForecastRuleObj"), ForecastTask("ForecastTaskObj"), ForecastTaskDetail("ForecastTaskDetailObj"), PriceBookAccount("PriceBookAccountObj"),NonstandardAttribute("NonstandardAttributeObj"),ContactMemberRelationship("ContactMemberRelationshipObj"),
    PartnerAddr("PartnerAddrObj"), WechatSession("WechatSessionObj"), WechatGroup("WechatGroupObj"), WechatGroupUser("WechatGroupUserObj"), WechatEmployee("WechatEmployeeObj"), SaleActionNew("SaleActionNewObj"), SaleActionStageNew("SaleActionStageNewObj"), UserDefinedField("UserDefinedFieldObj"),
    QualityInspectionRule("QualityInspectionRuleObj"), QualityInspection("QualityInspectionObj"), InheritRecord("InheritRecordObj"), WechatFriendsRecord("WechatFriendsRecordObj"),
    SaleContractChange("SaleContractChangeObj"),SaleContractLineChange("SaleContractLineChangeObj"),SaleContractAvailableRangeRelation("SaleContractAvailableRangeRelationObj"),SaleContractPriceBookRelation("SaleContractPriceBookRelationObj"),
    AccountRiskPortraitRecord("AccountRiskPortraitRecordObj"),PivotTableRule("PivotTableRuleObj"), PivotTableMapping("PivotTableMappingObj"), PivotTableInstance("PivotTableInstanceObj"), PivotTableHistory("PivotTableHistoryObj"), PivotTableHistorySummary("PivotTableHistorySummaryObj"), PivotTableHistoryData("PivotTableHistoryDataObj"),
    EnterpriseRisk("EnterpriseRiskObj"),OpportunityDecisionChain("OpportunityDecisionChainObj"),OpportunityDecisionChainDetail("OpportunityDecisionChainDetailObj"),WechatSessionAnalysisRule("WechatSessionAnalysisRuleObj"), WechatSessionAnalysis("WechatSessionAnalysisObj"), WechatSessionAnalysisMaster("WechatSessionAnalysisMasterObj"),
    SuspectedOpportunity("SuspectedOpportunityObj"),SalesOrderChange("SalesOrderChangeObj"),SalesOrderProductChange("SalesOrderProductChangeObj"),AttributeConstraint("AttributeConstraintObj"),AttributeConstraintLines("AttributeConstraintLinesObj"),
    PartnerDepartment("PartnerDepartmentObj"),SubAccountTreeRelationLog("SubAccountTreeRelationLogObj"),SubAccountTreeRelation("SubAccountTreeRelationObj"),
    ActivitySummaryObj("ActivitySummaryObj"),
    ActivitySummaryDetailObj("ActivitySummaryDetailObj"),BizQueryManage("BizQueryManageObj"),BizQuerySearch("BizQuerySearchObj"),
    PartnerContactRelationship("PartnerContactRelationshipObj"),
    AdvancedFormula("AdvancedFormulaObj"),AdvancedFormulaLine("AdvancedFormulaLineObj"),
    ProcurementConsumptionRecords("ProcurementConsumptionRecordsObj"),
    ProcurementQlmBuyDataBill("ProcurementQlmBuyDataBillObj"),
    OperationsStrategy("OperationsStrategyObj"), OperationsActivityTemplate("OperationsActivityTemplateObj"), OperationsTaskTemplate("OperationsTaskTemplateObj"), OperationsActivity("OperationsActivityObj"), OperationsTask("OperationsTaskObj"),
    ServiceMarket("ServiceMarketObj"),ServiceMarketDetail("ServiceMarketDetailObj"), ServiceMarketLog("ServiceMarketLogObj"), BusinessRiskInformation("BusinessRiskInformationObj"),
    AccountsReceivableNote("AccountsReceivableNoteObj"), AccountsReceivableDetail("AccountsReceivableDetailObj"), MatchNote("MatchNoteObj"), MatchNoteDetail("MatchNoteDetailObj"), SalesInvoice("SalesInvoiceObj"), SalesInvoiceDetail("SalesInvoiceDetailObj"),
    AIInsightsResults("AIInsightsResultsObj"),
    ContactEvaluation("ContactEvaluationObj"),CustomerRetentionStrategy("CustomerRetentionStrategyObj"),CustomerRetentionObjectives("CustomerRetentionObjectivesObj"),
    MCRExpansionPlan("MCRExpansionPlanObj"),Vocalize("VocalizeObj"), NewOpportunityContactRelationship("NewOpportunityContactRelationshipObj"), SFAPromptTemplate("SFAPromptTemplateObj"),
    QuoteChange("QuoteChangeObj"), QuoteLinesChange("QuoteLinesChangeObj"),
    PartnerProvision("PartnerProvisionObj"),PartnerAgreement("PartnerAgreementObj"),RelationTemplate("RelationTemplateObj"), EmailAccount("EmailAccountObj"),
    BankStatement("BankStatementObj"),VirtualAccount("VirtualAccountObj"),ReceivedPayment("ReceivedPaymentObj"),AttributeGroup("AttributeGroupObj"),
    PartnerAgreementDetail("PartnerAgreementDetailObj"),
    Settlement("SettlementObj"),SettlementDetail("SettlementDetailObj"),ActivityQuestion("ActivityQuestionObj"),ActivityBusiness("ActivityBusinessObj"),
    SalesTopicLibrary("SalesTopicLibraryObj"),InteractionStrategy("InteractionStrategyObj"),InteractionStrategyDetail("InteractionStrategyDetailObj"),
    TermBank("TermBankObj"),AgreementStatusRecord("AgreementStatusRecordObj"),
    ContractProgressRule("ContractProgressRuleObj"), ContractProgressRuleGoal("ContractProgressRuleGoalObj"),
    ContractProgressRuleGoalCheck("ContractProgressRuleGoalCheckObj"), ContractProgressRuleGoalSnapshot("ContractProgressRuleGoalSnapshotObj"),
    ActivityResourceUsageDetail("ActivityResourceUsageDetailObj"),
    ActivityAccountSummary("ActivityAccountSummaryObj"),ActivityOpportunitySummary("ActivityOpportunitySummaryObj"),ActivityContactSummary("ActivityContactSummaryObj"),ActivityDepartmentSummary("ActivityDepartmentSummaryObj"),
    Requirement("RequirementObj"),KnowledgeClass("KnowledgeClassObj"),KnowledgeDocument("KnowledgeDocumentObj"),SalesCoachRecord("SalesCoachRecordObj"),
    AccountsReceivableQuickRule("AccountsReceivableQuickRuleObj"),ObjectArRuleRelated("ObjectArRuleRelatedObj")
    ;

    private static final String PACKAGE_NAME = SFAPreDefineObject.class.getPackage().getName();
    private final String apiName;

    SFAPreDefineObject(String apiName) {
        this.apiName = apiName;
    }

    public static SFAPreDefineObject getEnum(String apiName) {
        List<SFAPreDefineObject> list = Arrays.asList(SFAPreDefineObject.values());
        return list.stream().filter(m -> m.getApiName().equalsIgnoreCase(apiName)).findAny().orElse(null);
    }

    public static void init() {
        for (SFAPreDefineObject object : SFAPreDefineObject.values()) {
            PreDefineObjectRegistry.register(object);
        }
    }

    @Override
    public String getApiName() {
        return apiName;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this + actionCode + "Action";
        ActionClassInfo actionClassInfo = new ActionClassInfo(className);
        if (isPartnerAction(actionCode) && !check(actionClassInfo)) {
            return new ActionClassInfo(PACKAGE_NAME + ".action.Base" + actionCode + "Action");
        }
        return actionClassInfo;
    }

    private Boolean isPartnerAction(String actionCode) {
        List<String> partnerActionCodes = Lists.newArrayList(ObjectAction.CHANGE_PARTNER.getActionCode(),
                ObjectAction.CHANGE_PARTNER_OWNER.getActionCode(), ObjectAction.DELETE_PARTNER.getActionCode(),
                "EnablePartnerView");
        return partnerActionCodes.contains(actionCode);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this + methodName + "Controller";
        return new ControllerClassInfo(className);
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    private boolean check(ActionClassInfo classInfo) {
        try {
            Class.forName(classInfo.getClassName());
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

}
