package com.facishare.crm.sfa.predefine.service.proposal.dto;

import com.facishare.crm.platform.annotation.Convertible;
import lombok.Data;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Data
@Convertible
public class ProposalDTO {
    private String caseOverviewId;
    private String relatedObjectDescribe;
    private String relatedDataId;
    private String accountId;
    private String leadsId;
    private String newOpportunityId;
    private String content;
}
