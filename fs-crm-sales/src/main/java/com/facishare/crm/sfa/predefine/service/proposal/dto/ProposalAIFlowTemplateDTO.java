package com.facishare.crm.sfa.predefine.service.proposal.dto;

import com.facishare.crm.platform.annotation.Convertible;
import com.facishare.crm.platform.annotation.Diff;
import lombok.Data;

import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Data
@Convertible
@Diff
public class ProposalAIFlowTemplateDTO {
    private String name;
    private String flowId;
    private String describe;
    private List<String> outputNodes;
}
