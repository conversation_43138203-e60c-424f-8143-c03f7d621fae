package com.facishare.crm.sfa.predefine.service.proposal.access;

import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.util.Safes;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Component
@Slf4j
public class CaseOverviewAccess {
    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Resource
    private ObjectDataAccess objectDataAccess;

    public static final String CASE_OVERVIEW_OBJ = "CaseOverviewObj";


    public boolean exists(User user, String id) {
        List<IObjectData> dataList = metaDataFindServiceExt.findObjectByIdsWithFieldsIgnoreAll(user, Lists.newArrayList(id),
                CASE_OVERVIEW_OBJ, Lists.newArrayList(DBRecord.ID));
        return Safes.isNotEmpty(dataList);
    }

    public IObjectData queryById(User user, String id) {
        return objectDataAccess.queryByOwner(user, CASE_OVERVIEW_OBJ, id);
    }

    public IObjectData create(User user, IObjectData objectData) {
        return objectDataAccess.create(user, objectData, CASE_OVERVIEW_OBJ);
    }

    public IObjectData queryContent(User user, String id) {
        return objectDataAccess.queryByOwner(user, CASE_OVERVIEW_OBJ, id, Lists.newArrayList("_id", "content"));
    }

    public void updateByFields(User user, IObjectData objectData, Set<String> updateFields) {
        objectDataAccess.updateByFields(user, objectData, updateFields);
    }
}
