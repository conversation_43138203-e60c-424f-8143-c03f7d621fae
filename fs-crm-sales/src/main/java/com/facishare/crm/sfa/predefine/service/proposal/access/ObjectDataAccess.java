package com.facishare.crm.sfa.predefine.service.proposal.access;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.lto.utils.Safes;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-20
 * ============================================================
 */
@Component
@Slf4j
public class ObjectDataAccess {
    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private ObjectDataAccess objectDataAccess;

    public IObjectData create(User user, IObjectData objectData, String objectApiName) {
        objectData.setDescribeApiName(objectApiName);
        objectData.setTenantId(user.getTenantId());
        return serviceFacade.saveObjectData(user, objectData);
    }

    public IObjectData update(User user, IObjectData objectData) {
        return serviceFacade.updateObjectData(user, objectData);
    }

    public IObjectData query(User user, String objectApiName, String id) {
        return query(user, objectApiName, id, Lists.newArrayList());
    }

    public IObjectData queryByOwner(User user, String objectApiName, String id) {
        return queryByOwner(user, objectApiName, id, Lists.newArrayList());
    }

    public IObjectData queryByOwner(User user, String objectApiName, String id, List<String> fields) {
        String owner = getOwner(user);
        if (StringUtils.isBlank(owner)) {
            return null;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), "owner", owner);
        SearchUtil.fillFilterEq(query.getFilters(), "_id", id);
        return queryByTemplate(user, objectApiName, query, fields);
    }

    public String getOwner(User user) {
        return user.isOutUser() ? user.getOutUserId() : user.getUpstreamOwnerIdOrUserId();
    }


    public IObjectData query(User user, String objectApiName, String id, List<String> fields) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), "_id", id);
        return queryByTemplate(user, objectApiName, query, fields);
    }


    public IObjectData queryByTemplate(User user, String objectApiName, SearchTemplateQuery query, List<String> fields) {
        List<IObjectData> dataList = queryListByTemplate(user, objectApiName, query, fields);
        return Safes.first(dataList);
    }

    public List<IObjectData> queryListByTemplate(User user, String objectApiName, SearchTemplateQuery query, List<String> fields) {
        if (query == null) {
            query = new SearchTemplateQuery();
        }
        List<IObjectData> dataList;
        if (CollectionUtils.isEmpty(fields)) {
            dataList = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, objectApiName, query).getData();
        } else {
            dataList = metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, objectApiName, query, fields);
        }
        return dataList;
    }

    public List<IObjectData> queryOwnerListByTemplate(User user, String objectApiName, SearchTemplateQuery query, List<String> fields) {
        String owner = getOwner(user);
        if (StringUtils.isBlank(owner)) {
            return Lists.newArrayList();
        }
        SearchUtil.fillFilterEq(query.getFilters(), "owner", owner);
        return queryListByTemplate(user, objectApiName, query, fields);
    }


    public IObjectData delete(User user, String objectApiName, String id) {
        return objectDataAccess.deleteByOwner(user, objectApiName, id);
    }

    public IObjectData deleteByOwner(User user, String objectApiName, String id) {
        IObjectData data = queryByOwner(user, objectApiName, id);
        if (data == null) {
            log.warn("ObjectDataAccess#No data found for id {}", id);
            return null;
        }
        List<IObjectData> dataList = serviceFacade.bulkDeleteDirect(Lists.newArrayList(data), user);
        return Safes.first(dataList);
    }

    public void updateByFields(User user, IObjectData objectData, Set<String> updateFields) {
        if (user == null) {
            throw new IllegalArgumentException("User is null");
        }
        if (objectData == null) {
            throw new IllegalArgumentException("ObjectData is null");
        }
        if (CollectionUtils.isEmpty(updateFields)) {
            throw new IllegalArgumentException("updateFields is empty");
        }
        metaDataFindServiceExt.bulkUpdateByFields(user, Lists.newArrayList(objectData), Lists.newArrayList(updateFields));
    }
}
