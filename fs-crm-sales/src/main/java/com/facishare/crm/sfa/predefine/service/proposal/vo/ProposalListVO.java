package com.facishare.crm.sfa.predefine.service.proposal.vo;

import com.facishare.crm.platform.annotation.Convertible;
import com.facishare.crm.platform.annotation.Mappable;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Data
@Convertible
@JsonInclude(JsonInclude.Include.ALWAYS)
public class ProposalListVO {
    @Mappable(fieldApiName = "_id")
    private String id;
    private String name;
}
