package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.PriceBookImportUtils;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/15
 */
public class PriceBookProductInsertImportTemplateAction extends StandardInsertImportTemplateAction {

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        super.customHeader(headerFieldList);
        PriceBookImportUtils.removeCurrencyFields(actionContext.getTenantId(), headerFieldList);
    }
}
