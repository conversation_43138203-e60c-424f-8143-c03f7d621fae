package com.facishare.crm.sfa.predefine.service.proposal.access;

import com.facishare.crm.sfa.predefine.service.GoalValue.utilities.SearchUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Slf4j
@Component
public class ApiCallLogAccess {
    @Resource
    private ObjectDataAccess objectDataAccess;

    private static final String API_CALL_LOG_OBJ = "ApiCallLogObj";

    public IObjectData create(User user, IObjectData objectData) {
        return objectDataAccess.create(user, objectData, API_CALL_LOG_OBJ);
    }

    public List<IObjectData> listByBiz(User user, String bizSource, String platform) {
        if (StringUtils.isAnyBlank(bizSource, platform)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(0);
        SearchUtil.fillFilterEq(query.getFilters(), "bizSource", bizSource);
        SearchUtil.fillFilterEq(query.getFilters(), "platform", platform);
        return objectDataAccess.queryListByTemplate(user, API_CALL_LOG_OBJ, query, Lists.newArrayList(
                "_id",
                "response_message",
                "request_time",
                "response_time",
                "status_code",
                "success",
                "owner"));

    }
}
