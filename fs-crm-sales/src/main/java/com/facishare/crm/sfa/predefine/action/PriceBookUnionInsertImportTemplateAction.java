package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.util.PriceBookImportUtils;
import com.facishare.crm.sfa.utilities.util.PriceBookUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;

public class PriceBookUnionInsertImportTemplateAction extends StandardUnionInsertImportTemplateAction {

    @Override
    protected void customMasterHeader(List<IFieldDescribe> masterFieldList) {
        super.customMasterHeader(masterFieldList);
        PriceBookUtil.removeUnsupportedImportFields(masterFieldList);
        PriceBookImportUtils.removeAndAddFields(actionContext.getTenantId(), masterFieldList);
    }

    @Override
    protected void customDetailHeader(List<IFieldDescribe> detailFieldList) {
        super.customDetailHeader(detailFieldList);
        PriceBookImportUtils.removeDetailFields(actionContext.getTenantId(), detailFieldList);
        PriceBookImportUtils.removeCurrencyFields(actionContext.getTenantId(), detailFieldList);
    }

    @Override
    protected String getFieldSampleValue(IFieldDescribe field) {
        if (PriceBookConstants.Field.VIRTUAL_EMPLOYEE_NAME.getApiName().equals(field.getApiName())) {
            return I18N.text("AvailableRangeObj.virtual_employee_name.sample");
        } else if (PriceBookConstants.Field.VIRTUAL_DEPT_NAME.getApiName().equals(field.getApiName())) {
            return I18N.text("AvailableRangeObj.virtual_dept_name.sample");
        }
        return super.getFieldSampleValue(field);
    }
}