package com.facishare.crm.sfa.predefine.service.proposal.vo;

import com.facishare.crm.platform.annotation.Convertible;
import com.facishare.crm.platform.annotation.Mappable;
import com.facishare.crm.sfa.utilities.proxy.model.OneFlowOfRemote;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Data
@Convertible
@JsonInclude(JsonInclude.Include.ALWAYS)
public class ProposalAIFlowTemplateVO {
    @Mappable(fieldApiName = "_id")
    private String id;
    private String name;
    private String flowId;
    private String flowName;
    private String describe;
    private List<String> outputNodes;
    private List<OneFlowOfRemote.FlowNode> outputNodeInfo;
}
