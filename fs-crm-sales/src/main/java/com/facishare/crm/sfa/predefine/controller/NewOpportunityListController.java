package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.sfa.predefine.service.NewOpportunityInitService;
import com.facishare.crm.sfa.utilities.util.NewOpportunityUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.metadata.MetaDataComputeServiceImpl;
import com.facishare.paas.appframework.metadata.MtCurrency;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@SuppressWarnings("Duplicates")
@EqualsAndHashCode(callSuper = true)
public class NewOpportunityListController extends StandardListController {


    private final NewOpportunityInitService newOpportunityInitService = SpringUtil.getContext().getBean(NewOpportunityInitService.class);

    private final MetaDataComputeServiceImpl metaDataComputeService = SpringUtil.getContext().getBean(MetaDataComputeServiceImpl.class);

    private SearchTemplateQuery countQuery;

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        query.setWhatSearchParameter(null);
        return query;
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        SearchTemplateQuery query = super.customSearchTemplate(searchQuery);
        query.setWhatSearchParameter(null);
        return query;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);

        SearchTemplateQuery query = this.serviceFacade
                .getSearchTemplateQuery(controllerContext.getUser(), objectDescribe,
                        arg.getSearchTemplateId(), arg.getSearchQueryInfo());
        List<ObjectDataDocument> dataList = result.getDataList();
        if (query.getLimit() > 0 && dataList.size() < 1000) {
            //拼装列表
            String tenantId = controllerContext.getUser().getTenantId();
            String userID = controllerContext.getUser().getUpstreamOwnerIdOrUserId();
            result.setDataList(newOpportunityInitService.stagePercent(dataList, tenantId, userID));
        }

        return result;
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        super.beforeQueryData(query);
        countQuery = SearchUtil.copySearchTemplateQuery(query);
        countQuery.getOrders().clear();
    }

    @Override
    protected NewOpportunityListResult buildResult(List<ILayout> layouts, ISearchTemplateQuery query, QueryResult<IObjectData> queryResult) {
        Result result = super.buildResult(layouts, query, queryResult);
        NewOpportunityListResult newOpportunityListResult = new NewOpportunityListResult();
        newOpportunityListResult.setObjectDescribe(result.getObjectDescribe());
        newOpportunityListResult.setListLayouts(result.getListLayouts());
        newOpportunityListResult.setDataList(result.getDataList());
        newOpportunityListResult.setTotal(result.getTotal());
        newOpportunityListResult.setLimit(result.getLimit());
        newOpportunityListResult.setOffset(result.getOffset());
        newOpportunityListResult.setUserInfos(result.getUserInfos());
        newOpportunityListResult.setButtonInfo(result.getButtonInfo());
        newOpportunityListResult.setObjectDescribeExt(result.getObjectDescribeExt());
        newOpportunityListResult.setDataTagInfo(result.getDataTagInfo());
        newOpportunityListResult.setAllPageSummaryData(result.getAllPageSummaryData());
        newOpportunityListResult.setExtendInfo(result.getExtendInfo());
        newOpportunityListResult.setLayout(result.getLayout());
        newOpportunityListResult.setRelatedObjectDataSpecified(result.getRelatedObjectDataSpecified());
        newOpportunityListResult.setSupportFullFieldSearchOnListPage(result.getSupportFullFieldSearchOnListPage());
        newOpportunityListResult.setRecordTypeMapping(result.getRecordTypeMapping());
        newOpportunityListResult.setRelatedObjectDataSpecified(result.getRelatedObjectDataSpecified());

        List<IFilter> filters = countQuery.getFilters();
        if (filters.stream().anyMatch(x -> "sales_process_id".equals(x.getFieldName()))
                || filters.stream().anyMatch(x -> "sales_stage".equals(x.getFieldName()))) {
            //拼装totleAmount
            Count countFieldDescribe = new CountFieldDescribe();
            countFieldDescribe.setApiName(Utils.NEW_OPPORTUNITY_API_NAME);
            countFieldDescribe.setFieldApiName("amount0");
            countFieldDescribe.setSubObjectDescribeApiName(Utils.NEW_OPPORTUNITY_API_NAME);
            if (objectDescribe.containsMultiCurrencyField() && objectDescribe.containsField("base_amount")) {
                countFieldDescribe.setCountFieldApiName("base_amount");
            } else {
                countFieldDescribe.setCountFieldApiName("amount");
            }
            countFieldDescribe.setCountType(Count.TYPE_SUM);
            countFieldDescribe.setReturnType("number");
            countFieldDescribe.setDecimalPlaces(2);
            stopWatch.lap("buildResult getCountValue begin");
            Object obj = metaDataComputeService.getCountValue(controllerContext.getUser(), countFieldDescribe, SearchUtil.copySearchTemplateQuery(countQuery));
            Optional<MtCurrency> currency = NewOpportunityUtil.preferCurrencyFromLayout(controllerContext.getUser(), objectDescribe, serviceFacade);
            if (obj != null) {
                if (currency.isPresent()) {
                    newOpportunityListResult.setTotalAmount(NewOpportunityUtil.recalculateByCurrency(new BigDecimal(obj.toString()), currency.get()));
                } else {
                    newOpportunityListResult.setTotalAmount(new BigDecimal(obj.toString()));
                }
            } else {
                log.warn("amount is null");
            }
            countFieldDescribe = new CountFieldDescribe();
            countFieldDescribe.setApiName(Utils.NEW_OPPORTUNITY_API_NAME);
            countFieldDescribe.setFieldApiName("amount1");
            countFieldDescribe.setSubObjectDescribeApiName(Utils.NEW_OPPORTUNITY_API_NAME);
            if (objectDescribe.containsMultiCurrencyField() && objectDescribe.containsField("base_probability_amount")) {
                countFieldDescribe.setCountFieldApiName("base_probability_amount");
            } else {
                countFieldDescribe.setCountFieldApiName("probability_amount");
            }
            countFieldDescribe.setCountType(Count.TYPE_SUM);
            countFieldDescribe.setReturnType("number");
            countFieldDescribe.setDecimalPlaces(2);
            stopWatch.lap("buildResult getCountValue begin");
            obj = metaDataComputeService.getCountValue(controllerContext.getUser(), countFieldDescribe, SearchUtil.copySearchTemplateQuery(countQuery));
            if (obj != null) {
                if (currency.isPresent()) {
                    newOpportunityListResult.setTotalProbabilityAmount(NewOpportunityUtil.recalculateByCurrency(new BigDecimal(obj.toString()), currency.get()));
                } else {
                    newOpportunityListResult.setTotalProbabilityAmount(new BigDecimal(obj.toString()));
                }
            } else {
                log.warn("probability_amount is null");
            }
            stopWatch.lap("buildResult getCountValue end");
        }

        return newOpportunityListResult;
    }

}

@Data
@EqualsAndHashCode(callSuper = true)
class NewOpportunityListResult extends BaseListController.Result {
    @JSONField(name = "M10")
    private BigDecimal totalAmount;
    @JSONField(name = "M11")
    private BigDecimal totalProbabilityAmount;
}


