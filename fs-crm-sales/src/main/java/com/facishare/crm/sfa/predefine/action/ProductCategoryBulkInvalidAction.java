package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.util.Safes;
import com.facishare.paas.metadata.api.DBRecord;
import com.google.common.collect.Lists;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.ProductCategoryBizService;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel;
import com.facishare.crm.sfa.predefine.service.treepath.TreePathMetadataService;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryV2Validator;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/13 10:31
 */
public class ProductCategoryBulkInvalidAction extends StandardBulkInvalidAction {
    private final ProductCategoryV2Validator productCategoryV2Validator = SpringUtil.getContext().getBean(ProductCategoryV2Validator.class);
    private final TreePathMetadataService treePathMetadataService = SpringUtil.getContext().getBean(TreePathMetadataService.class);
    private final MetaDataFindServiceExt metaDataFindServiceExt = SpringUtil.getContext().getBean(MetaDataFindServiceExt.class);
    private final ProductCategoryBizService productCategoryBizService = SpringUtil.getContext().getBean(ProductCategoryBizService.class);

    /**
     * 需要被删除的数据
     */
    private List<IObjectData> deletedDataList = Lists.newArrayList();

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        SearchTemplateQuery query = treePathMetadataService.getTreePathSearchTemplateQuery(arg.getDataIds(), ProductCategoryModel.Filed.PRODUCT_CATEGORY_PATH);
        metaDataFindServiceExt.dealDataByTemplate(actionContext.getUser(), ProductCategoryModel.Metadata.API_NAME, query, objectDataList -> {
            deletedDataList.addAll(objectDataList);
            productCategoryV2Validator.productCategoryDeletedValidator(actionContext.getUser(), objectDataList);
        });
    }

    @Override
    protected Result doAct(Arg arg) {
        Result result = super.doAct(arg);


        Set<String> deletedIds = deletedDataList.stream().map(DBRecord::getId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(deletedDataList)) {
            serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(deletedDataList, actionContext.getUser());
        }
        productCategoryBizService.updateParentCategoryHasChildrenField(actionContext.getUser(), dataList, deletedIds);
        return result;
    }
}
