package com.facishare.crm.sfa.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.platform.annotation.Convertible;
import com.facishare.crm.platform.annotation.Mappable;
import com.facishare.crm.sfa.model.ProductCategoryTree;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ProductCategoryObject {

    String API_NAME = "ProductCategoryObj";

    String ORDER_FIELD = "order_field";
    String CODE = "code";
    String PID = "pid";
    String NAME = "name";
    String CATEGORY_CODE = "category_code";
    String CATEGORY = "category";
    String CATEGORY_IMAGE = "category_image";
    String _ID = "_id";

    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("code")
        String code;

        @JSONField(name = "M2")
        @JsonProperty("id")
        String id;
    }

    @Data
    class GrayMqArg {
        String tenantId;
    }

    @Getter
    @Setter
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class TreeArg {
        @Builder.Default
        private Boolean filterByShopCategory = Boolean.FALSE;
    }

    @Data
    class CheckChildArg {
        List<String> objectIdList;
    }

    @Data
    class BulkAddArg {
        private List<ProductCategoryTree> categoryList;
    }

    @Data
    class SearchArg {
        @JSONField(name = "field_api_name")
        @JsonProperty("field_api_name")
        private String fieldApiName;
        @JSONField(name = "field_value")
        @JsonProperty("field_values")
        private List<String> fieldValues;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class GetCategoryByPriceBookArg {
        @JSONField(name = "price_book_id")
        @JsonProperty("price_book_id")
        String priceBookId;

        @JsonProperty("account_id")
        String accountId;

        @JsonProperty("available_range_Id")
        private String availableRangeId;
        @JsonProperty("master_data")
        private ObjectDataDocument masterData;
        @JsonProperty("detail_data_list")
        private HashMap<String, List<ObjectDataDocument>> detailDataList;
        @Builder.Default
        private Boolean filterByShopCategory = Boolean.FALSE;

        @JsonProperty("ignore_product_status")
        private boolean ignoreProductStatus;
    }


    @Data
    @Builder
    class Result {
        @JSONField(name = "M1")
        private boolean result = false;
        private List<ObjectDataDocument> data;
    }

    @Data
    @Builder
    class ListResult {
        private List<ObjectDataDocument> result;
    }

    @Data
    @Builder
    class GrayCategoryResult {
        private boolean success;
        private String message;
    }

    @Data
    @Convertible
    class CategoryNode {
        @Mappable(fieldApiName = "_id")
        private String id;
        private String name;
        private String code;
        @JsonInclude(JsonInclude.Include.ALWAYS)
        private String pid;
        @JsonInclude(JsonInclude.Include.ALWAYS)
        private Boolean hasChildren;
        private List<CategoryNode> children;
    }

    @Data
    @JsonInclude(JsonInclude.Include.ALWAYS)
    class CategoryLineageResult {
        private List<CategoryNode> categoryNodes;
        private List<CategoryNode> categoryTreeList;
    }

    @Data
    class CategoryLineageArg {
        private List<String> idList;
        private List<String> codeList;
//        private Boolean includeBrother;
        /**
         * tree or list
         */
        private String mode;
    }

    @Data
    class CategoryChildrenArg {
        private String parentId;
        private Boolean includeParent;
    }


    @Data
    @Builder
    class SingleResult {
        private ObjectDataDocument result;
    }

    @Data
    @Builder
    class CategoryTree {
        private List<CategoryObject> result;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ListChildrenArg {
        private List<String> codes;
        @Builder.Default
        private Boolean filterByShopCategory = false;
    }

    @Data
    class ChildCategoryArg {
        private String objectDataId;
    }

    @Data
    @Builder
    class ListChildrenResult {
        @JSONField(name = "M1")
        private Map<String, Set<String>> result;
    }

    @Data
    @Builder
    class CategoryResult {
        private String id;
        private String name;
        private String code;
        private Integer orderField;
        private String categoryCode;
        private List<CategoryResult> children;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class CategoryArg {
        private String queryInfo;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class ProductCategoryListArg {
        @JsonProperty("object_data")
        private ObjectDataDocument objectData;
        private HashMap<String, List<ObjectDataDocument>> details;
        @Builder.Default
        private Boolean filterByShopCategory = Boolean.FALSE;
        @Builder.Default
        private String filterCategory = "ALL";
        private String searchQueryInfo;
        private String relatedFieldApiName;
        private List<String> nodeConditions;
        @JsonProperty("ignore_product_status")
        private boolean ignoreProductStatus;
        private ObjectDataDocument masterDataForWhere;
        private ObjectDataDocument objectDataForWhere;
        @Builder.Default
        private Map<String, List<ObjectDataDocument>> detailsForWhere = Maps.newHashMap();
        @Builder.Default
        private Set<String> returnFields = Sets.newHashSet();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class CategoryListResult {
        public List<ObjectDataDocument> result;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class NodeInfo {
        private String id;
        private boolean isLeaf;
        private String name;
    }
}