package com.facishare.crm.sfa.predefine.service.proposal.access;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.lto.utils.Safes;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Component
@Slf4j
public class CaseOverviewSourceAccess {
    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Resource
    private ObjectDataAccess objectDataAccess;
    @Resource
    private ServiceFacade serviceFacade;

    private static final String CASE_OVERVIEW_SOURCE_OBJ = "CaseOverviewSourceObj";


    public IObjectData delete(User user, String id) {
        IObjectData data = metaDataFindServiceExt.findObjectByIdIgnoreAll(user.getTenantId(), id, CASE_OVERVIEW_SOURCE_OBJ);
        if (data == null) {
            return null;
        }
        List<IObjectData> dataList = serviceFacade.bulkDeleteDirect(Lists.newArrayList(data), user);
        return Safes.first(dataList);
    }

    public IObjectData create(User user, IObjectData objectData) {
        return objectDataAccess.create(user, objectData, CASE_OVERVIEW_SOURCE_OBJ);
    }

    public List<IObjectData> list(User user) {
        return metaDataFindServiceExt.findAllObjectIgnoreAll(user, CASE_OVERVIEW_SOURCE_OBJ);
    }

    public IObjectData queryById(User user, String id) {
        return metaDataFindServiceExt.findObjectByIdIgnoreAll(user, id, CASE_OVERVIEW_SOURCE_OBJ);
    }

    public void updateByFields(User user, IObjectData originalData, Set<String> fieldApiNames) {
        if (CollectionUtils.isEmpty(fieldApiNames)) {
            return;
        }
        objectDataAccess.updateByFields(user, originalData, fieldApiNames);
    }

    public boolean existsRelatedObjectData(User user, String id, String relatedObjectDescribe) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), "related_object_describe", relatedObjectDescribe);
        if (StringUtils.isNotBlank(id)) {
            SearchUtil.fillFilterNotEq(query.getFilters(), DBRecord.ID, id);
        }
        query.setLimit(1);
        IObjectData objectData = objectDataAccess.queryByTemplate(user, CASE_OVERVIEW_SOURCE_OBJ, query, Lists.newArrayList("_id"));
        return objectData != null;
    }

    public IObjectData queryByObject(User user, String objectApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        SearchUtil.fillFilterEq(query.getFilters(), "related_object_describe", objectApiName);
        return objectDataAccess.queryByTemplate(user, CASE_OVERVIEW_SOURCE_OBJ, query, Lists.newArrayList("_id", "related_fields", "related_object_describe"));
    }
}
