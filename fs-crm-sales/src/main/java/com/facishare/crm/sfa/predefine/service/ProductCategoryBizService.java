package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.model.ShopCategoryModel;
import com.facishare.crm.platform.AssertValidator;
import com.facishare.crm.platform.converter.Converter;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.model.CategoryImportValidateModel;
import com.facishare.crm.sfa.model.ProductCategoryTree;
import com.facishare.crm.sfa.predefine.CategoryPathAssembly;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.action.ProductCategoryInsertImportDataAction;
import com.facishare.crm.sfa.predefine.enums.CategoryFilterConditionEnum;
import com.facishare.crm.sfa.predefine.enums.CategoryFilterEnum;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.CategoryObject;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject;
import com.facishare.crm.sfa.predefine.service.treepath.impl.TreePathService;
import com.facishare.crm.sfa.prm.api.enhancer.DescribeEnhancer;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.sfa.prm.platform.utils.I18NUtils;
import com.facishare.crm.sfa.task.AsyncTaskProducer;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.proxy.SalesOrderBizProxy;
import com.facishare.crm.sfa.utilities.util.CategoryRandomTimeUtil;
import com.facishare.crm.sfa.utilities.util.LayoutUtils;
import com.facishare.crm.sfa.utilities.util.ProductCategoryUtils;
import com.facishare.crm.sfa.utilities.util.SFARestHeaderUtil;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryV2Validator;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.core.predef.controller.StandardSummaryFieldController;
import com.facishare.paas.appframework.core.util.FunctionParamBuildUtils;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ProductCategoryService;
import com.facishare.paas.appframework.metadata.dto.ProductAllCategoriesModel;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.*;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Filed.*;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Filed.CATEGORY;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Metadata.API_NAME;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Metadata.CATEGORY_CUSTOM_DISPLAY_FIELDS;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject.*;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject.CATEGORY_CODE;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject.CODE;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject.ORDER_FIELD;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject.PID;
import static com.facishare.crm.sfa.predefine.service.transfer.CrmPackageObjectConstants.*;
import static com.facishare.crm.sfa.utilities.util.ProductCategoryUtils.NEW_CATEGORY_LIMIT;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;
import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.*;
import static com.facishare.crm.sfa.utilities.util.i18n.SalesOrderI18NKeyUtil.DATA_NOT_EXIST;

/**
 * @author: sundy
 * @date: 2020/11/11 16:01
 * @description: 方法迁移
 */
@Service
@Slf4j
@Data
public class ProductCategoryBizService implements ProductCategoryBizIntService {
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private TreePathService treePathService;
    @Autowired
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Autowired
    private ProductCategoryV2Validator productCategoryV2Validator;
    @Autowired
    private AsyncTaskProducer asyncTaskProducer;
    @Autowired
    private CategoryRandomTimeUtil categoryRandomTimeUtil;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Autowired
    private ProductCategoryService productCategoryService;
    @Autowired
    private DhtPriceBookService dhtPriceBookService;
    @Autowired
    private ProductCategoryValidator productCategoryValidator;
    @Resource
    private ProductCategoryUtils productCategoryUtils;
    @Resource
    private CategoryStaticUtilService categoryStaticUtilService;
    @Resource
    private DescribeEnhancer describeEnhancer;
    @Resource
    private ServiceFacadeProxy serviceFacadeProxy;
    @Autowired
    private SalesOrderBizProxy salesOrderBizProxy;
    @Resource(name = "objectConverter")
    private Converter converter;
    @Resource
    private AssertValidator assertValidator;

    private static final Set<Operator> SUPPORT_CATEGORY_OPERATOR = Sets.newHashSet(Operator.EQ, Operator.IN, Operator.IS, Operator.N, Operator.NIN, Operator.ISN, Operator.HASANYOF, Operator.NHASANYOF);
    private static final Set<Operator> TRANSFER_IN_OPERATOR = Sets.newHashSet(Operator.EQ, Operator.IN, Operator.IS, Operator.HASANYOF);
    private static final Set<CategoryFilterConditionEnum> DEFAULT_CATEGORY_FILTER_CONDITIONS = ImmutableSet.of(CategoryFilterConditionEnum.INCLUDE_PARENT_NODE);
    private static final Set<String> PRE_CATEGORY_FIELD = ImmutableSet.of(PRODUCT_CATEGORY_ID, SHOP_CATEGORY_ID, PRODUCT_CATEGORY, SHOP_CATEGORY);


    private static final String SYNC_DESCRIBE_BIZ = "product_category_sync_describe";
    private static final String GRAY_PRODUCT_CATEGORY_OBJECT = "gray_product_category_object";
    private static final String SYNC_CATEGORY_FIELD_BIZ = "sync_category_field";
    private static final String SYNC_DESCRIBE_BIZ_DELAY = "product_category_sync_describe_delay";

    public List<ProductCategoryTree> bulkAddTreeHandler(ProductCategoryTree root, User user, ProductCategoryInsertImportDataAction.Recorder recorder) {
        if (Objects.isNull(root)) {
            return Lists.newArrayList();
        }
        Queue<ProductCategoryTree> queue = new ArrayDeque<>();
        List<ProductCategoryTree> list = new ArrayList<>();
        int rootOrderField = productCategoryUtils.getMaxOrderUnderParent(user, "") + 1;
        recorder.record(root.getPid(), rootOrderField);
        root.setOrderField(recorder.getOrderField(root.getPid()));
        root.setId(getGenerateId());
        queue.offer(root);
        while (!queue.isEmpty()) {
            ProductCategoryTree item = queue.poll();
            list.add(item);
            if (CollectionUtils.isEmpty(item.getChildren())) {
                continue;
            }
            int orderField = 1;
            String pid = item.getId();
            Set<String> nameSet = Sets.newHashSet();
            for (ProductCategoryTree child : item.getChildren()) {
                nameSet.add(child.getName());
                child.setPid(pid);
                child.setId(getGenerateId());
                child.setOrderField(orderField++);
                queue.offer(child);
            }
            if (nameSet.size() != item.getChildren().size()) {
                throw new ValidateException(categoryStaticUtilService.text(SO_CHECK_CATEGORY_SAME_NAME));
            }
        }
        return list;
    }

    public String getGenerateId() {
        return serviceFacadeProxy.generateId();
    }

    public List<ProductCategoryTree> importTreeHandler(ProductCategoryTree root, CategoryImportValidateModel.CategoryGlobalVariable globalVariable, ProductCategoryInsertImportDataAction.Recorder recorder) {
        Queue<ProductCategoryTree> queue = new ArrayDeque<>();
        List<ProductCategoryTree> list = new ArrayList<>();
        root.setId(getGenerateId());
        String dataPid = Optional.ofNullable(root.getPid()).orElse("");
        int rootOrderField = productCategoryUtils.getMaxOrderUnderParent(dataPid, globalVariable.getProductCategoryDataFromDB()) + 1;
        recorder.record(root.getPid(), rootOrderField);
        root.setOrderField(recorder.getOrderField(root.getPid()));
        queue.offer(root);
        while (!queue.isEmpty()) {
            ProductCategoryTree item = queue.poll();
            list.add(item);
            if (CollectionUtils.isEmpty(item.getChildren())) {
                continue;
            }
            int orderField = 1;
            String pid = item.getId();
            for (ProductCategoryTree child : item.getChildren()) {
                child.setPid(pid);
                child.setId(getGenerateId());
                child.setOrderField(orderField++);
                queue.offer(child);
            }
        }
        return list;
    }

    public void handleCategoryForCPQ(ServiceContext context, List<IObjectData> categoryDataList, CategoryObject categoryTreeList, String categoryByPriceBookIdSql, Boolean filterByShopCategory) {
        StopWatch stopWatch = StopWatch.create("handleCategoryForCPQ");
        Set<String> categoryCodes;
        log.info("treeListByPriceBookId: SQL:{}", categoryByPriceBookIdSql);
        try {
            categoryCodes = findUsedCategorySet(context.getTenantId(), categoryByPriceBookIdSql, filterByShopCategory, categoryDataList);
        } catch (MetadataServiceException e) {
            log.error("treeListByPriceBookId error:", e);
            throw new APPException("system error.");
        }
        stopWatch.lap("findBySql");
        remove(new Stack<>(), categoryTreeList, categoryCodes);
        stopWatch.lap("remove");
        stopWatch.log();
    }

    public Set<String> findUsedCategorySet(String tenantId, String sql, Boolean filterByShopCategory, List<IObjectData> categoryDataList) throws MetadataServiceException {
        List<Map> mapList = findBySql(tenantId, sql);
        if (mapList == null || mapList.isEmpty()) {
            return Sets.newHashSet();
        }
        return Boolean.TRUE.equals(filterByShopCategory) ?
                convertShopCategory(mapList, categoryDataList) :
                convertProductCategory(mapList);
    }

    private Set<String> convertShopCategory(List<Map> mapList, List<IObjectData> categoryDataList) {
        return mapList.stream()
                .filter(Objects::nonNull)
                .flatMap(map -> convertShopCategory(map, categoryDataList).stream())
                .collect(Collectors.toSet());
    }

    private Set<String> convertShopCategory(Map map, List<IObjectData> categoryDataList) {
        Object arrayObject = map.get(SHOP_CATEGORY_ID);
        if (arrayObject == null) {
            return Sets.newHashSet();
        }
        List<String> categoryIds = Lists.newArrayList((String[]) arrayObject);
        return categoryDataList
                .stream()
                .filter(d -> categoryIds.contains(d.getId()))
                .map(x -> x.get(CODE, String.class))
                .collect(Collectors.toSet());
    }

    public Set<String> convertProductCategory(List<Map> mapList) {
        return mapList.stream()
                .filter(Objects::nonNull)
                .flatMap(map -> convertProductCategory(map).stream())
                .collect(Collectors.toSet());
    }

    public Set<String> convertProductCategory(Map map) {
        if (map == null) {
            return Sets.newHashSet();
        }
        Object category = map.get(ProductCategoryObject.CATEGORY);
        if (category == null) {
            return Sets.newHashSet();
        }
        return Sets.newHashSet(String.valueOf(category));
    }

    public String handleSqlForCPQ(String sql, User user) {
        if (bizConfigThreadLocalCacheService.isCPQEnabled(user.getTenantId())) {
            return sql.replaceAll("bp.product_status = '1'", "bp.product_status = '1' and bp.is_saleable='true'");
        }
        return sql;
    }

    private List<Map> findBySql(String tenantId, String querySql) throws MetadataServiceException {
        String sql = handleSqlForCPQ(querySql, new User(tenantId, "-10000"));
        log.info("DhtPriceBookServiceCopy handleSqlForCPQ:tenantId:{},sql:{}", tenantId, sql);
        return objectDataService.findBySql(tenantId, querySql);
    }

    /**
     * 将 code 属于 codes 中的分类从 categoryObject 中移除
     *
     * @param auxiliaryStack
     * @param categoryObject
     * @param codes
     */
    public void remove(Stack<String> auxiliaryStack, CategoryObject categoryObject, Set<String> codes) {
        if (!codes.contains(categoryObject.getCode()) && categoryObject.getCode() != null) {
            auxiliaryStack.push(categoryObject.getCode());
        } else {
            auxiliaryStack.clear();
        }
        List<CategoryObject> children = categoryObject.getChildren();
        if (CollectionUtils.isNotEmpty(children)) {
            Iterator<CategoryObject> iterator = children.iterator();
            while (iterator.hasNext()) {
                CategoryObject nextObject = iterator.next();
                remove(auxiliaryStack, nextObject, codes);
                if (!auxiliaryStack.isEmpty()) {
                    String peek = auxiliaryStack.peek();
                    if (nextObject.getCode().equals(peek)) {
                        iterator.remove();
                        auxiliaryStack.pop();
                    }
                }
            }
        }
    }

    public List<IObjectData> bulkAddDataFill(User user, List<ProductCategoryTree> result, Map<String, String> categoryPidMap) {
        List<IObjectData> dataList = Lists.newArrayList();
        int code = 0;
        if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            code = Integer.parseInt(productCategoryUtils.findMaxCode(user)) + 1;
        }
        for (ProductCategoryTree d : result) {
            Map<String, Object> data = Maps.newHashMap();
            data.put(DBRecord.ID, d.getId());
            data.put(NAME, d.getName());
            data.put(ProductCategoryModel.Filed.ORDER_FIELD, String.valueOf(d.getOrderField()));
            ObjectDataDocument document = ObjectDataDocument.of(data);
            document.put(FIELD_DESCRIBE_API_NAME, API_NAME);
            document.put(FIELD_DESCRIBE_ID, ProductCategoryUtils.VIRTUAL_ID);
            IObjectData objectData = document.toObjectData();
            objectData.setTenantId(user.getTenantId());
            if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
                objectData.set(CODE, String.valueOf(code++));
            } else {
                objectData.set(CODE, getRandomId());
            }
            objectData.set(CATEGORY_CODE, StringUtils.isEmpty(d.getCategoryCode()) ? getRandomId() : d.getCategoryCode());
            categoryPidMap.put(data.get(CATEGORY_CODE).toString(), d.getPid());
            objectData.set("category_image", d.getCategoryImage());
            dataList.add(objectData);
        }
        return dataList;
    }

    public ProductCategoryObject.CategoryListResult categoryNodeList(User user, SearchTemplateQuery searchTemplateQuery) {
        QueryResult<IObjectData> queryResult = serviceFacadeProxy.findBySearchQuery(user, SFAPreDefineObject.ProductCategory.getApiName(), searchTemplateQuery);
        if (queryResult == null || com.facishare.paas.appframework.common.util.CollectionUtils.size(queryResult.getData()) == 0) {
            return CategoryListResult.builder().result(ObjectDataDocument.ofList(Lists.newArrayList())).build();
        }
        List<IObjectData> categoryDataList = queryResult.getData();
        // 提取搜索结果中的分类ids 并且使用set 过滤
        Set<String> categoryIds = categoryDataList.stream().map(IObjectData::getId).collect(Collectors.toSet());
        categoryDataList.forEach(category -> {
            // 分类路径
            String path = category.get(PRODUCT_CATEGORY_PATH, String.class);
            if (StringUtils.isNotEmpty(path)) {
                categoryIds.addAll(Lists.newArrayList(path.split("\\.")));
            }
        });
        List<IObjectData> parentCategoryList = Lists.newArrayList();
        if (com.facishare.paas.appframework.common.util.CollectionUtils.size(categoryIds) > 0) {
            parentCategoryList = serviceFacadeProxy.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(categoryIds), SFAPreDefineObject.ProductCategory.getApiName());
        }
        Map<String, String> categoryMap = parentCategoryList.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName));
        categoryDataList.forEach(category -> {
            String path = category.get(PRODUCT_CATEGORY_PATH, String.class);
            List<String> pathCategoryIds = Lists.newArrayList();
            List<String> pathCategoryNames = Lists.newArrayList();
            if (StringUtils.isNotEmpty(path)) {
                pathCategoryIds = Stream.of(path.split("\\.")).collect(Collectors.toList());
                pathCategoryNames = pathCategoryIds.stream().map(x -> StringUtils.isNotEmpty(categoryMap.get(x)) ? categoryMap.get(x) : null).collect(Collectors.toList());
            }
            category.set(PRODUCT_CATEGORY_PATH_NAME, pathCategoryNames);
            category.set(PRODUCT_CATEGORY_PATH_ID, pathCategoryIds);
        });
        return CategoryListResult.builder().result(ObjectDataDocument.ofList(categoryDataList)).build();
    }

    public BaseListController.Result handleCategoryName(User user, BaseListController.Result result, String categoryFieldApiName, String displayApiName) {
        if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            return result;
        }
        List<IObjectData> dataList = result.getDataList().stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dataList)) {
            return result;
        }
        List<IObjectData> handledDataList = handleCategoryName(user, dataList, categoryFieldApiName, displayApiName);
        result.setDataList(ObjectDataDocument.ofList(handledDataList));
        return result;
    }

    public void handleCategoryMappingCategoryId(User user, IObjectData originalData, IObjectData argData, String apiName) {
        if (originalData == null || argData == null || StringUtils.isBlank(apiName)) {
            log.warn("handleCategoryMappingCategoryId method, param is null, tenant:{}, apiName:{}",
                    user.getTenantId(), apiName);
            return;
        }
        if (!productCategoryV2Validator.checkCategoryFieldChanged(originalData, argData)) {
            return;
        }
        handleCategoryMappingCategoryId(user, argData, apiName);
    }

    /**
     * product_category_id 是后加的字段，之前都是存储的category，这个兼容是为了其他调用本方法的请求，防止只传了category而没有product_category_id的情况
     * 补充category 与 product_category_id
     * product_category_id 为最高优先级，其为空，则根据category 反填product_category_id
     *
     * @param user    用户
     * @param argData 数据arg
     * @param apiName 对象 apiName
     */
    public void handleCategoryMappingCategoryId(User user, IObjectData argData, String apiName) {
        if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            return;
        }
        IObjectDescribe objectDescribe = describeEnhancer.fetchObject(user, apiName);
        if (objectDescribe == null) {
            return;
        }
        if (objectDescribe.getFieldDescribe(CATEGORY) == null) {
            return;
        }
        if (getCategoryOptionSize(objectDescribe) == 0) {
            return;
        }
        SelectOneFieldDescribe category = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe(CATEGORY);
        List<ISelectOption> selectOptions = category.getSelectOptions();
        handleCategoryMappingCategoryId(user, argData, selectOptions);
    }

    public int getCategoryOptionSize(IObjectDescribe objectDescribe) {
        if (objectDescribe == null) {
            return 0;
        }
        if (objectDescribe.getFieldDescribe(CATEGORY) == null) {
            return 0;
        }
        SelectOneFieldDescribe category = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe(CATEGORY);
        return category.getSelectOptions().size();
    }

    public void handleCategoryMappingCategoryId(User user, IObjectData data, List<ISelectOption> selectOptions) {
        String productCategoryId = getDataFieldValue(data, PRODUCT_CATEGORY_ID, "");
        String category = getDataFieldValue(data, CATEGORY, "");
        if (StringUtils.isBlank(category) && StringUtils.isBlank(productCategoryId)) {
            return;
        }
        if (StringUtils.isBlank(productCategoryId)) {
            log.warn("handleCategoryMappingCategoryId, product_category_id is null, tenant:{}", user.getTenantId());
            String categoryId = productCategoryUtils.getCategoryIdByCode(user, category);
            data.set(PRODUCT_CATEGORY_ID, categoryId);
        } else {
            String code = productCategoryUtils.getCodeById(user, productCategoryId);
            Optional<ISelectOption> any = selectOptions.stream().filter(s -> code.equals(s.getValue())).findAny();
            if (any.isPresent() && !Objects.equals(code, data.get(CATEGORY))) {
                data.set(CATEGORY, code);
            } else if (!any.isPresent() && !Objects.equals(code, data.get(CATEGORY))) {
                asyncBackCategoryField(user, data.getId(), data.getDescribeApiName());
            }
        }
    }

    public String getDataFieldValue(IObjectData data, String fieldName, String defaultValue) {
        return ObjectDataUtils.getValueOrDefault(data, fieldName, defaultValue);
    }

    public void asyncBackCategoryField(User user, String dataId, String apiName) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId", user.getTenantId());
        jsonObject.put("dataId", dataId);
        jsonObject.put("objectApiName", apiName);
        asyncTaskProducer.create(SYNC_CATEGORY_FIELD_BIZ, jsonObject.toJSONString(), null, 4);
    }

    public Set<String> handleCategoryMappingCategoryIdOfImport(User user, List<IObjectData> dataList, String apiName) {
        Set<String> rst = Sets.newHashSet();
        if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            return rst;
        }
        IObjectDescribe objectDescribe = describeEnhancer.fetchObject(user, apiName);
        if (objectDescribe == null) {
            return rst;
        }
        if (objectDescribe.getFieldDescribe("category") == null) {
            return rst;
        }
        if (getCategoryOptionSize(objectDescribe) == 0) {
            return rst;
        }
        SelectOneFieldDescribe category = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe("category");
        List<ISelectOption> selectOptions = category.getSelectOptions();
        // product_category_id 有值
        Set<String> categoryIds = dataList.stream().map(x -> getDataFieldValue(x, PRODUCT_CATEGORY_ID, "")).collect(Collectors.toSet());
        categoryIds.remove("");
        Map<String, String> categoryIdMapCode = productCategoryUtils.getCodeByCategoryIds(user, Lists.newArrayList(categoryIds));
        for (IObjectData data : dataList) {
            String categoryId = getDataFieldValue(data, PRODUCT_CATEGORY_ID, "");
            String code = categoryIdMapCode.get(categoryId);
            if (StringUtils.isNotBlank(code)) {
                selectOptions.stream().filter(s -> code.equals(s.getValue())).findAny().ifPresent(v -> data.set(CATEGORY, code));
            } else {
                log.warn("handleCategoryMappingCategoryIdOfImport can't find code, tenant:{}, dataId:{}, productCategoryId:{}", user.getTenantId(), data.getId(), categoryId);
                rst.add(data.getId());
            }
        }
        return rst;
    }

    public AbstractStandardDetailController.Result handleCategoryName(User user, AbstractStandardDetailController.Result result, String categoryFieldApiName, String displayApiName) {
        if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            return result;
        }
        IObjectData data = result.getData().toObjectData();
        List<IObjectData> handledData = handleCategoryName(user, Lists.newArrayList(data), categoryFieldApiName, displayApiName);
        if (CollectionUtils.isNotEmpty(handledData)) {
            result.setData(ObjectDataDocument.of(handledData.get(0)));
        }
        return result;
    }

    public void removeLayoutCategoryField(User user, ILayout layout) {
        if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            return;
        }
        if (layout == null) {
            return;
        }
        LayoutExt layoutExt = LayoutExt.of(layout);
        Optional<FormComponentExt> formComponentExtOptional = layoutExt.getFormComponent();
        formComponentExtOptional.ifPresent(o -> {
            Set<String> removeFieldList = Sets.newHashSet("category");
            LayoutUtils.removeFormComponentFields(o, removeFieldList);
        });
    }

    public void removeListHeaderCategoryField(User user, StandardListHeaderController.Result after) {
        if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            return;
        }
        Set<String> removeFieldList = Sets.newHashSet("category");
        LayoutExt.of(after.getLayout().toLayout()).getFormComponent().ifPresent(o -> o.removeFields(removeFieldList));
        List<DocumentBaseEntity> fieldList = after.getFieldList();
        if (CollectionUtils.isNotEmpty(fieldList)) {
            fieldList.removeIf(x -> x.get("category") != null);
        }
        List<String> visibleFields = after.getVisibleFields();
        if (CollectionUtils.isNotEmpty(visibleFields)) {
            visibleFields.remove("category");
        }
        List<QueryTemplateDocument> templates = after.getTemplates();
        if (CollectionUtils.isEmpty(templates)) {
            return;
        }
        templates.forEach(template -> {
            ISearchTemplate iSearchTemplate = template.toSearchTemplate();
            if (iSearchTemplate == null) {
                return;
            }
            List<Map> fields = iSearchTemplate.getFieldList();
            if (CollectionUtils.isEmpty(fields)) {
                return;
            }
            fields.removeIf(x -> CATEGORY.equals(x.get("field_name")));
        });
    }

    public void removeFieldsOfSpuSkuImport(User user, List<IFieldDescribe> pendingFieldList) {
        if (productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            pendingFieldList.removeIf(x -> CATEGORY.equals(x.getApiName()));
        } else {
            pendingFieldList.removeIf(x -> PRODUCT_CATEGORY_ID.equals(x.getApiName()));
        }
    }


    /**
     * 将数据根据其 categoryFieldApiName 字段的数据所代表的节点拼接其树路径名字
     * A
     * / \
     * B   C
     * B的名字：A/B
     * C的名字：A/C
     * A的名字：A
     *
     * @param user                 用户
     * @param dataList             待处理路径的数据
     * @param categoryFieldApiName 节点字段apiName
     * @param displayApiName       设置路径名字的字段apiName
     * @return 返回处理完的数据
     */
    public List<IObjectData> handleCategoryName(User user, List<IObjectData> dataList, String categoryFieldApiName, String displayApiName) {
        StopWatch stopWatch = StopWatch.create("handleCategoryName");
        Map<String, String> currentCategoryMap = getCurrentCategoryMapping(user, dataList, categoryFieldApiName);
        stopWatch.lap("find currentCategoryMap");
        if (currentCategoryMap.isEmpty()) {
            log.warn("ProductCategoryBizService#handleCategoryNames currentCategoryMap isEmpty, tenant:{}", user.getTenantId());
            return dataList;
        }
        Set<String> pathIds = parsePaths(currentCategoryMap);
        if (CollectionUtils.isEmpty(pathIds)) {
            log.warn("ProductCategoryBizService#handleCategoryNames pathIds isEmpty, tenant:{}", user.getTenantId());
            return dataList;
        }
        List<IObjectData> categoryDataList = getCategoryDataByPathIds(user, pathIds);
        stopWatch.lap("find categoryDataList");
        if (CollectionUtils.isEmpty(categoryDataList)) {
            return dataList;
        }
        Map<String, IObjectData> productCategoryIdMapping = categoryDataList
                .stream()
                .collect(Collectors.toMap(
                        DBRecord::getId,
                        d -> d,
                        (k1, k2) -> k1));
        dataList.forEach(data -> settingDataCategoryName(categoryFieldApiName, displayApiName, data, currentCategoryMap, productCategoryIdMapping));
        stopWatch.lap("setting path display name end");
        stopWatch.logSlow(1000);
        return dataList;
    }

    private void settingDataCategoryName(String categoryFieldApiName, String displayApiName, IObjectData data, Map<String, String> currentCategoryMap, Map<String, IObjectData> productCategoryIdMapping) {
        StringBuilder categoryNameSB = categoryPathNameAssembly(categoryFieldApiName, data, currentCategoryMap, productCategoryIdMapping);
        if (categoryNameSB == null || categoryNameSB.length() == 0) {
            return;
        }
        data.set(displayApiName, categoryNameSB.toString());
    }

    private StringBuilder categoryPathNameAssembly(String categoryFieldApiName, IObjectData data, Map<String, String> currentCategoryMap, Map<String, IObjectData> productCategoryIdMapping) {
        String productCategoryId = getDataFieldValue(data, categoryFieldApiName, "");
        if (StringUtils.isBlank(productCategoryId)) {
            return null;
        }
        String categoryPath = currentCategoryMap.get(productCategoryId);
        if (StringUtils.isBlank(categoryPath)) {
            return null;
        }
        String[] split = categoryPath.split("\\.");
        StringBuilder categoryName = new StringBuilder();
        for (String categoryId : split) {
            IObjectData categoryData = productCategoryIdMapping.get(categoryId);
            if (categoryData == null) {
                categoryName.delete(0, categoryName.length());
                log.warn("categoryPathNameAssembly but categoryData == null tenant:{}, categoryId:{}", data.getTenantId(), categoryId);
                break;
            }
            String i18nName = I18NUtils.getDataI18nFieldValue(categoryData, ObjectData.NAME, String.class);
            categoryName.append(i18nName).append("/");
        }
        // 去掉最后一个 /
        if (categoryName.toString().endsWith("/")) {
            categoryName.deleteCharAt(categoryName.length() - 1);
        }
        return categoryName;
    }

    public List<IObjectData> getCategoryDataByPathIds(User user, Set<String> pathIds) {
        if (CollectionUtils.isEmpty(pathIds)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(0);
        Filter filter = new Filter();
        filter.setFieldName(DBRecord.ID);
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList(pathIds));
        searchTemplateQuery.setFilters(Lists.newArrayList(filter));
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, API_NAME, searchTemplateQuery).getData();
    }

    public Set<String> parsePaths(Map<String, String> currentCategoryMap) {
        Set<String> pathIds = Sets.newHashSet();
        for (String path : currentCategoryMap.values()) {
            String[] split = path.split("\\.");
            pathIds.addAll(Sets.newHashSet(split));
        }
        pathIds.remove("");
        return pathIds;
    }

    public Map<String, String> getCurrentCategoryMapping(User user, List<IObjectData> dataList, String categoryFieldApiName) {
        // categoryFieldApiName 是 _id 证明是处理产品分类List的数据，不需要再查
        if (DBRecord.ID.equals(categoryFieldApiName)) {
            return dataList.stream().filter(d -> StringUtils.isNotBlank(d.getId())).collect(Collectors.toMap(DBRecord::getId, d -> getDataFieldValue(d, PRODUCT_CATEGORY_PATH, "")));
        }
        Set<String> productCategoryIds = dataList.stream().filter(d -> StringUtils.isNotBlank(getDataFieldValue(d, categoryFieldApiName, ""))).map(d -> d.get(categoryFieldApiName).toString()).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(productCategoryIds)) {
            return Maps.newHashMap();
        }
        return treePathService.getCurrentPathByIds(user, Lists.newArrayList(productCategoryIds), API_NAME, PRODUCT_CATEGORY_PATH);
    }

    public void initPathField(User user, IObjectData objectData) {
        treePathService.initDataPath(user, objectData, API_NAME, PRODUCT_CATEGORY_PATH, PID);
    }

    public void changePathByParentId(User user, String objectId, String parentId) {
        treePathService.changePathByParentId(user, objectId, parentId, API_NAME, PRODUCT_CATEGORY_PATH);
    }

    public void bulkUpdateOrderField(User user, String orderField, Object pid, String dataId) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(0);
        List<IFilter> filters = searchQuery.getFilters();
        if (StringUtils.isEmpty((String) pid)) {
            filters.add(SearchUtil.filter(PID, Operator.IS, Lists.newArrayList()));
        } else {
            SearchUtil.fillFilterEq(filters, PID, pid);
        }
        filters.add(SearchUtil.filter(ORDER_FIELD, Operator.GTE, Lists.newArrayList(orderField)));
        SearchUtil.fillFilterNotEq(filters, DBRecord.ID, dataId);
        searchQuery.setOrders(Lists.newArrayList(new OrderBy(ORDER_FIELD, true)));
        QueryResult<IObjectData> result = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, ProductCategoryObject.API_NAME, searchQuery);
        List<IObjectData> data = result.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            //例如order是1 库里大于等于1的有3、4、5，这样就不需要更新后面的数据
            if (data.stream().anyMatch(o -> orderField.equals(o.get(ORDER_FIELD).toString()))) {
                int increment = Integer.parseInt(orderField);
                for (IObjectData datum : data) {
                    datum.set(ORDER_FIELD, ++increment);
                }
                serviceFacadeProxy.batchUpdateByFields(user, data, Lists.newArrayList(ORDER_FIELD));
            }
        }
    }

    public void sendSynchronizeDescribeMqByGray(User user) {
        sendSynchronizeDescribeMqByGray(user, "");
    }

    public void sendSynchronizeDescribeMqByGray(User user, String dataFrom) {
        if (getCategoryOptionSize(user) >= NEW_CATEGORY_LIMIT) {
            return;
        }
        if ("add_to_db".equals(dataFrom)) {
            return;
        }
        sendSynchronizeDescribeMq(user);
    }

    public int getCategoryOptionSize(User user) {
        IObjectDescribe describe = describeEnhancer.fetchObject(user, API_NAME);
        return getCategoryOptionSize(describe);
    }

    public void sendSynchronizeDescribeMq(User user) {
        if (categoryStaticUtilService.isStopSyncCategoryTenant(user.getTenantId())) {
            return;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId", user.getTenantId());
        // 分队列没有重试
        asyncTaskProducer.create(SYNC_DESCRIBE_BIZ, jsonObject.toJSONString(), Integer.valueOf(user.getTenantId()));
    }

    /**
     * 返回20位以内的随机数字，根据时间戳与随机数构造
     * 冲突概率较大，但是本方法只用于产品分类对象的业务字段生成id，数据库根据企业id与code生成唯一所以，并发概率不大，符合业务
     *
     * @return 随机id
     */
    public String getRandomId() {
        return categoryRandomTimeUtil.nextId();
    }

    public List<IObjectData> fillPathWithTheseDataListOfImport(User user, List<IObjectData> objectDataList) {
        return treePathService.fillPathWithTheseDataListNoUpdate(user, objectDataList, PRODUCT_CATEGORY_PATH, PID);
    }

    public void initCodeField(User user, IObjectData objectData) {
        String code;
        if (productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            String dataCode = getDataFieldValue(objectData, "code", "");
            if (StringUtils.isBlank(dataCode)) {
                code = getRandomId();
            } else {
                checkCode(dataCode);
                code = dataCode;
            }
        } else {
            code = String.valueOf(Integer.parseInt(productCategoryUtils.findMaxCode(user)) + 1);
        }
        objectData.set(ProductCategoryModel.Filed.CODE, code);
    }

    public void checkCode(String dataCode) {
        if (!CategoryRandomTimeUtil.checkRandomParam(dataCode)) {
            throw new ValidateException(categoryStaticUtilService.text(SO_CODE_STYLE_ERROR));
        }
    }

    public boolean havaChildNode(User user, List<String> pidList) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(1);
        List<IFilter> filters = searchQuery.getFilters();
        SearchUtil.fillFilterIn(filters, PID, pidList);
        List<IObjectData> data = metaDataFindServiceExt.findBySearchQuery(user, API_NAME, searchQuery).getData();
        return CollectionUtils.isNotEmpty(data);
    }

    /**
     * 新建的分类全部放到最后一个
     *
     * @param user       用户
     * @param objectData 新建数据参数
     */
    public void initOrderField(User user, IObjectData objectData) {
        String pid = getDataFieldValue(objectData, PID, "");
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = query.getFilters();
        if (StringUtils.isBlank(pid)) {
            SearchUtil.fillFilterIsNull(filters, PID);
        } else {
            SearchUtil.fillFilterEq(filters, PID, Lists.newArrayList(pid));
        }
        query.setOrders(Lists.newArrayList(new OrderBy(ORDER_FIELD, false)));
        query.setLimit(1);
        List<IObjectData> data = serviceFacadeProxy.findBySearchQuery(user, API_NAME, query).getData();
        int orderField = 1;
        if (CollectionUtils.isNotEmpty(data)) {
            String temp = getDataFieldValue(data.get(0), ORDER_FIELD, "1");
            orderField = Integer.parseInt(temp) + 1;
        }
        objectData.set(ProductCategoryModel.Filed.ORDER_FIELD, orderField);
    }

    public void fillPidAndPath(User user, Set<String> needSearchCategoryCodeList, Map<String, String> categoryPidMap) {
        Set<String> tempSet = Sets.newHashSet();
        tempSet.addAll(needSearchCategoryCodeList);
        tempSet.addAll(categoryPidMap.keySet());
        List<IObjectData> objectDataList = productCategoryUtils.findObjectByCategoryCode(user, tempSet, tempSet.size());
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        for (IObjectData data : objectDataList) {
            String categoryCode = data.get(CATEGORY_CODE).toString();
            String pid = categoryPidMap.get(categoryCode);
            if (pid != null) {
                data.set(PID, pid);
            }
        }
        if (productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            List<IObjectData> dataList = fillPathWithTheseDataListOfImport(user, objectDataList);
            List<IObjectData> needUpdateDataList = dataList.stream().filter(d -> !needSearchCategoryCodeList.contains(d.get(CATEGORY_CODE).toString())).collect(Collectors.toList());
            metaDataFindServiceExt.bulkUpdateByFields(user, needUpdateDataList, Lists.newArrayList(PID, PRODUCT_CATEGORY_PATH));
        } else {
            List<IObjectData> needUpdateDataList = objectDataList.stream().filter(d -> !needSearchCategoryCodeList.contains(d.get(CATEGORY_CODE).toString())).collect(Collectors.toList());
            metaDataFindServiceExt.bulkUpdateByFields(user, needUpdateDataList, Lists.newArrayList(PID));
        }
    }

    /**
     * 对老分类企业过来的请求补充必填字段
     */
    public void fillOldCategoryMustField(ObjectDataDocument objectDataDocument) {
        String recordType = objectDataDocument.toObjectData().getRecordType();
        if (StringUtils.isBlank(recordType)) {
            objectDataDocument.toObjectData().setRecordType("default__c");
        }
    }

    private List<ISelectOption> convert(CategoryObject categoryObject) {
        List<ISelectOption> selectOptionList = Lists.newArrayList();
        List<CategoryObject> children = categoryObject.getChildren();
        if (CollectionUtils.isNotEmpty(children)) {
            for (CategoryObject child : children) {
                dealCurrentLevel("", selectOptionList, child);
            }
        }
        //循环一遍 selectOneList，将Label结尾有"/"的删除掉。
        for (ISelectOption selectOption : selectOptionList) {
            if (selectOption.getLabel().endsWith("/")) {
                selectOption.setLabel(selectOption.getLabel().substring(0, selectOption.getLabel().length() - 1));
            }
        }
        return selectOptionList;
    }

    private void dealCurrentLevel(String itemNameOld, List<ISelectOption> selectOptionList, CategoryObject categoryObject) {
        String itemName = categoryObject.getName();
        itemName = itemNameOld + itemName + "/";
        List<CategoryObject> children = categoryObject.getChildren();
        if (CollectionUtils.isNotEmpty(children)) {
            for (CategoryObject child : children) {
                dealCurrentLevel(itemName, selectOptionList, child);
            }
        }
        String itemCode = categoryObject.getCode();
        SelectOption selectOption = new SelectOption(itemName, itemCode, "");
        selectOptionList.add(selectOption);
    }

    public void handleNewCategoryFilters(User user, List<IFilter> filters, String transferField, String shopField) {
        filterShopCategory(user, filters, shopField);
        filterProductCategory(user, filters, transferField);
    }

    public void filterShopCategory(User user, List<IFilter> filters, String shopField) {
        if (StringUtils.isBlank(shopField)) {
            shopField = SHOP_CATEGORY_ID;
        }
        String finalShopField = shopField;
        filters.stream().filter(f -> (SHOP_CATEGORY_ID.equals(f.getFieldName()) || finalShopField.equals(f.getFieldName())) && Operator.HASANYOF.equals(f.getOperator())).findAny().ifPresent(filter -> {
            List<String> fieldValues = Optional.ofNullable(filter.getFieldValues())
                    .orElse(Lists.newArrayList())
                    .stream()
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(fieldValues)) {
                return;
            }
            List<IObjectData> children = treePathService.getChildren(user, PRODUCT_CATEGORY_PATH, fieldValues);
            Set<String> categoryIdSet = children.stream().map(IObjectData::getId).collect(Collectors.toSet());
            categoryIdSet.addAll(fieldValues);
            filter.setOperator(Operator.HASANYOF);
            filter.setFieldValues(Lists.newArrayList(categoryIdSet));
            filter.setFieldName(finalShopField);
        });
    }


    private void filterProductCategory(User user, List<IFilter> filters, String transferField) {
        if (CollectionUtils.isEmpty(filters)) {
            return;
        }
        filters.forEach(filter -> fillChildCategoryId(user, filter, transferField));
    }


    public void fillChildCategoryId(User user, IFilter filter, String transferField) {
        if (!transferField.equals(filter.getFieldName()) || !SUPPORT_CATEGORY_OPERATOR.contains(filter.getOperator())) {
            return;
        }
        List<String> fieldValues = Optional.ofNullable(filter.getFieldValues())
                .orElse(Lists.newArrayList())
                .stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fieldValues)) {
            return;
        }
        List<IObjectData> children = treePathService.getChildren(user, PRODUCT_CATEGORY_PATH, fieldValues);
        Set<String> categoryIdSet = children.stream().map(IObjectData::getId).collect(Collectors.toSet());
        categoryIdSet.addAll(fieldValues);
        if (TRANSFER_IN_OPERATOR.contains(filter.getOperator())) {
            filter.setOperator(Operator.IN);
        } else {
            filter.setOperator(Operator.NIN);
        }
        filter.setFieldValues(Lists.newArrayList(categoryIdSet));
    }


    @Override
    public void handleNewCategoryListFilters(User user, String argQueryStr, SearchTemplateQuery currentQueryInfo, String transferField, String shopField) {
        if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            return;
        }
        if (StringUtils.isBlank(argQueryStr)) {
            log.warn("handleNewCategoryListFilters# but argQueryStr is null, tenant:{}", user.getTenantId());
            return;
        }
        SearchTemplateQuery argQueryInfo = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(argQueryStr);
        List<IFilter> argFilters = argQueryInfo.getFilters();
        List<IFilter> currentFilters = currentQueryInfo.getFilters();
        for (IFilter filter : argFilters) {
            if (PRODUCT_CATEGORY_ID_SEARCH.equals(filter.getFieldName()) && Operator.EQ.equals(filter.getOperator())) {
                filter.setFieldName(PRODUCT_CATEGORY_ID);
                currentFilters.add(filter);
            }
        }
        handleNewCategoryFilters(user, currentQueryInfo.getFilters(), transferField, shopField);
        log.info("handleNewCategoryListFilters#currentQueryInfo tenant:{}, currentQueryInfo:{}, originalArgQueryStr:{}", user.getTenantId(), currentQueryInfo, argQueryStr);
    }

    /**
     * 如果直接传 product_category_id，web底层会自动根据名字去匹配product_category_id.name
     * 所有 web 传 product_category_id_search，此方法将其转换为 product_category_id
     *
     * @param user             用户
     * @param argQueryStr      参数中传过来的 搜索条件字符串
     * @param currentQueryInfo 底层根据描述构建的搜索条件类
     */
    @Override
    public void handleNewCategoryListFilters(User user, String argQueryStr, SearchTemplateQuery currentQueryInfo) {
        handleNewCategoryListFilters(user, argQueryStr, currentQueryInfo, PRODUCT_CATEGORY_ID, SHOP_CATEGORY_ID);
    }

    public void handleMallCategoryFilters(User user, SearchTemplateQuery searchTemplateQuery) {
        if (searchTemplateQuery == null || CollectionUtils.isEmpty(searchTemplateQuery.getFilters())) {
            return;
        }
        List<IFilter> filters = searchTemplateQuery.getFilters();
        if (filters.stream().anyMatch(filter -> ProductConstants.MALL_CATEGORY_ID.equals(filter.getFieldName()) || ProductConstants.STORE_ID.equals(filter.getFieldName()))) {
            ShopCategoryModel.HandleMallCategoryArg handleMallCategoryArg = ShopCategoryModel.HandleMallCategoryArg.builder().filters(filters).build();
            ShopCategoryModel.HandleMallCategoryResult result = salesOrderBizProxy.handleMallCategoryFilter(handleMallCategoryArg, SFARestHeaderUtil.getCrmHeader(user.getTenantId(), user));
            if (result != null && result.isSuccess() && result.getData() != null && CollectionUtils.isNotEmpty(result.getData().getFilters())) {
                searchTemplateQuery.resetFilters(result.getData().getFilters());
            } else {
                log.warn("handle_mall_category_list_filters error, tenant:{}, filters:{}", user.getTenantId(), JSON.toJSONString(filters));
            }
        }
    }

    private void fillSubMallCategoryId(User user, IFilter filter) {
        if (!ProductConstants.MALL_CATEGORY_ID.equals(filter.getFieldName()) || CollectionUtils.isEmpty(filter.getFieldValues())) {
            return;
        }
        String categoryId = filter.getFieldValues().get(0);
        if (Strings.isNullOrEmpty(categoryId)) {
            return;
        }
        ShopCategoryModel.SubCategoryArg subCategoryArg = ShopCategoryModel.SubCategoryArg.builder().pid(categoryId).build();
        ShopCategoryModel.SubCategoryResult result = salesOrderBizProxy.querySubShopCategoryList(subCategoryArg, SFARestHeaderUtil.getCrmHeader(user.getTenantId(), user));
        if (result != null && result.isSuccess() && result.getData() != null && CollectionUtils.isNotEmpty(result.getData().getShopCategoryList())) {
            filter.setFieldValues(result.getData().getShopCategoryList().stream().map(ShopCategoryModel.SubCategory::getId).collect(Collectors.toList()));
        } else {
            log.warn("query sub mall category list error, tenant:{}, categoryId:{}", user.getTenantId(), categoryId);
        }
    }

    public void oldCategoryTransferNewCategoryListFilters(User user, SearchTemplateQuery searchTemplateQuery, String transferField) {
        // 非新搜索
        if (!categoryStaticUtilService.isNewCategorySearch(user.getTenantId())) {
            categorySearchByCode(user, searchTemplateQuery);
            return;
        }
        // 新搜索
        // 没有对象化
        if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            categorySearchByCode(user, searchTemplateQuery);
            return;
        }
        // 新搜索且对象化：订货通在用
        categorySearchById(user, searchTemplateQuery, transferField);
    }

    public void categorySearchById(User user, SearchTemplateQuery searchTemplateQuery, String transferField) {
        transferFilters(user, searchTemplateQuery.getFilters(), transferField);
        List<Wheres> wheres = searchTemplateQuery.getWheres();
        if (CollectionUtils.isEmpty(wheres)) {
            return;
        }
        wheres.stream()
                .filter(k -> CollectionUtils.isNotEmpty(k.getFilters()))
                .forEach(k -> transferFilters(user, k.getFilters(), transferField));
    }

    public void transferFilters(User user, List<IFilter> filters, String transferField) {
        if (CollectionUtils.isEmpty(filters)) {
            return;
        }
        Optional<IFilter> any = filters.stream().filter(f -> CATEGORY.equals(f.getFieldName())).findAny();
        filters.forEach(filter -> {
            if (!CATEGORY.equals(filter.getFieldName()) || !SUPPORT_CATEGORY_OPERATOR.contains(filter.getOperator())) {
                return;
            }
            List<String> codes = filter.getFieldValues();
            if (CollectionUtils.isEmpty(codes)) {
                return;
            }
            Collection<String> productCategoryIds = productCategoryUtils.getCategoryIdByCodes(user, codes).values();
            if (CollectionUtils.isEmpty(productCategoryIds)) {
                log.warn("transferFilters#productCategoryIds but is empty,tenant:{},, codes:{}", user.getTenantId(), codes);
                return;
            }
            filter.setFieldName(transferField);
            if (TRANSFER_IN_OPERATOR.contains(filter.getOperator())) {
                filter.setOperator(Operator.IN);
            } else {
                filter.setOperator(Operator.NIN);
            }
            filter.setFieldValues(Lists.newArrayList(productCategoryIds));
        });
        if (any.isPresent()) {
            filterProductCategory(user, filters, transferField);
        }
    }

    public void categorySearchByCode(User user, SearchTemplateQuery searchTemplateQuery) {
        // 老逻辑
        // 处理query里面的filters
        productCategoryService.handleCategoryFilters(user.getTenantId(), user.getUpstreamOwnerIdOrUserId(), searchTemplateQuery.getFilters());
        // 处理wheres里面的分类filters
        List<Wheres> wheres = searchTemplateQuery.getWheres();
        if (CollectionUtils.isEmpty(wheres)) {
            return;
        }
        wheres.stream()
                .filter(k -> CollectionUtils.isNotEmpty(k.getFilters()))
                .forEach(k -> productCategoryService.handleCategoryFilters(user.getTenantId(), user.getUpstreamOwnerIdOrUserId(), k.getFilters()));
    }

    public void fillCategoryField(User user, List<IObjectData> actualList, Set<String> fillCategoryData, String apiName) {
        if (CollectionUtils.isEmpty(actualList) || fillCategoryData == null || fillCategoryData.isEmpty()) {
            return;
        }
        Set<String> actualData = actualList.stream().map(DBRecord::getId).collect(Collectors.toSet());
        Set<String> rstSet = Sets.newHashSet();
        rstSet.addAll(actualData);
        rstSet.retainAll(fillCategoryData);
        for (String dataId : rstSet) {
            asyncBackCategoryField(user, dataId, apiName);
        }
    }

    public Map<String, String> fillBaseFieldValue(User user, List<IObjectData> validList, List<ProductCategoryTree> categoryTreeData) {
        Map<String, String> categoryPidMapping = Maps.newHashMap();
        AtomicInteger code = new AtomicInteger();
        if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            code.set(Integer.parseInt(productCategoryUtils.findMaxCode(user)) + 1);
        }
        Map<String, IObjectData> validDataMapping = validList.stream().collect(Collectors.toMap(k -> k.get(CATEGORY_CODE, String.class), v -> v));

        for (ProductCategoryTree treeData : categoryTreeData) {
            IObjectData validData = validDataMapping.get(treeData.getCategoryCode());
            if (validData == null) {
                continue;
            }
            validData.setId(treeData.getId());
            validData.setName(treeData.getName());
            validData.set(ProductCategoryModel.Filed.ORDER_FIELD, String.valueOf(treeData.getOrderField()));
            validData.setTenantId(user.getTenantId());
            if (productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
                validData.set(CODE, getRandomId());
            } else {
                validData.set(CODE, String.valueOf(code.getAndIncrement()));
                validData.setRecordType(DEFAULT_RECORD_TYPE);
                validData.set(CATEGORY_IMAGE, treeData.getCategoryImage());
                validData.set(FIELD_DESCRIBE_ID, ProductCategoryUtils.VIRTUAL_ID);
            }
            validData.set(CATEGORY_CODE, StringUtils.isEmpty(treeData.getCategoryCode()) ? getRandomId() : treeData.getCategoryCode());
            validData.set(FIELD_DESCRIBE_API_NAME, API_NAME);
            categoryPidMapping.put(validData.get(CATEGORY_CODE).toString(), treeData.getPid() == null ? "" : treeData.getPid());
        }
        return categoryPidMapping;
    }

    public void asyncSyncDescribe(User user, int delayTimeLevel) {
        if (categoryStaticUtilService.isStopSyncCategoryTenant(user.getTenantId())) {
            return;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId", user.getTenantId());
        String messageKey = user.getTenantId() + "@" + SYNC_DESCRIBE_BIZ_DELAY;
        asyncTaskProducer.create(SYNC_DESCRIBE_BIZ_DELAY, jsonObject.toJSONString(), messageKey, Integer.valueOf(user.getTenantId()), delayTimeLevel);
    }

    public void buildCategorySelectOptions(User user, ObjectDescribeDocument objectDescribe) {
        if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            return;
        }
        List<IObjectData> categoryData = productCategoryUtils.findAllSortedCategory(user, Lists.newArrayList(CODE, NAME, ORDER_FIELD, PID, PRODUCT_CATEGORY_PATH), true, CategoryFilterEnum.PRODUCT).getData();
        if (CollectionUtils.isEmpty(categoryData)) {
            return;
        }
        CategoryObject categoryObject = buildCategoryTree(categoryData);
        List<ISelectOption> options = convert(categoryObject);
        try {
            SelectOneFieldDescribe categoryFieldDescribe = (SelectOneFieldDescribe) objectDescribe.toObjectDescribe().getFieldDescribe("category");
            categoryFieldDescribe.setSelectOptions(options);
        } catch (Exception e) {
            log.error("buildCategorySelect error, tenant:{}", user.getTenantId(), e);
        }
    }

    public CategoryObject buildCategoryTree(List<IObjectData> allProductCategory) {
        StopWatch stopWatch = StopWatch.create("buildCategoryTree");
        // 并行流构建 CategoryObject
        String tenantId = getTenantId();
        boolean innerServiceRequest = productCategoryValidator.isInnerServiceRequest();
        List<CategoryObject> categoryObjectList = getCategoryObjects(allProductCategory, tenantId, innerServiceRequest);
        CategoryObject categoryObject = new CategoryObject();
        // 排序根节点
        categoryObject.setChildren(categoryObjectList.stream().filter(o -> o.getPid() == null)
                .sorted(Comparator.comparingInt(CategoryObject::getOrderField))
                .collect(Collectors.toList()));
        Map<String, CategoryObject> nodeMap = new HashMap<>();
        // 构建节点字典
        for (CategoryObject node : categoryObjectList) {
            nodeMap.put(node.getId(), node);
        }

        // 添加子节点
        for (CategoryObject node : categoryObjectList) {
            if (node.getPid() != null && nodeMap.containsKey(node.getPid())) {
                List<CategoryObject> children = nodeMap.get(node.getPid()).getChildren();
                children.add(node);
                // 排序子节点
                List<CategoryObject> storedChildren = children.stream().sorted(Comparator.comparingInt(CategoryObject::getOrderField)).collect(Collectors.toList());
                nodeMap.get(node.getPid()).setChildren(storedChildren);
            }
        }
        stopWatch.lap("buildTreeByForEach");
        stopWatch.log();
        return categoryObject;
    }

    public List<CategoryObject> getCategoryObjects(List<IObjectData> allProductCategory, String tenantId, boolean innerServiceRequest) {
        return allProductCategory.parallelStream()
                .map(data -> productCategoryValidator.buildCategory(tenantId, innerServiceRequest, data))
                .collect(Collectors.toList());
    }

    public String getTenantId() {
        return Optional.ofNullable(RequestContextManager.getContext()).map(RequestContext::getTenantId).orElse(null);
    }


    public void categoryPathAssemblyOfExport(User user, List<IObjectData> dataList, CategoryPathAssembly invoke) {
        if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            invoke.originalAssembly();
            return;
        }
        Map<String, String> productCategoryMapping = dataList
                .stream()
                .collect(Collectors.toMap(
                        DBRecord::getId,
                        d -> getDataFieldValue(d, PRODUCT_CATEGORY_ID, ""),
                        (k1, k2) -> k1));
        invoke.originalAssembly();
        dataList.forEach(data -> restoreCategoryId(data, productCategoryMapping));
        handleCategoryName(user, dataList, PRODUCT_CATEGORY_ID, PRODUCT_CATEGORY_ID);
    }

    private void restoreCategoryId(IObjectData data, Map<String, String> productCategoryMapping) {
        String categoryId = productCategoryMapping.get(data.getId());
        data.set(PRODUCT_CATEGORY_ID, categoryId);
    }

    public CategoryFilterEnum getCategoryFilterEnums(ProductCategoryListArg arg) {
        if (arg == null) {
            return CategoryFilterEnum.ALL;
        }
        CategoryFilterEnum categoryFilter = CategoryFilterEnum.getCategoryFilter(arg.getFilterCategory());
        if (categoryFilter == null) {
            throw new ValidateException(categoryStaticUtilService.text(SFA_PARAM_NON_COMPLIANT, "categoryFilter"));
        }
        if (categoryFilter != CategoryFilterEnum.SHOP && Boolean.TRUE.equals(arg.getFilterByShopCategory())) {
            throw new ValidateException(categoryStaticUtilService.text(SFA_PARAM_NON_COMPLIANT, "filterByShopCategory"));
        }
        return categoryFilter;
    }

    /**
     * 获取分类返回的字段有哪些
     *
     * @param user         用户
     * @param returnFields 参数传入的返回的字段 apiName 集合
     * @return 获取分类返回的字段
     */
    public List<String> getExtendFields(User user, Set<String> returnFields) {
        Set<String> fieldSet = Sets.newHashSet(LIST_DEFAULT_FIELDS);
        if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            // 未产品分类对象化的企业
            String customDisplayFields = bizConfigThreadLocalCacheService.getBizConfig(user.getTenantId(), CATEGORY_CUSTOM_DISPLAY_FIELDS);
            if (StringUtils.isNotEmpty(customDisplayFields)) {
                fieldSet.addAll(Arrays.asList(customDisplayFields.split(",")));
            }
        } else {
            // 产品分类对象化的企业
            if (CollectionUtils.isNotEmpty(returnFields)) {
                fieldSet.addAll(returnFields);
            } else {
                // 返回空数组代表返回值是所有字段
                return Lists.newArrayList();
            }
        }
        return Lists.newArrayList(fieldSet);
    }

    public List<IObjectData> filterByAvailableRange(ServiceContext context, ProductCategoryListArg arg, List<IObjectData> allCategoryDataFromDB) throws MetadataServiceException {
        if (arg == null || arg.getObjectData() == null) {
            return allCategoryDataFromDB;
        }
        if (categoryStaticUtilService.isDhtOrAccessoriesMallRequest(context.getPeerName(), context.getAppId())) {
            String accountId = categoryStaticUtilService.getAccountIdByOutTenantId(context.getTenantId(), context.getUser().getOutTenantId(), context.getUser().getOutUserId());
            arg.getObjectData().put("account_id", accountId);
        }
        return filterCategoryByAvailableRange(context.getUser(), arg, allCategoryDataFromDB);
    }

    public List<IObjectData> filterCategoryByAvailableRange(User user, ProductCategoryListArg arg, List<IObjectData> categoryDataList) throws MetadataServiceException {
        if (!bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId())) {
            return categoryDataList;
        }
        ObjectDataDocument dataDocument = arg.getObjectData();
        if (null == dataDocument) {
            return categoryDataList;
        }
        IObjectData objectData = dataDocument.toObjectData();
        String accountId = objectData.get("account_id", String.class);
        String partnerId = objectData.get("partner_id", String.class);
        if (Strings.isNullOrEmpty(accountId)) {
            return categoryDataList;
        }
        String priceBookId = objectData.get("price_book_id", String.class);
        log.info("filterCategoryByAvailableRange, objectData[{}]", objectData);
        Map<String, List<IObjectData>> detailDataList = Maps.newHashMap();
        dhtPriceBookService.detailDataDocument2detailData(arg.getDetails(), detailDataList);
        Tuple<Boolean, Set<String>> availableTuple = dhtPriceBookService.matchAvailableProduct(user, accountId,
                partnerId, priceBookId, null, objectData, detailDataList);
        if (!availableTuple.getKey()) {
            categoryDataList.clear();
            return categoryDataList;
        }
        String categorySetSql = getCategoryCodeSQL(user, availableTuple.getValue(), priceBookId, arg.getFilterByShopCategory(), arg.isIgnoreProductStatus());
        Set<String> categorySet = findUsedCategorySet(user.getTenantId(), categorySetSql, arg.getFilterByShopCategory(), categoryDataList);
        List<IObjectData> tempDataList = Lists.newArrayList();
        filterByUsedCategory(categoryDataList, categorySet, Sets.newHashSet(), tempDataList);
        categoryDataList.retainAll(tempDataList);
        return categoryDataList;
    }

    public void filterByUsedCategory(List<IObjectData> categoryDataList, Set<String> categorySet,
                                     Set<String> parentIdSet, List<IObjectData> tempDataList) {
        Set<String> parentCategory = Sets.newHashSet();
        for (IObjectData objectData : categoryDataList) {
            if (categorySet.contains(objectData.get(CODE, String.class)) || parentIdSet.contains(objectData.getId())) {
                tempDataList.add(objectData);
                String parentId = objectData.get(PID, String.class);
                if (!Strings.isNullOrEmpty(parentId)) {
                    parentCategory.add(parentId);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(parentCategory)) {
            filterByUsedCategory(categoryDataList, Sets.newHashSet(), parentCategory, tempDataList);
        }
    }

    public String getCategoryCodeSQL(User user, Set<String> productIds, String priceBookId, Boolean filterByShopCategory, boolean ignoreProductStatus) {
        String sql = Boolean.TRUE.equals(filterByShopCategory) ?
                getShopCategorySearchSql(user, productIds, priceBookId) :
                getCategorySearchSql(user, productIds, priceBookId);
        if (bizConfigThreadLocalCacheService.isCPQEnabled(user.getTenantId())) {
            sql = sql.replaceAll("bp.product_status = '1'", "bp.product_status = '1' and bp.is_saleable='true'");
        }
        if (ignoreProductStatus) {
            sql = sql.replaceAll("AND bp.product_status='1'", "");
        }
        return sql;
    }

    private String getShopCategorySearchSql(User user, Set<String> productIds, String priceBookId) {
        return Strings.isNullOrEmpty(priceBookId) ?
                getShopCategoryByProducts(user.getTenantId(), productIds) :
                getShopCategoryByPriceBookIdAndProducts(user.getTenantId(), priceBookId, productIds);
    }

    private String getShopCategoryByPriceBookIdAndProducts(String tenantId, String priceBookId, Set<String> productIds) {
        String sql = "SELECT \n" +
                "\tbp.shop_category_id \n" +
                "FROM\n" +
                "\tbiz_product bp\n" +
                "\tJOIN price_book_product pbp ON bp.tenant_id = pbp.tenant_id \n" +
                "\tAND pbp.product_id = bp.ID \n" +
                "WHERE\n" +
                "\tbp.tenant_id = '" + SqlEscaper.pg_escape(tenantId) + "' \n" +
                "\tAND bp.product_status='1' \n" +
                "\tAND bp.life_status = 'normal' \n" +
                "\tAND bp.is_deleted = '0' \n" +
                "\tAND pbp.is_deleted = '0'  \n" +
                "\tAND pbp.life_status = 'normal'\n" +
                "\tand pbp.pricebook_id='" + SqlEscaper.pg_escape(priceBookId) + "'\n";
        if (CollectionUtils.isNotEmpty(productIds)) {
            sql += " AND pbp.product_id = any (array ['" + Joiner.on("','").join(SqlEscaper.pg_escape(productIds)) + "'])";
        }
        return sql;
    }

    private String getShopCategoryByProducts(String tenantId, Set<String> productIds) {
        String sql = "SELECT \n" +
                "\tbp.shop_category_id \n" +
                "FROM\n" +
                "\tbiz_product bp\n" +
                "WHERE\n" +
                "\tbp.tenant_id = '" + SqlEscaper.pg_escape(tenantId) + "' \n" +
                "\tAND bp.product_status='1' \n" +
                "\tAND bp.life_status = 'normal' \n" +
                "\tAND bp.is_deleted = '0' \n";
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(productIds)) {
            sql += " AND bp.id = " + SqlEscaper.any_clause(productIds);
        }
        return sql;
    }

    private String getCategorySearchSql(User user, Set<String> productIds, String priceBookId) {
        return Strings.isNullOrEmpty(priceBookId) ?
                categoryStaticUtilService.getCategoryByProducts(user.getTenantId(), productIds) :
                categoryStaticUtilService.getCategoryByPriceBookIdAndProducts(user.getTenantId(), priceBookId, productIds);
    }

    public ProductCategoryTree getCategoryTree(User user, ChildCategoryArg arg) {
        String objectDataId = arg.getObjectDataId();
        IObjectData objectData = serviceFacadeProxy.findObjectData(user, objectDataId, "ProductCategoryObj");
        List<IObjectData> allCategory = productCategoryUtils.findAllCategory(user).getData();
        List<ProductCategoryTree> tree = productCategoryUtils.getTreeList(Lists.newArrayList(objectData), allCategory, PID);
        Optional<ProductCategoryTree> first = tree.stream().filter(t -> t.getId().equals(objectDataId)).findFirst();
        return first.orElse(null);
    }

    public SearchTemplateQuery buildListSearchQuery(User user, ProductCategoryListArg arg, boolean preCategoryField) {
        String searchQueryInfo = arg.getSearchQueryInfo();
        SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(searchQueryInfo);
        query = query == null ? new SearchTemplateQuery() : query;
        CategoryFilterEnum categoryFilter = getCategoryFilterEnums(arg);
        if (preCategoryField) {
            productCategoryUtils.fillCategoryFilter(query, categoryFilter);
        }
        try {
            if (arg.getObjectDataForWhere() != null && arg.getObjectDataForWhere().toObjectData() != null) {
                String bindObjectApiName = arg.getObjectDataForWhere().toObjectData().getDescribeApiName();
                serviceFacadeProxy.getFunctionLogicService().handleFiltersByValueType(user, bindObjectApiName, query, getTupleSupplier(user, arg));
            }
        } catch (ValidateException ve) {
            log.warn("构建函数筛选条件失败, tenant:{}", user.getTenantId(), ve);
            throw ve;
        } catch (Exception e) {
            log.warn("构建函数筛选条件失败, tenant:{}", user.getTenantId(), e);
            throw new ValidateException(categoryStaticUtilService.text(SFA_CATEGORY_FUNCTION_WHERE));
        }
        return query;
    }

    public Supplier<Tuple<IObjectData, Map<String, List<IObjectData>>>> getTupleSupplier(User user, ProductCategoryListArg arg) {
        return () -> {
            Tuple<IObjectData, Map<String, List<IObjectData>>> result = getFunctionData(arg);
            serviceFacadeProxy.fillQuoteValueVirtualField(user, result.getKey(), result.getValue());
            return result;
        };
    }

    public Tuple<IObjectData, Map<String, List<IObjectData>>> getFunctionData(ProductCategoryListArg arg) {
        return FunctionParamBuildUtils.getFunctionData(arg.getMasterDataForWhere(), arg.getObjectDataForWhere(), arg.getDetailsForWhere());
    }

    public boolean isPreCategoryField(String relatedFieldApiName) {
        return StringUtils.isBlank(relatedFieldApiName) || PRE_CATEGORY_FIELD.contains(relatedFieldApiName);
    }

    public List<IObjectData> queryListCategoryData(User user, Set<CategoryFilterConditionEnum> nodeConditions, boolean preCategoryField, SearchTemplateQuery searchQuery, Set<String> returnFields) {
        List<String> extendFields = getExtendFields(user, returnFields);
        searchQuery.setLimit(0);
        List<IObjectData> categoryData = findBySearchQueryWithFieldsIgnoreAll(user, SFAPreDefineObject.ProductCategory.getApiName(), searchQuery, extendFields);
        if (!preCategoryField) {
            return categoryData.stream()
                    .sorted(Comparator.comparingInt(data -> Integer.parseInt(getDataFieldValue(data, ORDER_FIELD, "1"))))
                    .collect(Collectors.toList());
        }
        return getIObjectDataList(user, nodeConditions, extendFields, categoryData);
    }

    public List<IObjectData> getIObjectDataList(User user, Set<CategoryFilterConditionEnum> nodeConditions, List<String> extendFields, List<IObjectData> categoryData) {
        List<IObjectData> relatedCategoryNodes = findRelatedCategoryNodesByConditions(user, nodeConditions, extendFields, categoryData);
        categoryData.addAll(relatedCategoryNodes);
        List<IObjectData> dataList = categoryData.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(IObjectData::getId, data -> data, (existing, replacement) -> existing),
                        map -> new ArrayList<>(map.values())
                ));
        return dataList.stream()
                .sorted(Comparator.comparingInt(data -> Integer.parseInt(getDataFieldValue(data, ORDER_FIELD, "1"))))
                .collect(Collectors.toList());
    }

    public List<IObjectData> findRelatedCategoryNodesByConditions(User user, Set<CategoryFilterConditionEnum> nodeConditions, List<String> extendFields, List<IObjectData> categoryData) {
        List<IObjectData> relatedCategoryNodes = Lists.newArrayList();
        if (CollectionUtils.isEmpty(nodeConditions)) {
            relatedCategoryNodes = findRelatedCategoryNodes(user, extendFields, categoryData, DEFAULT_CATEGORY_FILTER_CONDITIONS);
        } else {
            relatedCategoryNodes = findRelatedCategoryNodes(user, extendFields, categoryData, nodeConditions);
        }
        return relatedCategoryNodes;
    }

    public List<IObjectData> findRelatedCategoryNodes(User user, List<String> extendFields, List<IObjectData> categoryData, Set<CategoryFilterConditionEnum> conditions) {
        List<IObjectData> relatedCategoryData = Lists.newArrayList();
        List<String> existsDataId = categoryData.stream().map(DBRecord::getId).collect(Collectors.toList());
        for (CategoryFilterConditionEnum condition : conditions) {
            List<IObjectData> tempData = Lists.newArrayList();
            switch (condition) {
                case INCLUDE_PARENT_NODE:
                    tempData = getAllParentNodeData(user, existsDataId, extendFields, categoryData);
                    break;
                case INCLUDE_BROTHER_NODE:
                    tempData = getAllBrotherNodeData(user, existsDataId, extendFields, categoryData);
                    break;
                case INCLUDE_CHILDREN_NODE:
                    tempData = getAllChildrenNodeData(user, existsDataId, extendFields);
                    break;
                default:
                    break;
            }
            relatedCategoryData.addAll(tempData);
        }
        return relatedCategoryData;
    }

    public List<IObjectData> getAllChildrenNodeData(User user, List<String> existsDataId, List<String> extendFields) {
        List<IObjectData> childrenNodeData = Lists.newArrayList();
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterNotIn(query.getFilters(), IObjectData.ID, existsDataId);
        List<IObjectData> remainingCategoryData = findBySearchQueryWithFieldsIgnoreAll(user, SFAPreDefineObject.ProductCategory.getApiName(), query, extendFields);
        if (CollectionUtils.isEmpty(remainingCategoryData)) {
            return Lists.newArrayList();
        }
        for (IObjectData data : remainingCategoryData) {
            List<String> parentNodeIds = getCategoryParentPathNodeIds(data);
            parentNodeIds.stream().filter(existsDataId::contains).findAny().ifPresent(d -> childrenNodeData.add(data));
        }
        return childrenNodeData;
    }

    public List<String> getCategoryParentPathNodeIds(IObjectData data) {
        String path = getDataFieldValue(data, PRODUCT_CATEGORY_PATH, "");
        if (StringUtils.isBlank(path)) {
            return Lists.newArrayList();
        }
        String[] splitNodes = path.split("\\.");
        if (splitNodes.length <= 1) {
            return Lists.newArrayList();
        }
        String[] parentNodeIdArray = Arrays.copyOfRange(splitNodes, 0, splitNodes.length - 1);
        return Arrays.asList(parentNodeIdArray);
    }

    public List<IObjectData> findBySearchQueryWithFieldsIgnoreAll(User user, String objectApiName, SearchTemplateQuery query, List<String> fieldApiNames) {
        query.setLimit(0);
        if (CollectionUtils.isEmpty(fieldApiNames)) {
            return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, objectApiName, query).getData();
        } else {
            return metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, objectApiName, query, fieldApiNames);
        }
    }

    public List<IObjectData> getAllBrotherNodeData(User user, List<String> existsDataId, List<String> extendFields, List<IObjectData> categoryData) {
        Set<String> parentNodeIds = categoryData.stream().filter(d -> StringUtils.isNotBlank(getDataFieldValue(d, PID, ""))).map(x -> x.get(PID).toString()).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(parentNodeIds)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(query.getFilters(), PID, parentNodeIds);
        SearchUtil.fillFilterNotIn(query.getFilters(), IObjectData.ID, existsDataId);
        return findBySearchQueryWithFieldsIgnoreAll(user, SFAPreDefineObject.ProductCategory.getApiName(), query, extendFields);
    }

    public List<IObjectData> getAllParentNodeData(User user, List<String> existsDataId, List<String> extendFields, List<IObjectData> categoryData) {
        List<IObjectData> parentNodeData = Lists.newArrayList();
        Set<String> allParentNodeIds = getAllParentNodeIds(categoryData);
        existsDataId.forEach(allParentNodeIds::remove);
        if (CollectionUtils.isEmpty(allParentNodeIds)) {
            return parentNodeData;
        }
        if (CollectionUtils.isEmpty(extendFields)) {
            parentNodeData = metaDataFindServiceExt.findObjectByIdsIgnoreAll(user, Lists.newArrayList(allParentNodeIds), SFAPreDefineObject.ProductCategory.getApiName());
        } else {
            parentNodeData = metaDataFindServiceExt.findObjectByIdsWithFieldsIgnoreAll(user, Lists.newArrayList(allParentNodeIds), SFAPreDefineObject.ProductCategory.getApiName(), extendFields);
        }
        return parentNodeData;
    }

    public Set<String> getAllParentNodeIds(List<IObjectData> categoryData) {
        Set<String> parentNodeIds = Sets.newHashSet();
        for (IObjectData data : categoryData) {
            List<String> nodeIds = getCategoryParentPathNodeIds(data);
            parentNodeIds.addAll(nodeIds);
        }
        return parentNodeIds;
    }

    public List<IObjectData> findListCategoryData(User user, List<String> nodeConditions, boolean preCategoryField, SearchTemplateQuery searchQuery, Set<String> returnFields) {
        Set<CategoryFilterConditionEnum> conditions = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(nodeConditions)) {
            conditions = convertConditions(nodeConditions);
        }
        return queryListCategoryData(user, conditions, preCategoryField, searchQuery, returnFields);
    }

    private Set<CategoryFilterConditionEnum> convertConditions(List<String> nodeConditions) {
        return nodeConditions.stream().filter(c -> CategoryFilterConditionEnum.getCategoryFilterCondition(c) != null).map(CategoryFilterConditionEnum::getCategoryFilterCondition).collect(Collectors.toSet());
    }

    public void checkCategoryListArg(User user, ProductCategoryListArg arg) {
        try {
            SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());
        } catch (Exception e) {
            log.warn("checkCategoryListArg#SearchTemplateQuery.fromJsonString error, tenant:{}, searchQueryInfo:{}", user.getTenantId(), arg.getSearchQueryInfo());
            throw new ValidateException(categoryStaticUtilService.text(SFA_PARAM_NON_COMPLIANT, "searchQueryInfo"));
        }
        if (CollectionUtils.isEmpty(arg.getNodeConditions())) {
            return;
        }
        Set<CategoryFilterConditionEnum> categoryFilterConditions = convertConditions(arg.getNodeConditions());
        if (categoryFilterConditions.size() != arg.getNodeConditions().size()) {
            throw new ValidateException(categoryStaticUtilService.text(SFA_PARAM_NON_COMPLIANT, "nodeConditions"));
        }
    }

    public StandardSummaryFieldController.Arg transferProductCategorySearch(User user, StandardSummaryFieldController.Arg arg) {
        if (!productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            return arg;
        }
        String searchQueryInfo = arg.getSearchQueryInfo();
        SearchTemplateQuery argQueryInfo = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(searchQueryInfo);
        for (IFilter filter : argQueryInfo.getFilters()) {
            String fieldName = filter.getFieldName();
            if (PRODUCT_CATEGORY_ID_SEARCH.equals(fieldName)) {
                filter.setFieldName(PRODUCT_CATEGORY_ID);
                fillChildCategoryId(user, filter, PRODUCT_CATEGORY_ID);
            }
            if (SHOP_CATEGORY_ID.equals(fieldName)) {
                fillChildCategoryId(user, filter, SHOP_CATEGORY_ID);
            }
        }
        arg.setSearchQueryInfo(argQueryInfo.toJsonString());
        return arg;
    }

    public List<ProductAllCategoriesModel.CategoryPojo> queryProductAllCategories(List<IObjectData> allShopCategoryData) {
        List<ProductAllCategoriesModel.CategoryPojo> categoryObjectList = allShopCategoryData.stream().map(o -> new ProductAllCategoriesModel.CategoryPojo(
                o.getId(),
                o.get("code", String.class),
                o.getName(),
                o.get("order_field", String.class),
                o.get("pid", String.class),
                Boolean.FALSE,
                Lists.newArrayList())).collect(Collectors.toList());
        ProductAllCategoriesModel.CategoryPojo categoryObject = new ProductAllCategoriesModel.CategoryPojo();
        categoryObject.setChildren(categoryObjectList.stream().filter(o -> o.getParentId() == null).collect(Collectors.toList()));
        dealCurrentLevel(categoryObject, categoryObjectList);
        return categoryObject.getChildren();
    }

    private void dealCurrentLevel(ProductAllCategoriesModel.CategoryPojo categoryObject, List<ProductAllCategoriesModel.CategoryPojo> categoryObjectList) {
        List<ProductAllCategoriesModel.CategoryPojo> children = categoryObject.getChildren();
        if (CollectionUtils.isNotEmpty(children)) {

            for (ProductAllCategoriesModel.CategoryPojo child : children) {
                List<ProductAllCategoriesModel.CategoryPojo> collect = categoryObjectList.stream().filter(o -> child.getCategoryId().equals(o.getParentId())).sorted(Comparator.comparingInt(o -> Integer.parseInt(o.getCategoryOrder()))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    child.setChildren(collect);
                }
                dealCurrentLevel(child, categoryObjectList);
            }
        }

    }

    public void sendGrayCategoryObjectMq(User user) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId", user.getTenantId());
        String messageKey = "grayProductCategoryObject"
                .concat("@")
                .concat(user.getTenantId());
        asyncTaskProducer.create(GRAY_PRODUCT_CATEGORY_OBJECT, jsonObject.toJSONString(), messageKey);
    }

    public void updateParentCategoryHasChildrenField(User user, IObjectData productCategoryData) {
        if (productCategoryData == null) {
            return;
        }
        String pid = DataUtils.getValue(productCategoryData, PID, String.class);
        IObjectData parentCategoryData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, pid, SFAPreDefineObject.ProductCategory.getApiName());
        if (parentCategoryData == null) {
            return;
        }
        boolean hasChildren = hasChildren(user, parentCategoryData.getId());
        Boolean originalHasChildren = parentCategoryData.get(HAS_CHILDREN, Boolean.class);
        if (originalHasChildren != null && originalHasChildren == hasChildren) {
            return;
        }
        parentCategoryData.set(HAS_CHILDREN, hasChildren);
        metaDataFindServiceExt.bulkUpdateByFields(user, Lists.newArrayList(parentCategoryData), Lists.newArrayList(HAS_CHILDREN));

    }

    private boolean hasChildren(User user, String dataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), PID, dataId);
        query.setLimit(1);
        List<IObjectData> dataList = metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, SFAPreDefineObject.ProductCategory.getApiName(), query, Lists.newArrayList(IObjectData.ID));
        return org.apache.commons.collections.CollectionUtils.isNotEmpty(dataList);
    }

    public void updateParentCategoryHasChildrenField(User user, List<IObjectData> dataList, Set<String> deletedIds) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        Set<String> pids = dataList.stream().map(x -> DataUtils.getValue(x, PID, String.class)).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        pids.removeAll(deletedIds);

        List<IObjectData> parentDataList = metaDataFindServiceExt.findObjectByIdsIgnoreAll(user, Lists.newArrayList(pids), SFAPreDefineObject.ProductCategory.getApiName());
        if (CollectionUtils.isEmpty(parentDataList)) {
            return;
        }
        Set<String> newPids = parentDataList.stream().map(DBRecord::getId).collect(Collectors.toSet());
        Map<String, Integer> childCountMapping = batchQueryChildCountMapping(user, newPids);
        List<IObjectData> updateDataList = Lists.newArrayList();
        parentDataList.forEach(parentData -> insertUpdateHasChildrenData(parentData, childCountMapping, updateDataList));
        metaDataFindServiceExt.bulkUpdateByFields(user, updateDataList, Lists.newArrayList(HAS_CHILDREN));
    }

    private Map<String, Integer> batchQueryChildCountMapping(User user, Set<String> newPids) {
        Map<String, Integer> childCountMapping = Maps.newHashMap();
        List<List<String>> partition = Lists.partition(Lists.newArrayList(newPids), 500);
        for (List<String> ids : partition) {
            Map<String, Integer> map = queryChildCountMapping(user, Sets.newHashSet(ids));
            childCountMapping.putAll(map);
        }
        return childCountMapping;
    }

    private static void insertUpdateHasChildrenData(IObjectData parentData, Map<String, Integer> childCountMapping, List<IObjectData> updateDataList) {
        Boolean originalHasChildren = DataUtils.getValue(parentData, HAS_CHILDREN, Boolean.class);
        Integer count = childCountMapping.get(parentData.getId());
        boolean hasChildren = count != null && count > 0;
        if (originalHasChildren != null && originalHasChildren == hasChildren) {
            return;
        }
        parentData.set(HAS_CHILDREN, hasChildren);
        updateDataList.add(parentData);
    }

    private Map<String, Integer> queryChildCountMapping(User user, Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Maps.newHashMap();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(query.getFilters(), "pid", ids);
        SearchUtil.fillFilterEq(query.getFilters(), IObjectData.IS_DELETED, 0);
        List<IObjectData> dataList = serviceFacadeProxy.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(user.getTenantId()),
                query,
                SFAPreDefineObject.ProductCategory.getApiName(),
                Lists.newArrayList(PID),
                Count.TYPE_COUNT, null
        );
        Map<String, Integer> countMap = Maps.newHashMap();
        dataList.forEach(data -> {
            String pid = DataUtils.getValue(data, PID, String.class);
            Integer groupByCount = DataUtils.getValue(data, "groupbycount", Integer.class, 0);
            if (StringUtils.isBlank(pid)) {
                return;
            }
            countMap.put(pid, groupByCount);
        });
        return countMap;
    }

    public List<CategoryNode> children(User user, String parentId, Boolean includeParent) {
        if (StringUtils.isBlank(parentId)) {
            List<IObjectData> dataList = queryRootCategoryList(user);
            dataList = dataList.stream()
                    .sorted(Comparator.comparingInt(data -> Integer.parseInt(getDataFieldValue(data, ORDER_FIELD, "1"))))
                    .collect(Collectors.toList());
            return converter.convertInPutDataList(dataList, CategoryNode.class);
        }
        List<IObjectData> allChildrenDataList = Lists.newArrayList();
        if (Boolean.TRUE.equals(includeParent)) {
            IObjectData parentData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user.getTenantId(), parentId, SFAPreDefineObject.ProductCategory.getApiName());
            assertValidator.assertNotNull(parentData);
            allChildrenDataList.add(parentData);
        }
        allChildrenDataList.addAll(queryChildren(user, parentId));
        // 数据多语转化
        allChildrenDataList.forEach(data -> {
            String dataI18nName = I18NUtils.getDataI18nName(data);
            data.setName(dataI18nName);
        });
        List<IObjectData> storedDataList = allChildrenDataList.stream()
                .sorted(Comparator.comparingInt(data -> Integer.parseInt(getDataFieldValue(data, ORDER_FIELD, "1"))))
                .collect(Collectors.toList());
        return converter.convertInPutDataList(storedDataList, CategoryNode.class);
    }

    private List<IObjectData> queryChildren(User user, String parentId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), PID, parentId);
        // 查所有
        query.setLimit(0);
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, SFAPreDefineObject.ProductCategory.getApiName(), query).getData();
    }

    private List<IObjectData> queryRootCategoryList(User user) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterIsNull(query.getFilters(), PID);
        // 查所有
        query.setLimit(0);
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, SFAPreDefineObject.ProductCategory.getApiName(), query).getData();
    }

    public List<IObjectData> filterLeafOrIndependent(List<IObjectData> categoryDataList) {
        return categoryDataList.stream()
                .filter(node -> {
                    String path = DataUtils.getValue(node, PRODUCT_CATEGORY_PATH, String.class);
                    // 只要有一个别的节点的 path 是以当前 path 开头，且更长，则当前不是叶子
                    return categoryDataList.stream()
                            .filter(other -> other != node) // 不和自己比
                            .noneMatch(other -> {
                                String otherPath = DataUtils.getValue(other, PRODUCT_CATEGORY_PATH, String.class);
                                return otherPath.startsWith(path + "."); // 比如 A.B 是 A 的子节点
                            });
                })
                .collect(Collectors.toList());
    }

    public List<IObjectData> deduplicateById(List<IObjectData> dataList) {
        Set<String> seenIds = new HashSet<>();
        return dataList.stream()
                .filter(item -> seenIds.add(item.getId())) // 如果 id 已存在则 add 返回 false
                .collect(Collectors.toList());
    }

    public CategoryLineageResult lineage(User user, CategoryLineageArg arg) {
        assertValidator.assertAllNotBlank(arg.getMode());
        if (CollectionUtils.isEmpty(arg.getIdList()) && CollectionUtils.isEmpty(arg.getCodeList())) {
            throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
        }
        if (!"tree".equals(arg.getMode()) && !"list".equals(arg.getMode())) {
            throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
        }
        List<IObjectData> categoryDataList;
        if (CollectionUtils.isNotEmpty(arg.getIdList())) {
            categoryDataList = metaDataFindServiceExt.findObjectByIdsIgnoreAll(user,
                    arg.getIdList().stream().distinct().collect(Collectors.toList()),
                    SFAPreDefineObject.ProductCategory.getApiName());
        } else {
            categoryDataList = findCategoryDataListByCodes(user,
                    arg.getCodeList().stream().distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(categoryDataList)) {
            throw new ValidateException(I18N.text(DATA_NOT_EXIST));
        }
        categoryDataList = filterLeafOrIndependent(categoryDataList);

        Map<String, String> currentCategoryPathMap = Maps.newHashMap();
        for (IObjectData categoryData : categoryDataList) {
            String pid = DataUtils.getValue(categoryData, PID, String.class);
            if (StringUtils.isNotBlank(pid)) {
                String path = DataUtils.getValue(categoryData, PRODUCT_CATEGORY_PATH, String.class);
                currentCategoryPathMap.put(categoryData.getId(), path);
            }
        }
        List<IObjectData> finalLineageDataList = Lists.newArrayList(categoryDataList);
        // 查询 path 路径上的节点
        List<IObjectData> pathCategoryDataList = queryPathCategory(user, currentCategoryPathMap);
        finalLineageDataList.addAll(pathCategoryDataList);
        // 去重
        finalLineageDataList = deduplicateById(finalLineageDataList);

        // 节点排序
        finalLineageDataList = finalLineageDataList.stream()
                .sorted(Comparator.comparingInt(data -> Integer.parseInt(getDataFieldValue(data, ORDER_FIELD, "1"))))
                .collect(Collectors.toList());
        // 数据多语转换
        finalLineageDataList.forEach(data -> {
            String dataI18nName = I18NUtils.getDataI18nName(data);
            data.setName(dataI18nName);
        });
        List<CategoryNode> categoryNodeList = converter.convertInPutDataList(finalLineageDataList, CategoryNode.class);
        CategoryLineageResult lineageResult = new CategoryLineageResult();
        if ("list".equals(arg.getMode())) {
            lineageResult.setCategoryNodes(categoryNodeList);
        } else if ("tree".equals(arg.getMode())) {
            // 构建树
            List<CategoryNode> treeList = buildProductCategoryTree(categoryNodeList);
            lineageResult.setCategoryTreeList(treeList);
        } else {
            throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
        }
        return lineageResult;
    }

    private List<IObjectData> findCategoryDataListByCodes(User user, List<String> codeList) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(0);
        SearchUtil.fillFilterIn(query.getFilters(), CODE, codeList);
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, SFAPreDefineObject.ProductCategory.getApiName(), query).getData();
    }

    private List<IObjectData> queryPathCategory(User user, Map<String, String> currentCategoryPathMap) {
        if (currentCategoryPathMap == null || currentCategoryPathMap.isEmpty()) {
            return Lists.newArrayList();
        }
        Set<String> pathIds = parsePaths(currentCategoryPathMap);
        return metaDataFindServiceExt.findObjectByIdsIgnoreAll(
                user,
                new ArrayList<>(pathIds),
                SFAPreDefineObject.ProductCategory.getApiName()
        );
    }

    private List<CategoryNode> buildProductCategoryTree(List<CategoryNode> categoryNodeList) {
        // 1. 建立 id -> node 的映射表
        Map<String, CategoryNode> idMap = new HashMap<>();
        for (CategoryNode node : categoryNodeList) {
            idMap.put(node.getId(), node);
            node.setChildren(new ArrayList<>()); // 确保 children 不为 null
        }
        // 2. 构建树结构并收集所有根节点
        List<CategoryNode> rootList = new ArrayList<>();
        for (CategoryNode node : categoryNodeList) {
            String pid = node.getPid();
            if (StringUtils.isBlank(pid)) {
                // 没有父节点，说明是根节点
                rootList.add(node);
            } else {
                CategoryNode parent = idMap.get(pid);
                if (parent != null) {
                    parent.getChildren().add(node);
                } else {
                    // 父节点不存在，可以记录日志
                    log.warn("Parent not found for node id: {}", node.getId());
                    // 也可以将其视为根节点（如果你业务上允许）
                    rootList.add(node);
                }
            }
        }
        return rootList;
    }

    private List<IObjectData> queryPathCategoryWithSQL(User user, String currentCategoryId) {
        String sql = String.format("WITH RECURSIVE cat_tree AS (\n" +
                "    SELECT id, pid, tenant_id\n" +
                "    FROM product_category\n" +
                "    WHERE id = '%s' AND tenant_id = '%s'\n" +
                "\n" +
                "    UNION ALL\n" +
                "\n" +
                "    SELECT c.id, c.pid, c.tenant_id\n" +
                "    FROM product_category c\n" +
                "    JOIN cat_tree ct ON c.id = ct.pid AND c.tenant_id = ct.tenant_id\n" +
                ")\n" +
                "SELECT *\n" +
                "FROM cat_tree;", SqlEscaper.pg_escape(currentCategoryId), SqlEscaper.pg_escape(user.getTenantId()));
        try {
            List<Map> bySql = objectDataService.findBySql(user.getTenantId(), sql);
            Set<String> dataIds = Optional.ofNullable(bySql).orElse(Lists.newArrayList()).stream()
                    .map(d -> d.get("_id"))
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .collect(Collectors.toSet());
            return metaDataFindServiceExt.findObjectByIdsIgnoreAll(user, Lists.newArrayList(dataIds), SFAPreDefineObject.ProductCategory.getApiName());
        } catch (Exception e) {
            log.warn("queryPathCategoryWithSQL error, tenantId:{}", user.getTenantId(), e);
            throw new ValidateException(I18N.text(SFA_SYS_ERROR_MSG));
        }
    }
}