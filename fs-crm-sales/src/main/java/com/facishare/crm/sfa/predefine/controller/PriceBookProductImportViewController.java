package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.PriceBookImportUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardImportViewController;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class PriceBookProductImportViewController extends StandardImportViewController {

    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected void customFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        super.customFields(describe, fieldDescribes);
        PriceBookImportUtils.removeCurrencyFields(controllerContext.getTenantId(), fieldDescribes);
        if (arg.getImportType() == IMPORT_TYPE_EDIT) {
            if (StringUtils.equals(describe.getApiName(), SFAPreDefineObject.PriceBookProduct.getApiName())) {
                fieldDescribes.removeIf(f -> "product_id".equals(f.getApiName()));
                if (bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(controllerContext.getTenantId())) {
                    fieldDescribes.removeIf(f -> "actual_unit".equals(f.getApiName()));
                }
                if (bizConfigThreadLocalCacheService.isOpenStratifiedPricing(controllerContext.getTenantId())) {
                    fieldDescribes.removeIf(f -> "is_stratified_pricing".equals(f.getApiName()));
                }
            }
        }
    }
}
