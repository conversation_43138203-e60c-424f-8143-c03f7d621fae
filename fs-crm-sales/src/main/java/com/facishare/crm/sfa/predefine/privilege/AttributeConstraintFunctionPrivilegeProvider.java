package com.facishare.crm.sfa.predefine.privilege;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.paas.appframework.privilege.model.DefaultFunctionPrivilegeProvider;
import org.springframework.stereotype.Component;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2024-02-19
 */

@Component
public class AttributeConstraintFunctionPrivilegeProvider extends DefaultFunctionPrivilegeProvider {

    @Override
    public String getApiName() {
        return SFAPreDefineObject.AttributeConstraint.getApiName();
    }

}
