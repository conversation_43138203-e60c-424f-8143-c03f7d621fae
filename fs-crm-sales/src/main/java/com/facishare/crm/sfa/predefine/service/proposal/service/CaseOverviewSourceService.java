package com.facishare.crm.sfa.predefine.service.proposal.service;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.platform.AssertValidator;
import com.facishare.crm.platform.async.executor.AsyncBootstrap;
import com.facishare.crm.platform.converter.Converter;
import com.facishare.crm.platform.utils.DiffUtils;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.service.proposal.access.CaseOverviewSourceAccess;
import com.facishare.crm.sfa.predefine.service.proposal.dto.CaseOverviewSourceDTO;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.UpdateCaseSourceRequest;
import com.facishare.crm.sfa.predefine.service.proposal.vo.*;
import com.facishare.crm.sfa.prm.api.enhancer.DescribeEnhancer;
import com.facishare.crm.util.Safes;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.ObjectDesignerService;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.FindDescribeList;
import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.button.ButtonDefineType;
import com.facishare.paas.appframework.metadata.button.ButtonType;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IUdefButtonService;
import com.facishare.paas.metadata.impl.UdefButton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.*;
import java.util.concurrent.TimeoutException;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.proposal.dto.ProposalGeneratorI18N.CASE_OVERVIEW_SOURCE_EXISTS;
import static com.facishare.crm.sfa.predefine.service.proposal.dto.ProposalGeneratorI18N.CASE_OVERVIEW_SOURCE_NOT_EXISTS;
import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_CATEGORY_ASYNC_TIMEOUT;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Service
@Slf4j
public class CaseOverviewSourceService {
    @Resource
    private CaseOverviewSourceAccess caseOverviewSourceAccess;
    @Resource(name = "objectConverter")
    private Converter converter;
    @Resource
    private ObjectDesignerService objectDesignerService;
    @Resource
    private AsyncBootstrap asyncBootstrap;
    @Resource
    private DescribeEnhancer describeEnhancer;
    @Resource
    private ServiceFacade serviceFacade;
    @Autowired
    private IUdefButtonService buttonService;
    @Resource
    private AssertValidator assertValidator;

    public boolean deleteCaseOverviewSource(User user, String id) {
        assertValidator.assertAllNotBlank(id);
        IObjectData delete = caseOverviewSourceAccess.delete(user, id);
        if (delete != null) {
            CaseOverviewSourceDTO caseOverviewSource = converter.convertDTO(delete, CaseOverviewSourceDTO.class);
            deleteAiButton(user, caseOverviewSource.getRelatedObjectDescribe());
            deleteAiFunc(user, caseOverviewSource.getRelatedObjectDescribe());
        }
        return true;
    }

    private void deleteAiFunc(User user, String relatedObjectDescribe) {
        serviceFacade.deleteUserDefinedActionCode(user, relatedObjectDescribe, ObjectAction.PROPOSAL_AI_GENERATE.getActionCode());
    }

    private void deleteAiButton(User user, String relatedObjectDescribe) {
        IUdefButton expiredButton = buttonService.findByApiNameAndTenantId(ObjectAction.PROPOSAL_AI_GENERATE.getButtonApiName(), relatedObjectDescribe, user.getTenantId());
        if (expiredButton == null) {
            return;
        }
        buttonService.deleteUdefButton(expiredButton.getApiName(), expiredButton.getDescribeApiName(), user.getTenantId());
    }

    public CaseOverviewSourceVO createCaseOverviewSource(User user, CaseOverviewSourceDTO caseOverviewSource) {
        assertValidator.assertAllNotBlank(caseOverviewSource.getRelatedObjectDescribe());
        if (caseOverviewSourceAccess.existsRelatedObjectData(user, null, caseOverviewSource.getRelatedObjectDescribe())) {
            throw new ValidateException(I18N.text(CASE_OVERVIEW_SOURCE_EXISTS));
        }
        IObjectData objectData = converter.convertObjectData(caseOverviewSource);
        IObjectData savedData = caseOverviewSourceAccess.create(user, objectData);
        // 创建「AI方案」按钮
        createAIButtonAndFunc(user, caseOverviewSource.getRelatedObjectDescribe());
        // 创建「AI方案」功能权限
        createAiFunc(user, caseOverviewSource.getRelatedObjectDescribe());
        return converter.convertDTO(savedData, CaseOverviewSourceVO.class);
    }

    private void createAIButtonAndFunc(User user, String relatedObjectDescribe) {
        IUdefButton button = buildButtonInfo(ObjectAction.PROPOSAL_AI_GENERATE, relatedObjectDescribe);
        IUdefButton expiredButton = serviceFacade.findButtonByApiName(user, button.getApiName(), button.getDescribeApiName());
        if (expiredButton == null) {
            serviceFacade.createCustomButton(user, button);
        }
    }

    private void createAiFunc(User user, String relatedObjectDescribe) {
        List<String> actionCodes = Lists.newArrayList(ObjectAction.PROPOSAL_AI_GENERATE.getActionCode());
        serviceFacade.batchCreateFunc(user, relatedObjectDescribe, actionCodes);
        serviceFacade.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, relatedObjectDescribe, actionCodes, Lists.newArrayList());
    }

    private IUdefButton buildButtonInfo(ObjectAction objectAction, String objectApiName) {
        IUdefButton createButton = new UdefButton();
        createButton.setApiName(objectAction.getButtonApiName());
        createButton.setLabel(objectAction.getActionLabel());
        createButton.setUsePages(Lists.newArrayList(ButtonUsePageType.Detail.getId()));
        createButton.setDescribeApiName(objectApiName);
        createButton.setButtonType(ButtonType.COMMON.getId());
        createButton.setIsActive(true);
        createButton.setDefineType(ButtonDefineType.SYSTEM.getId());
        return createButton;
    }

    public List<CaseOverviewSourceVO> queryCaseOverviewSources(User user) {
        List<IObjectData> dataList = caseOverviewSourceAccess.list(user);
        return assembleCaseOverviewSourceList(user, dataList);
    }

    private List<CaseOverviewSourceVO> assembleCaseOverviewSourceList(User user, List<IObjectData> dataList) {
        Map<String, List<String>> relatedFieldMapping = dataList.stream().filter(d -> CollectionUtils.isNotEmpty(d.get("related_fields", List.class))).collect(
                Collectors.toMap(DBRecord::getId, v -> v.get("related_fields", List.class), (k1, k2) -> k1
                ));
        List<CaseOverviewSourceVO> voList = converter.convertInPutDataList(dataList, CaseOverviewSourceVO.class);
        Set<String> objectDescribeList = voList.stream().map(CaseOverviewSourceVO::getRelatedObjectDescribe)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        Map<String, IObjectDescribe> describeMapping = describeEnhancer.fetchObjects(user, objectDescribeList);
        voList.forEach(vo -> {
            List<String> relatedFields = relatedFieldMapping.get(vo.getId());
            if (CollectionUtils.isEmpty(relatedFields)) {
                return;
            }
            IObjectDescribe describe = describeMapping.get(vo.getRelatedObjectDescribe());
            if (describe == null) {
                return;
            }
            vo.setRelatedObjectDescribeName(describe.getDisplayName());
            Map<String, IFieldDescribe> fieldDescribeMapping = describe.getFieldDescribeMap();
            List<FieldVO> relatedFieldVOList = Lists.newArrayList();
            for (String relatedField : relatedFields) {
                FieldVO fieldVO = new FieldVO();
                fieldVO.setFieldApiName(relatedField);
                fieldVO.setLabel(Optional.ofNullable(fieldDescribeMapping.get(relatedField)).map(IFieldDescribe::getLabel).orElse(null));
                relatedFieldVOList.add(fieldVO);
            }
            vo.setRelatedFields(relatedFieldVOList);
        });
        return voList;
    }

    public CaseOverviewSourceVO queryCaseOverviewSource(User user, String id) {
        assertValidator.assertAllNotBlank(id);
        IObjectData objectData = caseOverviewSourceAccess.queryById(user, id);
        assertValidator.assertNotNull(objectData);
        List<CaseOverviewSourceVO> caseOverviewSourceVOS = assembleCaseOverviewSourceList(user, Lists.newArrayList(objectData));
        return Safes.first(caseOverviewSourceVOS);
    }

    public boolean updateCaseOverviewSource(User user, UpdateCaseSourceRequest updateCaseSourceRequest) {
        assertValidator.assertAllNotBlank(updateCaseSourceRequest.getId(), updateCaseSourceRequest.getRelatedObjectDescribe());
        IObjectData originalData = caseOverviewSourceAccess.queryById(user, updateCaseSourceRequest.getId());
        assertValidator.assertNotNull(originalData);
        if (caseOverviewSourceAccess.existsRelatedObjectData(user, updateCaseSourceRequest.getId(), updateCaseSourceRequest.getRelatedObjectDescribe())) {
            throw new ValidateException(I18N.text(CASE_OVERVIEW_SOURCE_EXISTS));
        }
        CaseOverviewSourceDTO originalCaseOverviewSource = converter.convertDTO(originalData, CaseOverviewSourceDTO.class);
        CaseOverviewSourceDTO argCaseOverviewSource = converter.convertDTO(updateCaseSourceRequest, CaseOverviewSourceDTO.class);
        if (!DiffUtils.hasChanged(originalCaseOverviewSource, argCaseOverviewSource)) {
            return true;
        }
        Map<String, Object[]> changedFields = DiffUtils.getChangedFields(originalCaseOverviewSource, argCaseOverviewSource);
        Set<String> fieldApiNames = ObjectDataUtils.setFieldValue(originalData, argCaseOverviewSource, changedFields.keySet());
        caseOverviewSourceAccess.updateByFields(user, originalData, fieldApiNames);
        return true;
    }

    public List<AbleObject> queryAbleObjects(ServiceContext serviceContext) {
        List<AbleObject> ableObjects = Lists.newArrayList();
        Map<String, Supplier<Object>> tasks = new HashMap<>();
        tasks.put("custom", () -> fetchCustomAbleObjects(serviceContext));
        tasks.put("package", () -> fetchPackageAbleObjects(serviceContext));
        try {
            asyncBootstrap.submitTasks(tasks, 15000, result -> {
                List customObjects = result.get("custom", List.class);
                if (CollectionUtils.isNotEmpty(customObjects)) {
                    ableObjects.addAll(customObjects);
                }
                List packageObjects = result.get("package", List.class);
                if (CollectionUtils.isNotEmpty(packageObjects)) {
                    ableObjects.addAll(packageObjects);
                }
            });
        } catch (TimeoutException te) {
            log.error("CaseOverviewSourceService#queryAbleObjects error, TimeoutException", te);
            throw new ValidateException(I18N.text(SO_CATEGORY_ASYNC_TIMEOUT));
        } catch (Exception e) {
            log.error("CaseOverviewSourceService#queryAbleObjects error", e);
            throw new ValidateException(I18N.text(SO_CATEGORY_ASYNC_TIMEOUT));
        }
        return ableObjects;
    }

    private List<AbleObject> fetchCustomAbleObjects(ServiceContext serviceContext) {
        return fetchAbleObjects(serviceContext, "custom");
    }

    private List<AbleObject> fetchPackageAbleObjects(ServiceContext serviceContext) {
        return fetchAbleObjects(serviceContext, "package");
    }

    private List<AbleObject> fetchAbleObjects(ServiceContext serviceContext, String describeDefineType) {
        FindDescribeList.Arg arg = new FindDescribeList.Arg();
        arg.setDescribeDefineType(describeDefineType);
        arg.setIncludeControlLevel(true);
        arg.setIncludeFieldDescribe(false);
        arg.setIncludeUnActived(false);
        arg.setPackageName("package");
        arg.setSourceInfo("object_management");
        FindDescribeList.Result describeManage = objectDesignerService.findDescribeManageList(arg, serviceContext);
        List<ObjectDescribeDocument> describeDocumentList = Optional.ofNullable(describeManage).map(FindDescribeList.Result::getObjectDescribeList).orElse(Lists.newArrayList());
        List<AbleObject> ableObjects = Lists.newArrayList();
        for (ObjectDescribeDocument describeDocument : describeDocumentList) {
            AbleObject ableObject = new AbleObject();
            ableObject.setDisplayName(describeDocument.toObjectDescribe().getDisplayName());
            ableObject.setDescribeApiName(describeDocument.toObjectDescribe().getApiName());
            ableObjects.add(ableObject);
        }
        return ableObjects;
    }

    public List<AbleField> queryAbleFields(User user, String objectDescribeName) {
        IObjectDescribe describe = describeEnhancer.fetchObject(user, objectDescribeName);
        assertValidator.assertNotNull(describe);
        List<IFieldDescribe> fieldDescribes = describe.getFieldDescribes();
        if (CollectionUtils.isEmpty(fieldDescribes)) {
            return Lists.newArrayList();
        }
        List<AbleField> ableFields = Lists.newArrayList();
        fieldDescribes.stream().filter(fieldDescribe -> !FieldDescribeExt.of(fieldDescribe).isSystemField()).forEach(fieldDescribe -> {
            AbleField ableField = new AbleField();
            ableField.setType(fieldDescribe.getType());
            ableField.setLabel(fieldDescribe.getLabel());
            ableField.setApiName(fieldDescribe.getApiName());
            ableFields.add(ableField);
        });
        return ableFields;
    }

    public CaseOverviewSourceVO queryCaseOverviewSourceByApiName(User user, String objectApiName) {
        assertValidator.assertAllNotBlank(objectApiName);
        IObjectData objectData = caseOverviewSourceAccess.queryByObject(user, objectApiName);
        if (objectData == null) {
            throw new ValidateException(I18N.text(CASE_OVERVIEW_SOURCE_NOT_EXISTS));
        }
        List<CaseOverviewSourceVO> caseOverviewSourceVOS = assembleCaseOverviewSourceList(user, Lists.newArrayList(objectData));
        return Safes.first(caseOverviewSourceVOS);
    }
}
