package com.facishare.crm.sfa.predefine.service.proposal.service;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.platform.AssertValidator;
import com.facishare.crm.platform.converter.Converter;
import com.facishare.crm.platform.utils.DiffUtils;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.service.proposal.access.ProposalAIFlowTemplateAccess;
import com.facishare.crm.sfa.predefine.service.proposal.dto.FlowInfoDTO;
import com.facishare.crm.sfa.predefine.service.proposal.dto.ProposalAIFlowTemplateDTO;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.UpdateProposalFlowRequest;
import com.facishare.crm.sfa.predefine.service.proposal.proxy.OneFlowProxy;
import com.facishare.crm.sfa.predefine.service.proposal.vo.ProposalAIFlowTemplateListVO;
import com.facishare.crm.sfa.predefine.service.proposal.vo.ProposalAIFlowTemplateVO;
import com.facishare.crm.sfa.utilities.proxy.model.OneFlowOfRemote;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Service
@Slf4j
public class ProposalAIFlowTemplateService {
    @Resource
    private ProposalAIFlowTemplateAccess proposalAIFlowTemplateAccess;
    @Resource(name = "objectConverter")
    private Converter converter;
    @Resource
    private AssertValidator assertValidator;
    @Resource
    private OneFlowProxy oneFlowProxy;


    public ProposalAIFlowTemplateVO createFlowTemplate(User user, ProposalAIFlowTemplateDTO proposalAIFlowTemplate) {
        assertValidator.assertAllNotBlank(proposalAIFlowTemplate.getName());
        IObjectData objectData = converter.convertObjectData(proposalAIFlowTemplate);
        IObjectData savedData = proposalAIFlowTemplateAccess.create(user, objectData);
        return converter.convertDTO(savedData, ProposalAIFlowTemplateVO.class);
    }

    public boolean deleteFlowTemplate(User user, String id) {
        assertValidator.assertAllNotBlank(id);
        proposalAIFlowTemplateAccess.delete(user, id);
        return true;
    }

    public boolean updateFlowTemplate(User user, UpdateProposalFlowRequest updateProposalFlowRequest) {
        assertValidator.assertAllNotBlank(updateProposalFlowRequest.getId(), updateProposalFlowRequest.getName());
        IObjectData originalData = proposalAIFlowTemplateAccess.queryById(user, updateProposalFlowRequest.getId());
        assertValidator.assertNotNull(originalData);
        ProposalAIFlowTemplateDTO originalProposalFlow = converter.convertDTO(originalData, ProposalAIFlowTemplateDTO.class);
        ProposalAIFlowTemplateDTO argProposalFlow = converter.convertDTO(updateProposalFlowRequest, ProposalAIFlowTemplateDTO.class);
        if (!DiffUtils.hasChanged(originalProposalFlow, argProposalFlow)) {
            return true;
        }
        Map<String, Object[]> changedFields = DiffUtils.getChangedFields(originalProposalFlow, argProposalFlow);
        Set<String> fieldApiNames = ObjectDataUtils.setFieldValue(originalData, argProposalFlow, changedFields.keySet());
        proposalAIFlowTemplateAccess.updateByFields(user, originalData, fieldApiNames);
        return true;
    }

    public ProposalAIFlowTemplateVO queryFlowTemplate(User user, String id) {
        assertValidator.assertAllNotBlank(id);
        IObjectData objectData = proposalAIFlowTemplateAccess.queryById(user, id);
        assertValidator.assertNotNull(objectData);
        ProposalAIFlowTemplateVO templateVO = converter.convertDTO(objectData, ProposalAIFlowTemplateVO.class);
        String flowId = templateVO.getFlowId();
        Map<String, FlowInfoDTO> flowInfoDTOMap = flowNodeMapping(user, Sets.newHashSet(flowId));
        FlowInfoDTO flowInfoDTO = flowInfoDTOMap.get(flowId);
        if (flowInfoDTO == null) {
            return templateVO;
        }

        templateVO.setFlowName(flowInfoDTO.getFlowName());
        List<String> outputNodes = templateVO.getOutputNodes();
        Map<String, OneFlowOfRemote.FlowNode> flowNodeMapping = flowInfoDTO.getFlowNodeMapping();
        List<OneFlowOfRemote.FlowNode> nodes = Lists.newArrayList();
        Optional.of(outputNodes).orElse(Lists.newArrayList()).forEach(nodeId -> {
            OneFlowOfRemote.FlowNode flowNode = flowNodeMapping.get(nodeId);
            if (flowNode == null) {
                return;
            }
            nodes.add(flowNode);
        });
        return templateVO;
    }

    public List<ProposalAIFlowTemplateListVO> queryFlowTemplates(User user) {
        List<IObjectData> dataList = proposalAIFlowTemplateAccess.list(user);
        List<ProposalAIFlowTemplateListVO> listVOS = converter.convertInPutDataList(dataList, ProposalAIFlowTemplateListVO.class);
        Set<String> flowIds = listVOS.stream()
                .map(ProposalAIFlowTemplateListVO::getFlowId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        Map<String, String> flowMapping = flowMapping(user, flowIds);
        listVOS.forEach(item -> {
            item.setFlowName(flowMapping.get(item.getFlowId()));
            item.setFlowApiName(item.getFlowId());
        });
        return listVOS;
    }

    public List<OneFlowOfRemote.Flow> flowList(User user) {
        List<OneFlowOfRemote.Flow> flows = oneFlowProxy.flowList(user);
        Lang lang = RequestContextManager.getContext().getLang();
        flows.forEach(flow -> {
            flow.setFlowId(flow.getSourceWorkflowId());
            Map<String, String> nameTranslateInfo = flow.getNameTranslateInfo();
            if (nameTranslateInfo != null && StringUtils.isNotBlank(nameTranslateInfo.get(lang.getValue()))) {
                flow.setNameI18N(nameTranslateInfo.get(lang.getValue()));
            } else {
                flow.setNameI18N(flow.getName());
            }
        });
        return flows;
    }

    /**
     * flowId, nodeApi, Node item
     *
     * @param user
     * @param flowIds
     * @return
     */
    public Map<String, FlowInfoDTO> flowNodeMapping(User user, Set<String> flowIds) {
        Map<String, FlowInfoDTO> map = new HashMap<>();
        if (CollectionUtils.isEmpty(flowIds)) {
            return map;
        }
        List<OneFlowOfRemote.Flow> flows = flowList(user);
        flows.forEach(flow -> {
            if (!flowIds.contains(flow.getSourceWorkflowId())) {
                return;
            }
            FlowInfoDTO flowInfoDTO = new FlowInfoDTO();
            flowInfoDTO.setFlowId(flow.getSourceWorkflowId());
            flowInfoDTO.setFlowName(flow.getNameI18N());

            List<OneFlowOfRemote.FlowNode> outputs = flow.getOutputs();
            if (CollectionUtils.isEmpty(outputs)) {
                return;
            }
            Map<String, OneFlowOfRemote.FlowNode> flowNodeMap = outputs.stream()
                    .collect(Collectors.toMap(OneFlowOfRemote.FlowNode::getId,
                            OneFlowOfRemote -> OneFlowOfRemote,
                            (k1, k2) -> k1));
            flowInfoDTO.setFlowNodeMapping(flowNodeMap);
            map.put(flow.getSourceWorkflowId(), flowInfoDTO);
        });
        return map;
    }

    /**
     * flowId,flowName map
     *
     * @param user
     * @param flowIds
     * @return
     */
    public Map<String, String> flowMapping(User user, Set<String> flowIds) {
        List<OneFlowOfRemote.Flow> flows = flowList(user);
        Map<String, String> map = new HashMap<>();
        flows.forEach(flow -> {
            if (!flowIds.contains(flow.getSourceWorkflowId())) {
                return;
            }
            String sourceWorkflowId = flow.getSourceWorkflowId();
            map.put(sourceWorkflowId, flow.getNameI18N());
        });
        return map;
    }
}
