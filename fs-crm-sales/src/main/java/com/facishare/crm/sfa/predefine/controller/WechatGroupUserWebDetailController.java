package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.lto.utils.StringUtil;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

/**
 * <AUTHOR>
 * @Date 2023/12/01 18:04
 * @Version 1.0
 **/
public class WechatGroupUserWebDetailController extends StandardWebDetailController {
  @Override
  protected Result after(Arg arg, Result result) {
    result = super.after(arg, result);

    ILayout dlayout = new Layout(result.getLayout());
    //移动端，屏蔽关联按钮
    if(RequestUtil.isH5Request() || RequestUtil.isMobileRequest() || RequestUtil.isH5MobileRequest()) {
      List<IButton> buttons = dlayout.getButtons();
      List<IButton> newButtons = Lists.newArrayList();
      for (IButton button : buttons) {
        //过滤“关联员工”按钮
        if (!button.getName().equalsIgnoreCase(ObjectAction.RELATED_EMPLOYEE.getButtonApiName())) {
          newButtons.add(button);
        }
      }
      dlayout.setButtons(newButtons);
      return result;
    }

    String tenantId = controllerContext.getTenantId();
    String objectDataId = arg.getObjectDataId();
    if(this.data != null){
      String type = AccountUtil.getStringValue(this.data, "type", "0");
      String wechatEmployee = AccountUtil.getStringValue(this.data, "wechat_employee_id", "");
      log.warn("WechatGroupUserWebDetail tenantId:{}, objectDateId:{}, type:{}, wechat_employee_id:{}", tenantId, objectDataId, type, wechatEmployee);

      List<IComponent> iComponents = Lists.newArrayList();
      try {
        iComponents = dlayout.getComponents();
      } catch (Exception ex) {
        log.error("WechatGroupUserWebDetail after err:", ex);
      }
      if(CollectionUtils.isEmpty(iComponents)) return result;

      for (IComponent component : iComponents) {
        rebuildButtons(component, type, wechatEmployee);
      }
    }
    return result;
  }

  private void rebuildButtons(IComponent component, String type, String wechatEmployeeId){
    if (component.getName().equals("head_info")) {
      List<IButton> iButtons = component.getButtons();
      if (CollectionUtils.isEmpty(iButtons)) return;

      log.warn("dlayout buttons:{}", iButtons);
      List<IButton> newButtons = Lists.newArrayList();
      for (IButton button : iButtons) {
        if("2".equals(type) && StringUtil.isEmpty(wechatEmployeeId)){
          //“关联员工”按钮置首
          if (button.getName().equalsIgnoreCase(ObjectAction.RELATED_EMPLOYEE.getButtonApiName())) {
            newButtons.add(0, button);
          } else {
            newButtons.add(button);
          }
        }else{
          //过滤“关联员工”按钮
          if (!button.getName().equalsIgnoreCase(ObjectAction.RELATED_EMPLOYEE.getButtonApiName())) {
            newButtons.add(button);
          }
        }
      }
      component.setButtons(newButtons);
    }
  }
}
