package com.facishare.crm.sfa.predefine.service.proposal.resource;

import com.facishare.crm.sfa.predefine.service.proposal.dto.request.OverviewLLMRequest;
import com.facishare.crm.sfa.predefine.service.proposal.service.ProposalGenerateService;
import com.facishare.crm.sfa.prm.platform.model.ApiResponse;
import com.facishare.crm.sfa.predefine.service.proposal.dto.RequestId;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.QuickCreateCaseOverviewRequest;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.UpdateCaseOverviewRequest;
import com.facishare.crm.sfa.predefine.service.proposal.service.CaseOverviewService;
import com.facishare.crm.sfa.predefine.service.proposal.vo.CaseOverviewVO;
import com.facishare.crm.sfa.predefine.service.proposal.vo.OperationResultVO;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@ServiceModule("case-overview")
@Component
@Slf4j
public class CaseOverviewResource {
    @Resource
    private CaseOverviewService caseOverviewService;
    @Resource
    private ProposalGenerateService proposalGenerateService;

    @ServiceMethod("query")
    public ApiResponse<CaseOverviewVO> query(ServiceContext serviceContext, RequestId requestArg) {
        CaseOverviewVO caseOverview = caseOverviewService.queryCaseOverview(serviceContext.getUser(), requestArg.getId());
        return ApiResponse.success(caseOverview);
    }

    @ServiceMethod("update")
    public ApiResponse<OperationResultVO> update(ServiceContext serviceContext, UpdateCaseOverviewRequest request) {
        boolean updated = caseOverviewService.update(serviceContext.getUser(), request);
        OperationResultVO operationResultVo = OperationResultVO
                .builder()
                .id(request.getId())
                .success(updated)
                .build();
        return ApiResponse.success(operationResultVo);
    }

    @ServiceMethod("quick-create")
    public ApiResponse<OperationResultVO> quickCreate(ServiceContext serviceContext, QuickCreateCaseOverviewRequest request) {
        String dataId = caseOverviewService.quickCreate(serviceContext.getUser(), request);
        OperationResultVO operationResultVo = OperationResultVO
                .builder()
                .id(dataId)
                .success(true)
                .build();
        return ApiResponse.success(operationResultVo);
    }

    @ServiceMethod("generate")
    public ApiResponse<OperationResultVO> generate(ServiceContext serviceContext, OverviewLLMRequest arg) {
        String id = proposalGenerateService.overviewLLM(serviceContext.getUser(), arg);
        OperationResultVO vo = OperationResultVO.builder().id(id).success(true).build();
        return ApiResponse.success(vo);
    }
}
