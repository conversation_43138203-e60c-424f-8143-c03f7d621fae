package com.facishare.crm.sfa.predefine.service.proposal.resource;

import com.facishare.crm.sfa.predefine.service.proposal.dto.AiTaskDTO;
import com.facishare.crm.sfa.predefine.service.proposal.dto.TaskResult;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.TriggerFlowRequest;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.AiTaskRequest;
import com.facishare.crm.sfa.predefine.service.proposal.service.ProposalGenerateService;
import com.facishare.crm.sfa.predefine.service.proposal.vo.AiTaskVO;
import com.facishare.crm.sfa.prm.platform.model.ApiResponse;
import com.facishare.crm.sfa.utilities.proxy.model.OneFlowOfRemote;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@ServiceModule("proposal-generator")
@Component
@Slf4j
public class ProposalGeneratorResource {
    @Resource
    private ProposalGenerateService proposalGenerateService;

    @ServiceMethod("query-task")
    public ApiResponse<AiTaskVO> queryTask(ServiceContext serviceContext, AiTaskRequest arg) {
        AiTaskDTO aiTask = proposalGenerateService.queryTask(arg.getSceneApiName(), arg.getTaskId());
        TaskResult taskResult = proposalGenerateService.queryTaskResult(serviceContext.getUser(), aiTask, arg.getSceneApiName(), arg.getTaskId());
        AiTaskVO aiTaskVO = AiTaskVO.builder()
                .sceneApiName(arg.getSceneApiName())
                .taskId(arg.getTaskId())
                .status(aiTask.getStatus())
                .taskResult(taskResult)
                .build();
        return ApiResponse.success(aiTaskVO);
    }

    @ServiceMethod("trigger-flow")
    public ApiResponse<Map<String, OneFlowOfRemote.OutputResult>> triggerFlow(ServiceContext serviceContext, TriggerFlowRequest arg) {
        Map<String, OneFlowOfRemote.OutputResult> resultMap = proposalGenerateService.triggerFlow(serviceContext.getUser(), arg);
        return ApiResponse.success(resultMap);
    }
}
