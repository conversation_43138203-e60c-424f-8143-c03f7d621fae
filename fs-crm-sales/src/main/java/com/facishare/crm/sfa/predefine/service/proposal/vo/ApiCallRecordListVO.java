package com.facishare.crm.sfa.predefine.service.proposal.vo;

import com.facishare.crm.platform.annotation.Convertible;
import com.facishare.crm.platform.annotation.Mappable;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Data
@Convertible
@JsonInclude(JsonInclude.Include.ALWAYS)
public class ApiCallRecordListVO {
    @Mappable(fieldApiName = "_id")
    private String id;
    private Long requestTime;
    private String userName;
    private String departmentName;
    private Integer statusCode;
    private Boolean success;
    private String responseMessage;
    private Long responseTime;
}
