package com.facishare.crm.sfa.predefine.service.proposal.vo;

import com.facishare.crm.sfa.predefine.service.proposal.dto.TaskResult;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-26
 * ============================================================
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.ALWAYS)
public class AiTaskVO {
    private String sceneApiName;
    private String taskId;
    private String status;
    private TaskResult taskResult;
}
