package com.facishare.crm.sfa.predefine.service.proposal.proxy;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.utilities.proxy.OneFlowClient;
import com.facishare.crm.sfa.utilities.proxy.model.OneFlowOfRemote;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static com.facishare.crm.sfa.predefine.service.proposal.dto.ProposalGeneratorI18N.SFA_AI_FLOW_TRIGGER_FAILED;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-30
 * ============================================================
 */
@Service
@Slf4j
public class OneFlowProxy {
    @Resource(name = "oneFlowClient")
    private OneFlowClient oneFlowClient;

    public List<OneFlowOfRemote.Flow> flowList(User user) {
        List<OneFlowOfRemote.Flow> result = Lists.newArrayList();

        Map<String, String> headers = OneFlowClient.getHeaders(user.getTenantId());
        OneFlowOfRemote.ListArg arg = new OneFlowOfRemote.ListArg();
        arg.setEnable(true);
        arg.setPageSize(100);
        arg.setSubType("auto");
        int page = 1;
        while (page <= 5) {
            arg.setPageNumber(page);
            page++;
            OneFlowOfRemote.FlowListResponse response = oneFlowClient.list(headers, arg);
            if (response == null || response.getData() == null) {
                return result;
            }
            result.addAll(response.getData().getData());
            if (response.getData().getData().size() < 100) {
                break;
            }
        }
        return result;
    }

    public OneFlowOfRemote.TriggerResult trigger(User user, String flowId, Map<String, Object> inputs) {
        Map<String, String> headers = OneFlowClient.getHeaders(user.getTenantId());
        OneFlowOfRemote.Trigger arg = new OneFlowOfRemote.Trigger();
        arg.setSourceWorkflowId(flowId);
        OneFlowOfRemote.TriggerSource triggerSource = new OneFlowOfRemote.TriggerSource();
        triggerSource.setBusinessId(UUID.randomUUID().toString());
        triggerSource.setBusinessType("paas_ai_agent");
        arg.setSource(triggerSource);
        arg.setInputs(inputs);
        OneFlowOfRemote.TriggerResponse trigger = oneFlowClient.trigger(headers, arg);
        if (trigger == null || trigger.getData() == null) {
            log.warn("OneFlowProxy#trigger failed, message:{}", Optional.ofNullable(trigger).map(OneFlowOfRemote.TriggerResponse::getMessage).orElse("null"));
            throw new ValidateException(I18N.text(SFA_AI_FLOW_TRIGGER_FAILED));
        }
        return trigger.getData();
    }
}
