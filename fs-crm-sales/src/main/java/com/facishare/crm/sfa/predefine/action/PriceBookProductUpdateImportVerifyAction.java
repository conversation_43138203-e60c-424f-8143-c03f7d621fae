package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.PriceBookImportUtils;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportVerifyAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

/**
 * <AUTHOR> 2019-11-05
 * @instruction
 */
public class PriceBookProductUpdateImportVerifyAction extends StandardUpdateImportVerifyAction {
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService= SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fieldDescribes = super.getValidImportFields();
        fieldDescribes.removeIf(f -> "product_id".equals(f.getApiName()));
        if (bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(actionContext.getTenantId())) {
            fieldDescribes.removeIf(f -> "actual_unit".equals(f.getApiName()));
        }
        if (bizConfigThreadLocalCacheService.isOpenStratifiedPricing(actionContext.getTenantId())) {
            fieldDescribes.removeIf(f -> "is_stratified_pricing".equals(f.getApiName()));
        }
        PriceBookImportUtils.removeCurrencyFields(actionContext.getTenantId(), fieldDescribes);
        return fieldDescribes;
    }
}
