package com.facishare.crm.sfa.predefine.service.mapping.model;

import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.CreateRule;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface MappingRuleModel {
    
    @Data
    class Arg {
        private String bizType;
        CreateRule.Arg createRuleArg;
    }

    @Data
    @Builder
    class Result {
        private boolean success;
    }
}
