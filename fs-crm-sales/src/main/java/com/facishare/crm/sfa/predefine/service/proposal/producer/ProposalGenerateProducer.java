package com.facishare.crm.sfa.predefine.service.proposal.producer;

import com.facishare.crm.sfa.task.AsyncTaskCommonProducer;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-27
 * ============================================================
 */
@Component
public class ProposalGenerateProducer extends AsyncTaskCommonProducer {
    @PostConstruct
    public void init() {
        super.init("fs-crm-task-sfa-mq.ini", "proposal-generate-producer");
    }
}
