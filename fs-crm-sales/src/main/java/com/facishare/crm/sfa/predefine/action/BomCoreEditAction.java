package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.cache.RedisDataAccessor;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreV3Service;
import com.facishare.crm.sfa.predefine.service.task.ProductStatusChangeTaskService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.BomCoreConstants;
import com.facishare.crm.sfa.utilities.enums.EnumUtil;
import com.facishare.crm.sfa.utilities.util.BomUtils;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
@Slf4j
public class BomCoreEditAction extends StandardEditAction {
    private final BomCoreV3Service bomCoreV3Service = SpringUtil.getContext().getBean(BomCoreV3Service.class);
    private final ProductStatusChangeTaskService productStatusChangeTaskService = SpringUtil.getContext().getBean(ProductStatusChangeTaskService.class);
    private static final String ACTION_TYPE = "action_type";
    private Map<String, List<IObjectData>> groupBomMap = new HashMap<>();
    private Map<String, String> bom2GroupIdMap = new HashMap<>();
    private Map<String, IObjectData> dbBomIdMap = new HashMap<>();
    private List<IObjectData> groupToAdd = Lists.newArrayList();
    private List<IObjectData> groupToUpdate = Lists.newArrayList();
    private List<IObjectData> groupToDelete = Lists.newArrayList();
    private Set<String> noActiveNode = Sets.newHashSet();
    private List<IObjectData> allGroupList = Lists.newArrayList();
    private final RedisDataAccessor redisDataAccessor = SpringUtil.getContext().getBean(RedisDataAccessor.class);


    @Override
    protected void before(Arg arg) {
        dataInit(arg);
        super.before(arg);
        check();
    }

    private void dataInit(Arg arg) {
        List<ObjectDataDocument> treeList = arg.getDetails().get(Utils.BOM_API_NAME);
        if (CollectionUtils.notEmpty(treeList)) {
            SearchTemplateQuery query = getSearchTemplateQuery(arg);
            QueryResult<IObjectData> result = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(actionContext.getUser(), Utils.BOM_API_NAME, query, BomConstants.FILTER_FIELD);
            Map<String, List<ObjectDataDocument>> dataMap;
            String rootId = serviceFacade.generateId();
            if (result != null && CollectionUtils.notEmpty(result.getData())) {
                dataMap = bomCoreV3Service.analyzeBOMV3Tree(actionContext.getUser(), treeList, result.getData().get(0).get(BomConstants.FIELD_ROOT_ID, String.class), 0, result.getData(), MapUtils.getString(arg.getObjectData(), BomConstants.FIELD_PRODUCT_ID));
            } else {
                dataMap = bomCoreV3Service.analyzeBOMV3Tree(actionContext.getUser(), treeList, rootId, 0, Lists.newArrayList(), MapUtils.getString(arg.getObjectData(), BomConstants.FIELD_PRODUCT_ID));
            }
            Set<String> noActiveGroup = Sets.newHashSet();
            dataMap.getOrDefault(Utils.PRODUCT_GROUP_API_NAME, Lists.newArrayList()).stream().map(ObjectDataDocument::toObjectData).forEach(x -> {
                String actionType = x.get(ACTION_TYPE, String.class);
                if (Objects.equals(actionType, EnumUtil.actionType.create.getValue())) {
                    groupToAdd.add(x);
                    if (BooleanUtils.isFalse(x.get(BomConstants.FIELD_ENABLED_STATUS, Boolean.class))) {
                        noActiveGroup.add(x.getId());
                    }
                }
                if (Objects.equals(actionType, EnumUtil.actionType.update.getValue())) {
                    groupToUpdate.add(x);
                    if (BooleanUtils.isFalse(x.get(BomConstants.FIELD_ENABLED_STATUS, Boolean.class))) {
                        noActiveGroup.add(x.getId());
                    }
                }
                if (Objects.equals(actionType, EnumUtil.actionType.delete.getValue())) {
                    groupToDelete.add(x);
                }
                if (!Objects.equals(actionType, EnumUtil.actionType.delete.getValue())) {
                    allGroupList.add(x);
                }
            });
            List<ObjectDataDocument> bomList = new ArrayList<>();
            for (ObjectDataDocument objectDataDocument : dataMap.getOrDefault(Utils.BOM_API_NAME, Lists.newArrayList())) {
                if (StringUtils.equalsAny(MapUtils.getString(objectDataDocument, ACTION_TYPE),
                        EnumUtil.actionType.create.getValue(), EnumUtil.actionType.update.getValue(), EnumUtil.actionType.delete.getValue())) {
                    if (StringUtils.isNotBlank(MapUtils.getString(objectDataDocument, BomConstants.FIELD_PRODUCT_GROUP_ID)) && noActiveGroup.contains(MapUtils.getString(objectDataDocument, BomConstants.FIELD_PRODUCT_GROUP_ID)) && StringUtils.isNotBlank(objectDataDocument.getId())) {
                        noActiveNode.add(objectDataDocument.getId());
                    }
                    if (StringUtils.equals(MapUtils.getString(objectDataDocument, ACTION_TYPE),
                            EnumUtil.actionType.create.getValue()) && StringUtils.isNotBlank(MapUtils.getString(objectDataDocument, BomConstants.FIELD_PRODUCT_GROUP_ID))) {
                        bom2GroupIdMap.put(objectDataDocument.getId(), MapUtils.getString(objectDataDocument, BomConstants.FIELD_PRODUCT_GROUP_ID));
                        redisDataAccessor.set(BomUtils.getRedisKey(BomCoreConstants.BOM_CORE_APPROVE_BOM_GROUP_RELATE_ID_KEY,actionContext.getTenantId(),Optional.ofNullable(arg.getObjectData()).map(ObjectDataDocument::getId).orElse("")), JSON.toJSONString(bom2GroupIdMap),100*24*60*60);
                        objectDataDocument.put(BomConstants.FIELD_PRODUCT_GROUP_ID, null);
                    }
                    bomList.add(objectDataDocument);
                }
            }

            if (result == null || CollectionUtils.empty(result.getData())) {
                createRootNode(arg, rootId, bomList);
            }
            arg.getDetails().put(Utils.BOM_API_NAME, bomList);
            groupCache(arg);

        }
    }

    private void groupCache(Arg arg) {
        BomCoreConstants.ProductGroupArg build = BomCoreConstants.ProductGroupArg.builder().groupToAdd(ObjectDataDocument.ofList(groupToAdd)).groupToUpdate(ObjectDataDocument.ofList(groupToUpdate)).groupToDelete(ObjectDataDocument.ofList(groupToDelete)).build();
        redisDataAccessor.set(BomUtils.getRedisKey(BomCoreConstants.BOM_CORE_APPROVE_GROUP_KEY,actionContext.getTenantId(),Optional.ofNullable(arg.getObjectData()).map(ObjectDataDocument::getId).orElse("")), JSON.toJSONString(build),100*24*60*60);
    }

    private void createRootNode(Arg arg, String rootId, List<ObjectDataDocument> bomList) {
        if (Objects.isNull(arg.getObjectData())) {
            return;
        }
        IObjectData objectData = arg.getObjectData().toObjectData();
        IObjectData bomData;
        if (CollectionUtils.notEmpty(bomList)) {
            bomData = ObjectDataExt.of(bomList.get(0)).copy();
        } else {
            bomData = new ObjectData();
        }
        bomData.set(BomConstants.FIELD_PRODUCT_ID, objectData.get(BomConstants.FIELD_PRODUCT_ID));
        bomData.set(BomConstants.FIELD_PARENT_PRODUCT_ID, objectData.get(BomConstants.FIELD_PRODUCT_ID));
        bomData.setDescribeApiName(Utils.BOM_API_NAME);
        bomData.setTenantId(actionContext.getTenantId());
        bomData.setLastModifiedTime(System.currentTimeMillis());
        bomData.setLastModifiedBy(actionContext.getUser().getUpstreamOwnerIdOrUserId());
        bomData.setCreateTime(System.currentTimeMillis());
        bomData.setCreatedBy(actionContext.getUser().getUpstreamOwnerIdOrUserId());
        bomData.set(BomConstants.FIELD_ORDER_FIELD, 0);
        bomData.setId(rootId);
        bomData.set(BomConstants.FIELD_RELATED_CORE_ID, null);
        bomData.set(BomConstants.FIELD_NODE_BOM_CORE_TYPE, null);
        bomData.set(BomConstants.FIELD_NODE_BOM_CORE_VERSION, null);
        bomData.set(BomConstants.FIELD_CORE_ID, objectData.getId());
        bomData.set(BomConstants.FIELD_ROOT_ID, rootId);
        bomData.set(BomConstants.FIELD_BOM_PATH, rootId);
        bomData.set(BomConstants.FIELD_PARENT_BOM_ID, null);
        bomData.set(BomConstants.FIELD_PRODUCT_GROUP_ID, null);
        bomData.set(BomConstants.FIELD_UNIT_ID, null);
        bomData.set(BomConstants.SHARE_RATE, null);
        bomData.set(BomConstants.FIELD_ENABLED_STATUS, true);
        bomData.set(BomConstants.FIELD_AMOUNT, "1");
        bomData.set("dataIndex", 9999);
        bomData.setRecordType(Optional.ofNullable(arg.getObjectData()).map(ObjectDataDocument::toObjectData).map(MultiRecordType::getRecordType).orElse(MultiRecordType.RECORD_TYPE_DEFAULT));
        bomList.add(ObjectDataDocument.of(bomData));
    }

    @NotNull
    private SearchTemplateQuery getSearchTemplateQuery(Arg arg) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(2000);
        SearchUtil.fillFilterEq(query.getFilters(), BomConstants.FIELD_CORE_ID, arg.getObjectData().getId());
        SearchUtil.fillFilterEq(query.getFilters(), ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());
        SearchUtil.fillFilterEq(query.getFilters(), DBRecord.IS_DELETED, "0");
        SearchUtil.fillFilterIsNotNull(query.getFilters(), BomConstants.FIELD_ROOT_ID);
        List<OrderBy> orders = Lists.newArrayList();
        OrderBy orderBy = new OrderBy();
        orderBy.setIsAsc(true);
        orderBy.setFieldName(DBRecord.CREATE_TIME);
        orders.add(orderBy);
        query.setOrders(orders);
        return query;
    }

    private void check() {
        Map<String, IObjectData> newDbBomIdMap = new HashMap<>();
        List<IObjectData> addList = new ArrayList<>();
        List<String> nodeCoreList = Lists.newArrayList();
        for (IObjectData iObjectData : detailsToAdd) {
            if (StringUtils.equals(iObjectData.getDescribeApiName(), Utils.BOM_API_NAME)) {
                addList.add(iObjectData);
                newDbBomIdMap.put(iObjectData.getId(), iObjectData);
                if(StringUtils.isNotBlank(iObjectData.get(BomConstants.FIELD_RELATED_CORE_ID, String.class))){
                    nodeCoreList.add(iObjectData.get(BomConstants.FIELD_RELATED_CORE_ID, String.class));
                }
            }
        }
        List<IObjectData> updateList = new ArrayList<>();
        for (IObjectData x : detailsToUpdate) {
            if (StringUtils.equals(x.getDescribeApiName(), Utils.BOM_API_NAME)) {
                updateList.add(x);
                newDbBomIdMap.put(x.getId(), x);
            }
        }
        List<IObjectData> detailList = Lists.newArrayList();
        detailList.addAll(addList);
        detailList.addAll(updateList);
        bomCoreV3Service.checkType(detailList, objectData);
        bomCoreV3Service.checkBomCycle(actionContext.getUser(), objectData, detailList, 1);
        checkQuantity();
        checkNodeBomCore(newDbBomIdMap, addList, updateList);
        bomCoreV3Service.checkSaleStrategy(objectDescribe, actionContext.getTenantId(), nodeCoreList, Optional.ofNullable(objectData).map(x->x.get(BomCoreConstants.SALE_STRATEGY, String.class)).orElse(dbMasterData.get(BomCoreConstants.SALE_STRATEGY, String.class)));
    }

    private void checkNodeBomCore(Map<String, IObjectData> newDbBomIdMap, List<IObjectData> addList, List<IObjectData> updateList) {
        newDbBomIdMap.putAll(dbBomIdMap);
        if (CollectionUtils.notEmpty(updateList)) {
            List<IObjectData> deletedList = detailsToDelete.stream().filter(x -> StringUtils.equals(x.getDescribeApiName(), Utils.BOM_API_NAME)).collect(Collectors.toList());
            deletedList.forEach(x -> newDbBomIdMap.remove(x.getId()));
            updateList.forEach(x -> {
                if (BooleanUtils.isTrue(x.get(BomConstants.FIELD_IS_PACKAGE, Boolean.class, false)) || StringUtils.isNotBlank(x.get(BomConstants.FIELD_RELATED_CORE_ID, String.class))) {
                    IObjectData node = newDbBomIdMap.get(x.getId());
                    if (node != null) {
                        node.set(BomConstants.FIELD_IS_PACKAGE, x.get(BomConstants.FIELD_IS_PACKAGE, Boolean.class, false));
                        node.set(BomConstants.FIELD_RELATED_CORE_ID, x.get(BomConstants.FIELD_RELATED_CORE_ID, String.class));
                    }
                }
                IObjectData parentNode = newDbBomIdMap.get(x.get(BomConstants.FIELD_PARENT_BOM_ID, String.class));
                if (parentNode != null && (StringUtils.isNotBlank(parentNode.get(BomConstants.FIELD_RELATED_CORE_ID, String.class)) || BooleanUtils.isTrue(parentNode.get(BomConstants.FIELD_IS_PACKAGE, Boolean.class, false)))) {
                    throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_RELATED_BOM_CAN_NOT_CHILD_NODE));
                }
            });
        }
        addList.forEach(x -> {
            IObjectData parentNode = newDbBomIdMap.get(x.get(BomConstants.FIELD_PARENT_BOM_ID, String.class));
            if (parentNode != null && (StringUtils.isNotBlank(parentNode.get(BomConstants.FIELD_RELATED_CORE_ID, String.class)) || BooleanUtils.isTrue(parentNode.get(BomConstants.FIELD_IS_PACKAGE, Boolean.class, false)))) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_RELATED_BOM_CAN_NOT_CHILD_NODE));
            }
            if (StringUtils.equals(objectData.get(BomConstants.FIELD_PRODUCT_ID, String.class), x.get(BomConstants.FIELD_PRODUCT_ID, String.class)) && !StringUtils.equals(x.getId(), x.get(BomConstants.FIELD_ROOT_ID, String.class))) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_RELATED_BOM_CYCLE, ""));
            }
        });
    }

    @Override
    protected void doUpdateData() {
        if(!Objects.equals(dbMasterData.get(BomCoreConstants.SALE_STRATEGY),objectData.get(BomCoreConstants.SALE_STRATEGY))){
            objectData.set(BomCoreConstants.SALE_STRATEGY,dbMasterData.get(BomCoreConstants.SALE_STRATEGY));
        }
        for (IObjectData x : CollectionUtils.nullToEmpty(detailsToAdd)) {
            x.set(BomConstants.FIELD_PRODUCT_GROUP_ID, bom2GroupIdMap.get(x.getId()));
            String bomPath = x.get(BomConstants.FIELD_BOM_PATH, String.class);
            if (StringUtils.isNotBlank(bomPath)) {
                noActiveNode.stream().filter(bomPath::contains).findFirst().ifPresent(n -> x.set(BomConstants.FIELD_ENABLED_STATUS, false));
            }
        }
        checkGroupRule();
        Set<String> updateIds = detailsToUpdate.stream().map(DBRecord::getId).collect(Collectors.toSet());
        Set<String> deleteIds = detailsToDelete.stream().map(DBRecord::getId).collect(Collectors.toSet());
        List<IObjectData> dbBomList = dbDetailDataMap.getOrDefault(Utils.BOM_API_NAME, Lists.newArrayList());
        for (IObjectData bom : dbBomList) {
            if (updateIds.contains(bom.getId()) || deleteIds.contains(bom.getId()) || BooleanUtils.isFalse(bom.get(BomConstants.FIELD_ENABLED_STATUS, Boolean.class))) {
                continue;
            }
            String bomPath = bom.get(BomConstants.FIELD_BOM_PATH, String.class);
            if (StringUtils.isNotBlank(bomPath)) {
                noActiveNode.stream().filter(bomPath::contains).findFirst().ifPresent(n -> {
                    bom.set(BomConstants.FIELD_ENABLED_STATUS, false);
                    detailsToUpdate.add(bom);
                });
            }
        }
        if (CollectionUtils.notEmpty(groupToAdd)) {
            serviceFacade.bulkSaveObjectData(groupToAdd, actionContext.getUser());
        }
        if (CollectionUtils.notEmpty(groupToUpdate)) {
            serviceFacade.batchUpdate(groupToUpdate, actionContext.getUser());
        }
        if (CollectionUtils.notEmpty(groupToDelete)) {
            serviceFacade.bulkDeleteDirect(groupToDelete, actionContext.getUser());
        }
        super.doUpdateData();
        clearRedisKeys();
    }

    private void clearRedisKeys() {
        try {
            redisDataAccessor.del(BomUtils.getRedisKey(BomCoreConstants.BOM_CORE_APPROVE_GROUP_KEY,actionContext.getTenantId(),Optional.ofNullable(arg.getObjectData()).map(ObjectDataDocument::getId).orElse("")));
            redisDataAccessor.del(BomUtils.getRedisKey(BomCoreConstants.BOM_CORE_APPROVE_BOM_GROUP_RELATE_ID_KEY,actionContext.getTenantId(),Optional.ofNullable(arg.getObjectData()).map(ObjectDataDocument::getId).orElse("")));
        } catch (Exception e) {
            log.warn(e.getMessage(),e);
        }
    }

    private void checkGroupRule() {
        Map<String, List<IObjectData>> addBomMap = detailsToAdd.stream().filter(x -> SFAPreDefineObject.Bom.getApiName().equals(x.getDescribeApiName())
                && StringUtils.isNotBlank(x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class))
                && x.get(BomConstants.FIELD_SELECTED_BY_DEFAULT, Boolean.class, false)
                && Objects.equals(EnumUtil.actionType.create.getValue(), x.get(ACTION_TYPE, String.class)))
                .collect(Collectors.groupingBy(o -> o.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)));
        Map<String, List<IObjectData>> updateBomMap = new HashMap<>();
        Set<String> updateIds = Sets.newHashSet();
        for (IObjectData iObjectData : detailsToUpdate) {
            if (SFAPreDefineObject.Bom.getApiName().equals(iObjectData.getDescribeApiName())
                    && StringUtils.isNotBlank(iObjectData.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class))
            ) {
                if (iObjectData.get(BomConstants.FIELD_SELECTED_BY_DEFAULT, Boolean.class, false)) {
                    updateBomMap.computeIfAbsent(iObjectData.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class), k -> new ArrayList<>()).add(iObjectData);
                }
                updateIds.add(iObjectData.getId());
            }
        }
        Set<String> deleteIds = detailsToDelete.stream().filter(o -> SFAPreDefineObject.Bom.getApiName().equals(o.getDescribeApiName()))
                .map(DBRecord::getId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        allGroupList.stream().filter(x -> StringUtils.isNotBlank(x.get(BomConstants.FIELD_MAX_PROD_COUNT, String.class))
                && StringUtils.isNotBlank(x.getId())).forEach(x -> {

            int count = addBomMap.getOrDefault(x.getId(), Lists.newArrayList()).size();
            List<IObjectData> updateDataList = updateBomMap.getOrDefault(x.getId(), Lists.newArrayList());
            List<IObjectData> allDataList = groupBomMap.getOrDefault(x.getId(), Lists.newArrayList());
            List<IObjectData> dbList = allDataList.stream().filter(d -> !updateIds.contains(d.getId()) && !deleteIds.contains(d.getId()) && d.get(BomConstants.FIELD_SELECTED_BY_DEFAULT, Boolean.class, false)).collect(Collectors.toList());
            if ((updateDataList.size() + dbList.size() + count) > Long.parseLong(x.get(BomConstants.FIELD_MAX_PROD_COUNT, String.class))) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_RULE_WARN, x.getName()));
            }
        });
    }

    private void handParam() {
        detailsToDelete = Lists.newArrayList();
        Iterator<IObjectData> iterator = detailsToUpdate.iterator();
        while (iterator.hasNext()) {
            IObjectData element = iterator.next();
            if (Objects.equals(element.get(ACTION_TYPE, String.class), EnumUtil.actionType.delete.getValue())) {
                iterator.remove();  // 删除元素
                detailsToDelete.add(element);
            }
        }
    }

    @Override
    protected void prepareDetailObjectData() {
        //将从对象的数据分成更新、删除、新增三种集合
        super.prepareDetailObjectData();
        IObjectDescribe describe = this.objectDescribes.get(Utils.BOM_API_NAME);
        if (describe != null) {
            ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe().ifPresent(masterDetailField -> {
                if (Boolean.TRUE.equals(masterDetailField.getIsRequiredWhenMasterCreate())) {
                    if (CollectionUtils.notEmpty(arg.getDetails()) || dbDetailDataMap.getOrDefault(Utils.BOM_API_NAME, Lists.newArrayList()).size() > 1) {
                        masterDetailField.setIsRequiredWhenMasterCreate(false);
                    }
                }
            });
        }
        List<IObjectData> dbBomList = dbDetailDataMap.getOrDefault(Utils.BOM_API_NAME, Lists.newArrayList());
        Map<String, Set<String>> bomMap = new HashMap<>();
        dealData(dbBomList, bomMap, groupBomMap, dbBomIdMap);
        handParam();
        handUpdateData(bomMap, dbBomIdMap);
        handDeletedData(bomMap, groupBomMap, dbBomIdMap);
    }

    private void dealData(List<IObjectData> dbBomList, Map<String, Set<String>> bomMap, Map<String, List<IObjectData>> groupBomMap, Map<String, IObjectData> bomIdMap) {
        for (IObjectData x : dbBomList) {
            if (!Objects.equals(x.getId(), x.get(BomConstants.FIELD_ROOT_ID, String.class))) {
                String path = x.get(BomConstants.FIELD_BOM_PATH, String.class);
                if (StringUtils.isNotBlank(path)) {
                    String s = StringUtils.substringAfter(path, ".");
                    String[] arr = s.split("\\."); // 使用 . 作为分隔符，需要使用转义符 \
                    for (int i = 0; i < arr.length - 1; i++) {
                        String key = arr[i];
                        Set<String> values = Sets.newHashSet();
                        for (int j = i + 1; j < arr.length; j++) {
                            values.add(arr[j]);
                        }
                        bomMap.computeIfAbsent(key, k -> new HashSet<>()).addAll(values);
                    }
                }
                String groupId = x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class);
                if (StringUtils.isNotBlank(groupId)) {
                    groupBomMap.computeIfAbsent(groupId, k -> new ArrayList<>()).add(x);
                }
                bomIdMap.put(x.getId(), x);
            }
        }
    }

    private void handUpdateData(Map<String, Set<String>> bomMap, Map<String, IObjectData> bomIdMap) {
        detailsToUpdate.removeIf(x -> !StringUtils.equals(x.get(ACTION_TYPE, String.class), EnumUtil.actionType.update.getValue()));
        Map<String, IObjectData> updateMap = detailsToUpdate.stream().filter(x -> SFAPreDefineObject.Bom.getApiName().equals(x.getDescribeApiName())).collect(Collectors.toMap(DBRecord::getId, o -> o, (o1, o2) -> o1));
        Set<String> updateIds = Sets.newHashSet();
        List<IObjectData> tmpDetailsToUpdate = Lists.newArrayList();
        for (IObjectData objectData : detailsToUpdate) {
            if (!SFAPreDefineObject.Bom.getApiName().equals(objectData.getDescribeApiName())) {
                continue;
            }
            IObjectData dbObj = bomIdMap.get(objectData.getId());
            if (Objects.isNull(dbObj)) {
                continue;
            }
            String bomPath = objectData.get(BomConstants.FIELD_BOM_PATH, String.class);
            if (StringUtils.isNotBlank(bomPath)) {
                noActiveNode.stream().filter(bomPath::contains).findFirst().ifPresent(x -> objectData.set(BomConstants.FIELD_ENABLED_STATUS, false));
            }
            if (Boolean.TRUE.equals(objectData.get(BomConstants.FIELD_IS_REQUIRED))
                    && !Boolean.TRUE.equals(dbObj.get(BomConstants.FIELD_IS_REQUIRED))) {
                objectData.set(BomConstants.FIELD_SELECTED_BY_DEFAULT, true);
                updateIds.add(objectData.getId());
            }
            objectData.set(BomConstants.FIELD_BOM_PATH, dbObj.get(BomConstants.FIELD_BOM_PATH));
            objectData.set(BomConstants.FIELD_PARENT_BOM_ID, dbObj.get(BomConstants.FIELD_PARENT_BOM_ID));
            objectData.set(BomConstants.FIELD_ROOT_ID, dbObj.get(BomConstants.FIELD_ROOT_ID));
            boolean priceEdit = objectData.get(BomConstants.FIELD_PRICE_EDITABLE, Boolean.class, true);
            boolean enableStatus = objectData.get(BomConstants.FIELD_ENABLED_STATUS, Boolean.class, true);
            boolean dbPriceEdit = dbObj.get(BomConstants.FIELD_PRICE_EDITABLE, Boolean.class, true);
            if ((!priceEdit && dbPriceEdit) || !enableStatus) {
                Set<String> childNodes = bomMap.getOrDefault(objectData.getId(), Sets.newHashSet());
                childNodes.forEach(d -> {
                    IObjectData updateData = updateMap.get(d);
                    if (Objects.nonNull(updateData)) {
                        if (!priceEdit && dbPriceEdit) {
                            updateData.set(BomConstants.FIELD_PRICE_EDITABLE, false);
                            Integer priceMode = updateData.get(BomConstants.FIELD_PRICE_MODE, Integer.class, 1);
                            if (EnumUtil.PriceMode.DEF.getValue() == priceMode) {
                                BigDecimal price = updateData.get(BomConstants.FIELD_ADJUST_PRICE, BigDecimal.class, BigDecimal.ZERO);
                                if (price.compareTo(BigDecimal.ZERO) != 0) {
                                    updateData.set(BomConstants.FIELD_AMOUNT_EDITABLE, false);
                                }
                            }
                        }
                        if (!enableStatus) {
                            updateData.set(BomConstants.FIELD_ENABLED_STATUS, false);
                        }
                    } else {
                        IObjectData dbObjectData = bomIdMap.get(d);
                        if (dbObjectData != null) {
                            boolean flag = false;
                            if (!priceEdit && dbPriceEdit) {
                                dbObjectData.set(BomConstants.FIELD_PRICE_EDITABLE, false);
                                Integer priceMode = dbObjectData.get(BomConstants.FIELD_PRICE_MODE, Integer.class, 1);
                                if (EnumUtil.PriceMode.DEF.getValue() == priceMode) {
                                    BigDecimal price = dbObjectData.get(BomConstants.FIELD_ADJUST_PRICE, BigDecimal.class, BigDecimal.ZERO);
                                    if (price.compareTo(BigDecimal.ZERO) != 0) {
                                        dbObjectData.set(BomConstants.FIELD_AMOUNT_EDITABLE, false);
                                    }
                                }
                                flag = true;
                            }
                            if (!enableStatus) {
                                dbObjectData.set(BomConstants.FIELD_ENABLED_STATUS, false);
                                flag = true;
                            }
                            if (flag) {
                                tmpDetailsToUpdate.add(dbObjectData);
                            }
                        }

                    }
                });
            }
        }
        if (CollectionUtils.notEmpty(tmpDetailsToUpdate)) {
            detailsToUpdate.addAll(tmpDetailsToUpdate);
        }
        if (CollectionUtils.notEmpty(updateIds)) {
            productStatusChangeTaskService.createOrUpdateTask(actionContext.getTenantId(), Lists.newArrayList(), Lists.newArrayList(updateIds));
        }
    }

    private void handDeletedData(Map<String, Set<String>> bomMap, Map<String, List<IObjectData>> groupBomMap, Map<String, IObjectData> bomIdMap) {
        List<IObjectData> tmpDetailsToDelete = Lists.newArrayList();
        Set<String> deleteChildGroupList = Sets.newHashSet();
        detailsToDelete.stream().filter(x -> Objects.equals(x.getDescribeApiName(), Utils.BOM_API_NAME)).forEach(x -> {
            Set<String> childNodes = bomMap.getOrDefault(x.getId(), Sets.newHashSet());
            dealDeletedDataList(bomIdMap, deleteChildGroupList, childNodes, tmpDetailsToDelete);
        });
        groupToDelete.stream().filter(x -> StringUtils.isNotBlank(x.getId()) && x.get("delete_child_bom", Boolean.class, true)).forEach(x -> {
            List<IObjectData> tmpDataList = groupBomMap.getOrDefault(x.getId(), Lists.newArrayList());
            if (CollectionUtils.notEmpty(tmpDataList)) {
                detailsToDelete.addAll(tmpDataList);
            }
            tmpDataList.forEach(d -> {
                Set<String> deletedIds = bomMap.getOrDefault(d.getId(), Sets.newHashSet());
                dealDeletedDataList(bomIdMap, deleteChildGroupList, deletedIds, tmpDetailsToDelete);
            });
        });
        if (CollectionUtils.notEmpty(tmpDetailsToDelete)) {
            detailsToDelete.addAll(tmpDetailsToDelete);
        }
        if (CollectionUtils.notEmpty(deleteChildGroupList)) {
            List<IObjectData> groupList = serviceFacade.findObjectDataByIdsIgnoreAll(actionContext.getTenantId(), Lists.newArrayList(deleteChildGroupList), Utils.PRODUCT_GROUP_API_NAME);
            if (CollectionUtils.notEmpty(groupList)) {
                groupToDelete.addAll(groupList);
            }
        }
    }

    private void dealDeletedDataList(Map<String, IObjectData> bomIdMap, Set<String> deleteChildGroupList, Set<String> childNodes, List<IObjectData> tmpDetailsToDelete) {
        childNodes.forEach(id -> {
            IObjectData objectData = bomIdMap.get(id);
            if (Objects.nonNull(objectData)) {
                tmpDetailsToDelete.add(objectData);
                String groupId = objectData.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class);
                if (StringUtils.isNotBlank(groupId)) {
                    deleteChildGroupList.add(groupId);
                }
            }
        });
    }


    private void checkQuantity() {
        List<IObjectData> dbBomList = dbDetailDataMap.getOrDefault(Utils.BOM_API_NAME, Lists.newArrayList());
        Set<IObjectData> deletedList = detailsToDelete.stream().filter(x -> StringUtils.equals(x.getDescribeApiName(), Utils.BOM_API_NAME)).collect(Collectors.toSet());
        Set<IObjectData> addList = detailsToAdd.stream().filter(x -> StringUtils.equals(x.getDescribeApiName(), Utils.BOM_API_NAME)).collect(Collectors.toSet());
        int total = dbBomList.size() + addList.size() - deletedList.size();
        bomCoreV3Service.checkQuantity(actionContext.getTenantId(), total);
    }
}