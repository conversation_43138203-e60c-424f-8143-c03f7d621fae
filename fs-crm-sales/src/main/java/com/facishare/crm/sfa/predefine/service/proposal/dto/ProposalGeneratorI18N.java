package com.facishare.crm.sfa.predefine.service.proposal.dto;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-23
 * ============================================================
 */
public interface ProposalGeneratorI18N {
    // 场景已存在，不可重复新建
    String CASE_OVERVIEW_SOURCE_EXISTS = "case.overview.source.exists";
    // 无该对象的场景配置数据，请联系「AI方案生成器」模块管理员配置该对象的场景配置数据后再进行使用。
    String CASE_OVERVIEW_SOURCE_NOT_EXISTS = "case.overview.source.not.exists";
    /**
     * AI Flow 触发失败
     */
    String SFA_AI_FLOW_TRIGGER_FAILED = "sfa.ai.flow.trigger.failed";
}
