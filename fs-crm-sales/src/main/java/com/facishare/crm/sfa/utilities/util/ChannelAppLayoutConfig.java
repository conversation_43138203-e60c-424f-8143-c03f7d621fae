package com.facishare.crm.sfa.utilities.util;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.model.PartnerChannelManage;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-07-09
 * ============================================================
 */
@Service
@Slf4j
public class ChannelAppLayoutConfig {
    private List<PartnerChannelManage.AppConfigLayout> appConfigLayout = Lists.newArrayList();
    private static final String CHANNEL_MODE = "channelMode";
    private static final String APPLY_TO_APP = "applyToApp";
    public static final String RELATED_BUSINESS_OBJECT = "relatedBusinessObject";

    @PostConstruct
    public void init() {
        //SFA配置文件
        ConfigFactory.getConfig("fs-prm-channel-layout", this::reload);
    }

    private void reload(IConfig config) {
        if (ObjectUtils.isEmpty(config.getString())) {
            log.warn("ChannelAppLayoutConfig#reload updateConfig is null,configName:{}", config.getName());
            return;
        }
        try {
            updateConfig(config.getString());
        } catch (Exception e) {
            log.error("reload updateConfig error,config name:{} ", config.getName(), e);
        }
    }

    private void updateConfig(String json) {
        this.appConfigLayout = JacksonUtils.fromJson(json, new TypeReference<List<PartnerChannelManage.AppConfigLayout>>() {});
    }

    public List<PartnerChannelManage.AppConfigLayout> getAppConfigLayout() {
        return appConfigLayout;
    }

    public PartnerChannelManage.AppConfigLayout getChannelMode() {
        AtomicReference<PartnerChannelManage.AppConfigLayout> build = new AtomicReference<>(PartnerChannelManage.AppConfigLayout.builder().build());
        Optional.ofNullable(appConfigLayout).orElse(Lists.newArrayList())
                .stream()
                .filter(d -> CHANNEL_MODE.equals(d.getApiName()))
                .findAny()
                .ifPresent(build::set
                );
        return build.get();
    }

    public PartnerChannelManage.AppConfigLayout getApplyToApp() {
        AtomicReference<PartnerChannelManage.AppConfigLayout> build = new AtomicReference<>(PartnerChannelManage.AppConfigLayout.builder().build());
        Optional.ofNullable(appConfigLayout).orElse(Lists.newArrayList())
                .stream()
                .filter(d -> APPLY_TO_APP.equals(d.getApiName()))
                .findAny()
                .ifPresent(build::set
                );
        return build.get();
    }

    public PartnerChannelManage.AppConfigLayout getRelatedBusinessObject() {
        AtomicReference<PartnerChannelManage.AppConfigLayout> build = new AtomicReference<>(PartnerChannelManage.AppConfigLayout.builder().build());
        Optional.ofNullable(appConfigLayout).orElse(Lists.newArrayList())
                .stream()
                .filter(d -> RELATED_BUSINESS_OBJECT.equals(d.getApiName()))
                .findAny()
                .ifPresent(build::set
                );
        return build.get();
    }
}
