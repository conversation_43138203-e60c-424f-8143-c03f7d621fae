package com.facishare.crm.sfa.predefine.service.proposal.dto;

import com.facishare.crm.sfa.utilities.proxy.model.OneFlowOfRemote;
import lombok.Data;

import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-30
 * ============================================================
 */
@Data
public class FlowInfoDTO {
    private String flowId;
    private String flowName;
    private Map<String, OneFlowOfRemote.FlowNode> flowNodeMapping;
}
