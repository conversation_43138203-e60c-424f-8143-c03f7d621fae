package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.PriceBookImportUtils;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

/**
 * <AUTHOR> 2019-11-05
 * @instruction
 */
public class PriceBookProductUpdateImportTemplateAction extends StandardUpdateImportTemplateAction {
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        super.customHeader(headerFieldList);
        headerFieldList.removeIf(f -> "product_id".equals(f.getApiName()));
        if (bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(actionContext.getTenantId())) {
            headerFieldList.removeIf(f -> "actual_unit".equals(f.getApiName()));
        }
        if (bizConfigThreadLocalCacheService.isOpenStratifiedPricing(actionContext.getTenantId())) {
            headerFieldList.removeIf(f -> "is_stratified_pricing".equals(f.getApiName()));
        }
        PriceBookImportUtils.removeCurrencyFields(actionContext.getTenantId(), headerFieldList);
    }
}
