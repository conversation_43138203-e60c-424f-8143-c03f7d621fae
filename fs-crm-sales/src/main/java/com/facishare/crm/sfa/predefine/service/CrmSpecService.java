package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.predefine.service.model.CrmDeletedSpecOrSpecValue;
import com.facishare.crm.sfa.utilities.util.ConcatenateSqlUtils;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypeRoleViewPojo;
import com.facishare.paas.appframework.metadata.dto.auth.RoleViewForWebPojo;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.crm.openapi.Utils.SPECIFICATION_API_NAME;
import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_ARG_CHECK_NULL;
import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_SPEC_CHECK_USED;

/**
 * 规格、规格值相关的service
 */
@ServiceModule("crm_spec")
@Component
@Slf4j
public class CrmSpecService {
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private RecordTypeLogicServiceImpl recordTypeService;
    @Autowired
    FunctionPrivilegeService functionPrivilegeService;


    /**
     * 规格、规格值是否被产品使用
     */
    @ServiceMethod("is_reference_spec_or_spec_value")
    public CrmDeletedSpecOrSpecValue.Result deletedSpecOrSpecValue(CrmDeletedSpecOrSpecValue.Arg arg, ServiceContext context) {
        log.info("SpuSkuService deletedSpecOrSpecValue arg{}:", arg);
        CrmDeletedSpecOrSpecValue.Result result = CrmDeletedSpecOrSpecValue.Result.builder().build();
        QueryResult<IObjectData> dataResult;
        String sql = ConcatenateSqlUtils.getReferenceSpecOrValueSql();
        try {
            if (!arg.getSpecList().isEmpty()) {
                sql = String.format(sql, context.getTenantId(), "spec_id", arg.getSpecList().stream().map(SqlEscaper::pg_escape).collect(Collectors.joining("','", "('", "')")));
                dataResult = objectDataService.findBySql(sql, context.getTenantId(), "SpuSkuSpecValueRelateObj");
                if (dataResult.getData().isEmpty()) {
                    result.setResult(Boolean.TRUE);
                } else {
                    throw new ValidateException(I18N.text(SO_SPEC_CHECK_USED));
                }
            } else if (!arg.getSpecValueList().isEmpty()) {
                sql = String.format(sql, context.getTenantId(), "spec_value_id", arg.getSpecValueList().stream().map(SqlEscaper::pg_escape).collect(Collectors.joining("','", "('", "')")));

                dataResult = objectDataService.findBySql(sql, context.getTenantId(), "SpuSkuSpecValueRelateObj");
                if (dataResult.getData().isEmpty()) {
                    result.setResult(Boolean.TRUE);
                } else {
                    throw new ValidateException(I18N.text(SO_SPEC_CHECK_USED));
                }

            } else {
                result.setMessage(I18N.text(SO_ARG_CHECK_NULL));
                result.setResult(Boolean.FALSE);
            }
        } catch (MetadataServiceException e) {
            log.error("SpuSkuService deletedSpecOrSpecValue findBySql sql:{}", sql, e);
            throw new MetaDataException(e.getMessage());
        }
        return result;
    }

    @ServiceMethod("init_many_unit_spec")
    public void initManyUnit(ServiceContext context) {
        updateDefaultRecodeTypeName(context.getUser());
        addUnitRecordType(context.getUser());
        System.out.println("11");
    }

    /**
     * 多单位开启的时候规格新增一个业务类型--单位
     */
    public void addUnitRecordType(User user) {
        RecordTypeRoleViewPojo pojo = new RecordTypeRoleViewPojo();
        pojo.setLabel(I18N.text(SFAI18NKeyUtil.SFA_CRM_SPEC_UNIT));
        pojo.setApi_name("unit_record_type__c");
        pojo.setDescription("");
        pojo.setIs_active(Boolean.TRUE);

        List<Map<String, String>> roleList = functionPrivilegeService.getRoleList(user);
        List<RoleViewForWebPojo> roles = roleList.stream().map(o -> {
            RoleViewForWebPojo roleViewForWebPojo = new RoleViewForWebPojo();
            roleViewForWebPojo.setRoleCode(o.get("roleCode"));
            roleViewForWebPojo.setIs_default(Boolean.FALSE);
            roleViewForWebPojo.setIs_used(Boolean.TRUE);
            roleViewForWebPojo.setLayout_api_name("SpecificationObj_layout_generate_by_UDObjectServer__c");
            return roleViewForWebPojo;
        }).collect(Collectors.toList());

        pojo.setRoles(roles);
        HashMap hashMap = new HashMap();
        hashMap.put("edit", 1);
        hashMap.put("enable", 0);
        hashMap.put("remove", 0);
        pojo.setConfig(hashMap);

        recordTypeService.createRecordType(user.getTenantId(), SPECIFICATION_API_NAME, pojo, user);
    }

    public void updateDefaultRecodeTypeName(User user) {
        RecordTypeResult recordTypeList = recordTypeService.findRecordTypeList(user.getTenantId(), SPECIFICATION_API_NAME, Boolean.FALSE);
        Map record = (Map) recordTypeList.getRecord_list().stream().filter(o -> "default__c".equals(((Map) o).get("api_name"))).findFirst().orElse(Maps.newHashMap());
        recordTypeService.updateRecordType(user, SPECIFICATION_API_NAME, getRecordTypeStr(record));
    }

    public String getRecordTypeStr(Map record) {
        return "{\"label\":\"" + I18N.text(SFAI18NKeyUtil.SFA_CRM_SPEC) + "\",\"api_name\":\"default__c\",\"description\":\"" + record.getOrDefault("description", "") + "\",\"is_active\":true}";
    }

}
