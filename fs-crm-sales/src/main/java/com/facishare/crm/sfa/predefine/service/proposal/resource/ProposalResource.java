package com.facishare.crm.sfa.predefine.service.proposal.resource;

import com.facishare.crm.sfa.predefine.service.proposal.service.ProposalGenerateService;
import com.facishare.crm.sfa.prm.platform.model.ApiResponse;
import com.facishare.crm.platform.converter.Converter;
import com.facishare.crm.sfa.predefine.service.proposal.dto.ProposalDTO;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.*;
import com.facishare.crm.sfa.predefine.service.proposal.dto.RequestId;
import com.facishare.crm.sfa.predefine.service.proposal.service.ProposalService;
import com.facishare.crm.sfa.predefine.service.proposal.vo.OperationResultVO;
import com.facishare.crm.sfa.predefine.service.proposal.vo.ProposalListVO;
import com.facishare.crm.sfa.predefine.service.proposal.vo.ProposalVO;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@ServiceModule("proposal")
@Component
@Slf4j
public class ProposalResource {
    @Resource
    private ProposalService proposalService;
    @Resource(name = "objectConverter")
    private Converter converter;
    @Resource
    ProposalGenerateService proposalGenerateService;

    @ServiceMethod("list")
    public ApiResponse<List<ProposalListVO>> list(ServiceContext serviceContext, ProposalListRequest request) {
        List<ProposalListVO> proposals = proposalService.list(serviceContext.getUser(), request.getRelatedObjectDescribe(), request.getRelatedDataId());
        return ApiResponse.success(proposals);
    }

    @ServiceMethod("quick-create")
    public ApiResponse<String> quickCreate(ServiceContext serviceContext, InitProposalRequest request) {
        ProposalDTO proposalDTO = converter.convertDTO(request, ProposalDTO.class);
        String dataId = proposalService.quickCreate(serviceContext.getUser(), proposalDTO);
        return ApiResponse.success(dataId);
    }

    @ServiceMethod("rename")
    public ApiResponse<OperationResultVO> rename(ServiceContext serviceContext, RenameRequest renameRequest) {
        boolean renamed = proposalService.rename(serviceContext.getUser(), renameRequest);
        OperationResultVO operationResultVO = OperationResultVO.builder().id(renameRequest.getId()).success(renamed).build();
        return ApiResponse.success(operationResultVO);
    }

    @ServiceMethod("delete")
    public ApiResponse<OperationResultVO> delete(ServiceContext serviceContext, RequestId requestId) {
        boolean deleted = proposalService.deleteProposal(serviceContext.getUser(), requestId.getId());
        OperationResultVO operationResultVO = OperationResultVO.builder().id(requestId.getId()).success(deleted).build();
        return ApiResponse.success(operationResultVO);
    }

    @ServiceMethod("query")
    public ApiResponse<ProposalVO> query(ServiceContext serviceContext, RequestId requestId) {
        ProposalVO proposal = proposalService.queryProposal(serviceContext.getUser(), requestId.getId());
        return ApiResponse.success(proposal);
    }

    @ServiceMethod("update")
    public ApiResponse<OperationResultVO> update(ServiceContext serviceContext, UpdateProposalRequest updateArg) {
        boolean updated = proposalService.updateProposalContent(serviceContext.getUser(), updateArg);
        OperationResultVO operationResultVO = OperationResultVO.builder().id(updateArg.getId()).success(updated).build();
        return ApiResponse.success(operationResultVO);
    }

    @ServiceMethod("set-ppt")
    public ApiResponse<OperationResultVO> setPPT(ServiceContext serviceContext, UpdatePPTIdRequest updateArg) {
        boolean updated = proposalService.setPPT(serviceContext.getUser(), updateArg);
        OperationResultVO operationResultVO = OperationResultVO.builder().id(updateArg.getId()).success(updated).build();
        return ApiResponse.success(operationResultVO);
    }
    @ServiceMethod("generate")
    public ApiResponse<OperationResultVO> generate(ServiceContext serviceContext, ProposalLLMRequest arg) {
        String id = proposalGenerateService.proposalLLM(serviceContext.getUser(), arg);
        OperationResultVO vo = OperationResultVO.builder().id(id).success(true).build();
        return ApiResponse.success(vo);
    }

}
