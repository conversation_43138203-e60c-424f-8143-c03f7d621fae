package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.BomCoreConstants;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.appframework.metadata.dto.RecordTypeLayoutStructure;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_ADD;
import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_EDIT;


public class BomCoreDescribeLayoutController extends SFADescribeLayoutController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        dealButton(newResult);
        if (Objects.isNull(formComponent)) {
            return newResult;
        }
        if (SFAConfigUtil.isSimpleBom(controllerContext.getTenantId())) {
            PreDefLayoutUtil.removeSomeFields(formComponent, Sets.newHashSet(BomCoreConstants.FIELD_CATEGORY));
        }
        if (Objects.equals(arg.getLayout_type(),LAYOUT_TYPE_EDIT)) {
            PreDefLayoutUtil.setFormComponentFieldReadOnly(formComponent, Lists.newArrayList(BomCoreConstants.FIELD_CATEGORY,BomCoreConstants.FIELD_PRODUCT_ID,BomCoreConstants.SALE_STRATEGY));
        }
        if(Objects.equals(Boolean.TRUE, arg.getInclude_detail_describe()) && (Objects.equals(arg.getLayout_type(),LAYOUT_TYPE_EDIT) || Objects.equals(arg.getLayout_type(),LAYOUT_TYPE_ADD))){
            removeBOMObjAbstractFields(newResult);
        }
        return newResult;
    }

    private void removeBOMObjAbstractFields(Result newResult) {
        List<DetailObjectListResult> detailObjectListResults = newResult.getDetailObjectList();
        if(CollectionUtils.isEmpty(detailObjectListResults)){
            return;
        }
        DetailObjectListResult detailObjectListResult = detailObjectListResults.stream().filter(x->Objects.equals(BomConstants.DESC_API_NAME, x.getObjectApiName())).findFirst().get();
        if(detailObjectListResult == null) {
            return;
        }
        List<RecordTypeLayoutStructure> layoutList = detailObjectListResult.getLayoutList();
        if(CollectionUtils.isEmpty(layoutList)){
            return;
        }
        for (RecordTypeLayoutStructure layout : layoutList) {
            LayoutExt.of(LayoutDocument.of(layout.getDetail_layout()).toLayout()).getFormComponent()
                    .map(FormComponentExt::getFormComponent)
                    .map(FormComponent.class::cast)
                    .ifPresent(formComponent -> PreDefLayoutUtil.removeSomeFields(formComponent, Sets.newHashSet("modified_adjust_price", "price_book_id")));
        }
    }


    private void dealButton(Result result) {
        ObjectDescribeDocument objectDescribe = result.getObjectDescribe();
        if (Objects.nonNull(objectDescribe)) {
            List<String> actionCodeList = Lists.newArrayList(
                    ObjectAction.CREATE_GROUP.getActionCode(),
                    ObjectAction.SET_GROUP.getActionCode());
            Map<String, Boolean> butMap = serviceFacade.funPrivilegeCheck(controllerContext.getUser(),
                    Utils.BOM_API_NAME, actionCodeList);
            actionCodeList.removeIf(x -> !butMap.get(x));
            objectDescribe.put("funButton", actionCodeList);
        }
    }

}
