package com.facishare.crm.sfa.utilities.proxy;

import com.facishare.crm.sfa.utilities.proxy.model.OneFlowOfRemote;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.HashMap;
import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-30
 * ============================================================
 */
@RestResource(value = "FlowRestResource", desc = "流程编排服务", contentType = "application/json")
public interface OneFlowClient {
    @POST(value = "/one/def/list", desc = "")
    OneFlowOfRemote.FlowListResponse list(@HeaderMap Map<String, String> headerMap, @Body OneFlowOfRemote.ListArg arg);

    @POST(value = "/one/trigger", desc = "")
    OneFlowOfRemote.TriggerResponse trigger(@HeaderMap Map<String, String> headerMap, @Body OneFlowOfRemote.Trigger triggerArg);

    static Map<String, String> getHeaders(String tenantId) {
        Map<String, String> headers = new HashMap<>();
        headers.put("x-tenant-id", tenantId);
        headers.put("x-user-id", User.SUPPER_ADMIN_USER_ID);
        return headers;
    }
}
