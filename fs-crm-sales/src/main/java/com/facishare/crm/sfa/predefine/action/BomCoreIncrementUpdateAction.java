package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.constant.BomCoreConstants;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.Set;


public class BomCoreIncrementUpdateAction extends StandardIncrementUpdateAction {
    private static final Set<String> FILTER_FIELD = Sets.newHashSet(BomCoreConstants.SALE_STRATEGY,BomCoreConstants.FIELD_PRODUCT_ID,BomCoreConstants.FIELD_CATEGORY,BomCoreConstants.FIELD_CORE_VERSION);


    @Override
    protected void before(Arg arg) {
        super.before(arg);
        ObjectDataExt.remove(Lists.newArray<PERSON>ist(objectData), FILTER_FIELD);
    }
}
