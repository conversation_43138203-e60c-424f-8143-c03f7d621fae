package com.facishare.crm.sfa.predefine.service.proposal.llm;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.platform.AssertValidator;
import com.facishare.crm.platform.async.TaskCallback;
import com.facishare.crm.platform.async.TaskResult;
import com.facishare.crm.platform.async.executor.AsyncBootstrap;
import com.facishare.crm.sfa.predefine.service.proposal.service.CaseOverviewService;
import com.facishare.crm.sfa.predefine.service.proposal.vo.CaseOverviewVO;
import com.facishare.crm.sfa.utilities.proxy.AiRestProxy;
import com.facishare.crm.sfa.utilities.proxy.model.AiRestProxyModel;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import java.util.function.Supplier;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-26
 * ============================================================
 */
@Service
@Slf4j
public class ProposalLLMService {
    @Resource(name = "aiRestProxy")
    private AiRestProxy aiRestProxy;
    @Resource
    private CaseOverviewService caseOverviewService;
    @Resource
    private AssertValidator assertValidator;
    @Resource
    private AsyncBootstrap asyncBootstrap;

    public String generate(User user, String caseOverviewId, String customPrompt) {
        CaseOverviewVO caseOverviewVO = caseOverviewService.queryCaseOverview(user, caseOverviewId);
        assertValidator.assertNotNull(caseOverviewVO);
        String content = caseOverviewVO.getContent();
        assertValidator.assertAllNotBlank(content);
        AiRestProxyModel.Arg arg = new AiRestProxyModel.Arg();
        arg.setApiName("prompt_ai_proposal");
        Map<String, Object> map = assembleSceneVariables(content);
        map.put("customPrompt", customPrompt);
        arg.setSceneVariables(map);
        AiRestProxyModel.Resposne response = aiRestProxy.completions(arg, AiRestProxy.getHeaders(user.getTenantId()));
        if (ObjectUtils.isEmpty(response) || response.getErrCode() != 0) {
            log.error("CaseOverviewLLMService aiRestProxy.completions response :{}", JSONObject.toJSONString(response));
            return "";
        }
        return response.getResult().getMessage();
    }

    private Map<String, Object> assembleSceneVariables(String caseOverviewContent) {
        Map<String, Object> sceneVariablesMapping = new HashMap<>(1);
        sceneVariablesMapping.put("caseOverview", caseOverviewContent);
        return sceneVariablesMapping;
    }

    public String generateName(User user, String content) {
        assertValidator.assertAllNotBlank(content);
        AiRestProxyModel.Arg arg = new AiRestProxyModel.Arg();
        arg.setApiName("prompt_proposal_name");
        Map<String, Object> map = assembleSceneVariables(content);
        arg.setSceneVariables(map);
        final String[] proposalName = new String[1];
        try {
            Map<String, Supplier<Object>> tasks = new HashMap<>();
            tasks.put("generateName", () -> aiRestProxy.completions(arg, AiRestProxy.getHeaders(user.getTenantId())));
            asyncBootstrap.submitTasks(tasks, 11000, new TaskCallback() {
                @Override
                public void onSuccess(TaskResult result) {
                    AiRestProxyModel.Resposne response = result.get("generateName", AiRestProxyModel.Resposne.class);
                    if (ObjectUtils.isEmpty(response) || response.getErrCode() != 0) {
                        log.error("CaseOverviewLLMService generateName response :{}", response);
                        proposalName[0] = "untitled name";
                    } else {
                        proposalName[0] = response.getResult().getMessage();
                    }
                }
            });
        } catch (TimeoutException e) {
            log.warn("CaseOverviewLLMService generateName timeout", e);
            return "untitled name";
        } catch (Exception e) {
            log.error("CaseOverviewLLMService generateName error", e);
            return "untitled name";
        }
        return proposalName[0];
    }
}
