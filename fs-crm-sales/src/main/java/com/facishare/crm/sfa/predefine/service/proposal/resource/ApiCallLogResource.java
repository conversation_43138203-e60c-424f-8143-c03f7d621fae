package com.facishare.crm.sfa.predefine.service.proposal.resource;

import com.facishare.crm.sfa.prm.platform.model.ApiResponse;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.ApiCallLogListRequest;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.ApiCallLogRequest;
import com.facishare.crm.sfa.predefine.service.proposal.service.ApiCallLogService;
import com.facishare.crm.sfa.predefine.service.proposal.vo.ApiCallRecordListVO;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@ServiceModule("api-log")
@Component
@Slf4j
public class ApiCallLogResource {
    @Resource
    private ApiCallLogService apiCallLogService;

    @ServiceMethod("save")
    public ApiResponse<Boolean> saveLog(ServiceContext serviceContext, ApiCallLogRequest logRequest) {
        if (logRequest == null) {
            return ApiResponse.error(400, "param is required");
        }
        apiCallLogService.saveLog(serviceContext.getUser(), logRequest);
        return ApiResponse.success(true);
    }

    @ServiceMethod("call")
    public ApiResponse<Boolean> callLog(ServiceContext serviceContext, ApiCallLogRequest logRequest) {
        if (logRequest == null) {
            return ApiResponse.error(400, "param is required");
        }
        apiCallLogService.callLog(serviceContext.getUser(), logRequest);
        return ApiResponse.success(true);
    }

    @ServiceMethod("list")
    public ApiResponse<List<ApiCallRecordListVO>> list(ServiceContext serviceContext, ApiCallLogListRequest request) {
        List<ApiCallRecordListVO> apiCallRecordList = apiCallLogService.listByBiz(serviceContext.getUser(), request.getBizSource(), request.getPlatform());
        return ApiResponse.success(apiCallRecordList);
    }

}
