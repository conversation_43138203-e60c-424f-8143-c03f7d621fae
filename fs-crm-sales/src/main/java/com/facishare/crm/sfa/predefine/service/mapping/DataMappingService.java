package com.facishare.crm.sfa.predefine.service.mapping;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.predefine.service.MultiSourceCommonService;
import com.facishare.crm.sfa.predefine.service.mapping.model.MappingByApiName;
import com.facishare.crm.sfa.predefine.service.mapping.model.MappingData;
import com.facishare.crm.sfa.predefine.service.mapping.model.MappingDataByRuleName;
import com.facishare.crm.sfa.predefine.service.mapping.model.MappingRuleModel;
import com.facishare.crm.sfa.utilities.proxy.CRMMetaDataServiceProxy;
import com.facishare.crm.sfa.utilities.util.FieldUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.i18n.SalesOrderI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.service.ObjectDesignerService;
import com.facishare.paas.appframework.core.predef.service.ObjectOptionDependenceService;
import com.facishare.paas.appframework.core.predef.service.ObjectOptionSetService;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.AddDescribeCustomField;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.DeleteRule;
import com.facishare.paas.appframework.core.predef.service.dto.option.SaveOptionSet;
import com.facishare.paas.appframework.core.predef.service.dto.option.dependence.CreateOptionDependence;
import com.facishare.paas.appframework.metadata.ObjectMappingService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IObjectMappingRuleDetailInfo;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@ServiceModule("data_mapping")
@Component
@Slf4j
public class DataMappingService {
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ObjectOptionSetService objectOptionSetService;
    @Autowired
    private ObjectDesignerService objectDesignerService;
    @Autowired
    private ObjectOptionDependenceService objectOptionDependenceService;
    @Autowired
    private CRMMetaDataServiceProxy crmMetaDataServiceProxy;
    @Autowired
    private MultiSourceCommonService multiSourceCommonService;

    @ServiceMethod("mapping")
    public MappingData.Result mapping(ServiceContext serviceContext, MappingData.Arg arg) {
        if (null == arg) {
            throw new ValidateException(I18N.text("paas.udobj.param_error"));
        }
        String sourceName = arg.getSourceApiName();
        String targetName = arg.getTargetApiName();
        if (Strings.isNullOrEmpty(sourceName) || Strings.isNullOrEmpty(targetName)) {
            throw new ValidateException(I18N.text("paas.udobj.param_error"));
        }
        String ruleName = Strings.isNullOrEmpty(arg.getRuleName())
                ? DataMappingConstants.PREDEFINE_MAPPING_RULE_NAME.get(sourceName.concat(targetName))
                : arg.getRuleName();
        if (null == ruleName) {
            throw new ValidateException(I18N.text("paas.udobj.param_error"));
        }
        IObjectData masterData;
        if (null == arg.getMasterData()) {
            masterData = new ObjectData();
            masterData.setTenantId(serviceContext.getTenantId());
            masterData.setDescribeApiName(sourceName);
        } else {
            masterData = arg.getMasterData().toObjectData();
			if (masterData.getTenantId() == null) {
				masterData.setTenantId(serviceContext.getTenantId());
			}
			if (masterData.getDescribeApiName() == null) {
				masterData.setDescribeApiName(sourceName);
			}
        }
        ObjectMappingService.MappingDataArg mappingDataArg = new ObjectMappingService.MappingDataArg();
        mappingDataArg.setRuleApiName(ruleName);
        mappingDataArg.setObjectData(masterData);
        Map<String, List<IObjectData>> detailDataMap = arg.toDetailDataMap();
        detailDataMap.forEach((k, v) -> v.forEach(d -> d.setTenantId(serviceContext.getTenantId())));
        mappingDataArg.setDetails(detailDataMap);
        ObjectMappingService.MappingDataResult mappingDataResult = objectMappingService.mappingData(serviceContext.getUser(), mappingDataArg);
        MappingData.Result result = new MappingData.Result();
        result.setObjectData(ObjectDataDocument.of(mappingDataResult.getObjectData()));
        if (CollectionUtils.notEmpty(mappingDataResult.getDetails())) {
            Map<String, List<ObjectDataDocument>> detailData = Maps.newHashMap();
            mappingDataResult.getDetails().forEach((k, v) -> {
                detailData.put(k, ObjectDataDocument.ofList(v));
            });
            result.setDetails(detailData);
        }
        Map<String, List<String>> mappingFields = Maps.newHashMap();
        List<IObjectMappingRuleInfo> ruleInfos = objectMappingService.findByApiName(serviceContext.getUser(), ruleName);
        if (CollectionUtils.notEmpty(ruleInfos)) {
            for (IObjectMappingRuleInfo ruleInfo : ruleInfos) {
                List<IObjectMappingRuleDetailInfo> fieldMapping = ruleInfo.getFieldMapping();
                if (CollectionUtils.notEmpty(fieldMapping)) {
                    List<String> fields = fieldMapping.stream().map(r -> r.getTargetFieldName()).collect(Collectors.toList());
                    mappingFields.put(ruleInfo.getTargetApiName(), fields);
                }
            }
        }
        result.setFields(mappingFields);
        return result;
    }

    /**
     * 订货通使用，从对象可能属于多个主对象
     * 选第一个有master_detail数据的从对象，获取这个从对象的主对象，作为参数里的主对象
     */
    @ServiceMethod("get_mappings")
    public MappingData.Result getMappings(ServiceContext serviceContext, MappingData.Arg arg) {
        if (CollectionUtils.empty(arg.getDetailDataMap())) {
            return new MappingData.Result();
        }

        List<String> detailApiNames = Lists.newArrayList(arg.getDetailDataMap().keySet());
        if (CollectionUtils.empty(detailApiNames)) {
            return new MappingData.Result();
        }
        String detailApiName = detailApiNames.get(0);
        String masterDetailFieldApiName = FieldUtil.getMasterDetailFieldApiName(serviceContext.getTenantId(), detailApiName, arg.getSourceApiName());
        if (Strings.isNullOrEmpty(masterDetailFieldApiName)) {
            String msg = I18N.text(SalesOrderI18NKeyUtil.SFA_OBJECT1_HAS_NO_MASTER_DETAIL_FIELD_TO_OBJECT2, detailApiName, arg.getSourceApiName());
            throw new ValidateException(msg);
        }

        List<ObjectDataDocument> detailDatas = arg.getDetailDataMap().get(detailApiName);
        String masterDetailFieldValue = getMasterDetailFieldValue(detailDatas, masterDetailFieldApiName);
        if (Strings.isNullOrEmpty(masterDetailFieldValue)) {
            String msg = I18N.text(SalesOrderI18NKeyUtil.SFA_DETAIL_OBJECT_FIELD_HAS_NO_VALUE, masterDetailFieldApiName);
            throw new ValidateException(msg);
        }

        //获取主对象数据
        List<IObjectData> masterDatas = serviceFacade.findObjectDataByIdsIgnoreAll(serviceContext.getTenantId(), Lists.newArrayList(masterDetailFieldValue), arg.getSourceApiName());
        if (CollectionUtils.empty(masterDatas)) {
            String msg = I18N.text(SalesOrderI18NKeyUtil.SFA_OBJECT_DATA_NOT_FOUND, masterDetailFieldValue);
            throw new ValidateException(msg);
        }

        IObjectData masterData = masterDatas.get(0);
        arg.setMasterData(ObjectDataDocument.of(masterData));

        return mapping(serviceContext, arg);
    }

    @ServiceMethod("get_mappings_by_rule_name")
    public MappingDataByRuleName.Result getMappingsByRuleName(ServiceContext serviceContext, MappingDataByRuleName.Arg arg) {
        if (null == arg) {
            throw new ValidateException(I18N.text("paas.udobj.param_error"));
        }
        String sourceName = arg.getSourceApiName();
        String targetName = arg.getTargetApiName();
        if (Strings.isNullOrEmpty(sourceName) || Strings.isNullOrEmpty(targetName)) {
            throw new ValidateException(I18N.text("paas.udobj.param_error"));
        }
        String ruleName = arg.getRuleName();
        if (null == ruleName) {
            throw new ValidateException(I18N.text("paas.udobj.param_error"));
        }
        if (CollectionUtils.empty(arg.getObjectDatas())) {
            throw new ValidateException(I18N.text("paas.udobj.param_error"));
        }

        MappingDataByRuleName.Result result = new MappingDataByRuleName.Result();

        List<IObjectMappingRuleInfo> rules = objectMappingService.findByApiName(serviceContext.getUser(), ruleName);
        if (CollectionUtils.empty(rules)) {
            return result;
        }
        List<ObjectDataDocument> mappingDataResult = Lists.newArrayList();
        IObjectMappingRuleInfo ruleInfo = rules.get(0);
        for (ObjectDataDocument objectDataDocument : arg.getObjectDatas()) {
            ObjectMappingService.MappingDataArg mappingDataArg = new ObjectMappingService.MappingDataArg();
            mappingDataArg.setRuleApiName(ruleName);
            mappingDataArg.setObjectData(objectDataDocument.toObjectData());
            ObjectMappingService.MappingDataResult mappingData = objectMappingService.mappingData(serviceContext.getUser(), mappingDataArg);
            mappingDataResult.add(ObjectDataDocument.of(mappingData.getObjectData()));
        }

        result.setObjectData(mappingDataResult);

        Map<String, List<String>> mappingFields = Maps.newHashMap();
        List<IObjectMappingRuleDetailInfo> fieldMapping = ruleInfo.getFieldMapping();
        if (CollectionUtils.notEmpty(fieldMapping)) {
            List<String> fields = fieldMapping.stream().map(r -> r.getTargetFieldName()).collect(Collectors.toList());
            mappingFields.put(ruleInfo.getTargetApiName(), fields);
        }
        result.setFields(mappingFields);

        return result;
    }

    private String getMasterDetailFieldValue(List<ObjectDataDocument> detailDatas, String masterDetailFieldApiName) {
        if (CollectionUtils.empty(detailDatas)) {
            return null;
        }

        for (ObjectDataDocument detailData : detailDatas) {
            String value = (String) detailData.get(masterDetailFieldApiName);
            if (!Strings.isNullOrEmpty(value)) {
                return value;
            }
        }

        return null;
    }

    /**
     *  线索添加映射规则、放开1、2级行业灰度配置
     * @param serviceContext
     * @param arg
     * @return
     */
    @ServiceMethod("initIndustryLevelByLeadsObjOfOptionSet")
    public MappingData.Result initIndustryLevelByLeadsObjOfOptionSet(ServiceContext serviceContext, MappingData.Arg arg) {
        /**
         * 1.批量新建通用字符集
         * 2.给线索描述中添加字段
         * 3.创建字段的依赖关系
         * 4.布局中添加字段
         * 5.线索转换为客户的映射关系
         */
        //1
        SaveOptionSet.Arg optionSet = new SaveOptionSet.Arg();
        String body = "{ \"option\": { \"label\": \"一级行业\",\"define_type\": \"package\", \"api_name\": \"option_h1ir0__c\", \"description\": \"\", \"options\": [ { \"label\": \"农、林、牧、渔业\", \"value\": \"MfO2aDzo1\", \"not_usable\": false, \"fe_key\": \"MfO2aDzo1_MfO2aDzo1\", \"font_color\": \"#181c25\" }, { \"label\": \"采矿业\", \"value\": \"oY38F8e12\", \"not_usable\": false, \"fe_key\": \"oY38F8e12_oY38F8e12\", \"font_color\": \"#181c25\" }, { \"label\": \"制造业\", \"value\": \"h7oDN8Vbn\", \"not_usable\": false, \"fe_key\": \"h7oDN8Vbn_h7oDN8Vbn\", \"font_color\": \"#181c25\" }, { \"label\": \"电力、热力、燃气及水生产和供应业\", \"value\": \"e34hK3CrE\", \"not_usable\": false, \"fe_key\": \"e34hK3CrE_e34hK3CrE\", \"font_color\": \"#181c25\" }, { \"label\": \"建筑业\", \"value\": \"G06S9GaEQ\", \"not_usable\": false, \"fe_key\": \"G06S9GaEQ_G06S9GaEQ\", \"font_color\": \"#181c25\" }, { \"label\": \"批发和零售业\", \"value\": \"xhj1bVR31\", \"not_usable\": false, \"fe_key\": \"xhj1bVR31_xhj1bVR31\", \"font_color\": \"#181c25\" }, { \"label\": \"交通运输、仓储和邮政业\", \"value\": \"At2dwudpS\", \"not_usable\": false, \"fe_key\": \"At2dwudpS_At2dwudpS\", \"font_color\": \"#181c25\" }, { \"label\": \"住宿和餐饮业\", \"value\": \"VC1exe4eS\", \"not_usable\": false, \"fe_key\": \"VC1exe4eS_VC1exe4eS\", \"font_color\": \"#181c25\" }, { \"label\": \"信息传输、软件和信息技术服务业\", \"value\": \"6wrsk1T8x\", \"not_usable\": false, \"fe_key\": \"6wrsk1T8x_6wrsk1T8x\", \"font_color\": \"#181c25\" }, { \"label\": \"金融业\", \"value\": \"s49O92tu0\", \"not_usable\": false, \"fe_key\": \"s49O92tu0_s49O92tu0\", \"font_color\": \"#181c25\" }, { \"label\": \"房地产业\", \"value\": \"lSIhX1Y24\", \"not_usable\": false, \"fe_key\": \"lSIhX1Y24_lSIhX1Y24\", \"font_color\": \"#181c25\" }, { \"label\": \"租赁和商务服务业\", \"value\": \"tNzVSxH1e\", \"not_usable\": false, \"fe_key\": \"tNzVSxH1e_tNzVSxH1e\", \"font_color\": \"#181c25\" }, { \"label\": \"科学研究和技术服务业\", \"value\": \"2Tl8x0GfB\", \"not_usable\": false, \"fe_key\": \"2Tl8x0GfB_2Tl8x0GfB\", \"font_color\": \"#181c25\" }, { \"label\": \"水利、环境和公共设施管理业\", \"value\": \"1rXMphDY6\", \"not_usable\": false, \"fe_key\": \"1rXMphDY6_1rXMphDY6\", \"font_color\": \"#181c25\" }, { \"label\": \"居民服务、修理和其他服务业\", \"value\": \"i12pW9E4j\", \"not_usable\": false, \"fe_key\": \"i12pW9E4j_i12pW9E4j\", \"font_color\": \"#181c25\" }, { \"label\": \"教育\", \"value\": \"gt903b1c2\", \"not_usable\": false, \"fe_key\": \"gt903b1c2_gt903b1c2\", \"font_color\": \"#181c25\" }, { \"label\": \"卫生和社会工作\", \"value\": \"Almx5FMEo\", \"not_usable\": false, \"fe_key\": \"Almx5FMEo_Almx5FMEo\", \"font_color\": \"#181c25\" }, { \"label\": \"文化、体育和娱乐业\", \"value\": \"qDtJ814Nl\", \"not_usable\": false, \"fe_key\": \"qDtJ814Nl_qDtJ814Nl\", \"font_color\": \"#181c25\" }, { \"label\": \"公共管理、社会保障和社会组织\", \"value\": \"ulQVZhy7a\", \"not_usable\": false, \"fe_key\": \"ulQVZhy7a_ulQVZhy7a\", \"font_color\": \"#181c25\" }, { \"label\": \"国际组织\", \"value\": \"7uWY1qs56\", \"not_usable\": false, \"fe_key\": \"7uWY1qs56_7uWY1qs56\", \"font_color\": \"#181c25\" } ] }}";// ignoreI18n
        optionSet = JSON.parseObject(body, SaveOptionSet.Arg.class);
        SaveOptionSet.Result result = objectOptionSetService.createOptionSet(optionSet,serviceContext);
        if(ObjectUtils.isEmpty(result)){
//            throw new ValidateException("批量新建通用字符集--一级行业 失败！！");
            String msg = I18N.text(SFAI18NKeyUtil.BATCH_CREATE_UNIVERSAL_CHARACTER_SET) + "--"
                    + I18N.text("BizQuerySearchObj.field.categoryFirst.label") + " "
                    + I18N.text("sfa.fail") + "!!";
            throw new ValidateException(msg);
        }
         body = "{ \"option\": { \"label\": \"二级行业\",\"define_type\": \"package\", \"api_name\": \"option_kZ61D__c\", \"description\": \"\", \"options\": [ { \"label\": \"农业\", \"value\": \"pxFT5Z8H9\", \"not_usable\": false, \"fe_key\": \"pxFT5Z8H9_pxFT5Z8H9\", \"font_color\": \"#181c25\" }, { \"label\": \"林业\", \"value\": \"a2EdgWy03\", \"not_usable\": false, \"fe_key\": \"a2EdgWy03_a2EdgWy03\", \"font_color\": \"#181c25\" }, { \"label\": \"畜牧业\", \"value\": \"72t1WFHEj\", \"not_usable\": false, \"fe_key\": \"72t1WFHEj_72t1WFHEj\", \"font_color\": \"#181c25\" }, { \"label\": \"渔业\", \"value\": \"Un7m9yy18\", \"not_usable\": false, \"fe_key\": \"Un7m9yy18_Un7m9yy18\", \"font_color\": \"#181c25\" }, { \"label\": \"农、林、牧、渔专业及辅助性活动\", \"value\": \"5KQjgduEu\", \"not_usable\": false, \"fe_key\": \"5KQjgduEu_5KQjgduEu\", \"font_color\": \"#181c25\" }, { \"label\": \"煤炭开采和洗选业\", \"value\": \"zslOo5uPO\", \"not_usable\": false, \"fe_key\": \"zslOo5uPO_zslOo5uPO\", \"font_color\": \"#181c25\" }, { \"label\": \"石油和天然气开采业\", \"value\": \"CydqnxLcp\", \"not_usable\": false, \"fe_key\": \"CydqnxLcp_CydqnxLcp\", \"font_color\": \"#181c25\" }, { \"label\": \"黑色金属矿采选业\", \"value\": \"r51JYtlKj\", \"not_usable\": false, \"fe_key\": \"r51JYtlKj_r51JYtlKj\", \"font_color\": \"#181c25\" }, { \"label\": \"有色金属矿采选业\", \"value\": \"bq8dIPOG4\", \"not_usable\": false, \"fe_key\": \"bq8dIPOG4_bq8dIPOG4\", \"font_color\": \"#181c25\" }, { \"label\": \"非金属矿采选业\", \"value\": \"dDa21mxY2\", \"not_usable\": false, \"fe_key\": \"dDa21mxY2_dDa21mxY2\", \"font_color\": \"#181c25\" }, { \"label\": \"开采专业及辅助性活动\", \"value\": \"DtQjla1Eo\", \"not_usable\": false, \"fe_key\": \"DtQjla1Eo_DtQjla1Eo\", \"font_color\": \"#181c25\" }, { \"label\": \"其他采矿业\", \"value\": \"VBm7oe31G\", \"not_usable\": false, \"fe_key\": \"VBm7oe31G_VBm7oe31G\", \"font_color\": \"#181c25\" }, { \"label\": \"农副食品加工业\", \"value\": \"8GH61KlLk\", \"not_usable\": false, \"fe_key\": \"8GH61KlLk_8GH61KlLk\", \"font_color\": \"#181c25\" }, { \"label\": \"食品制造业\", \"value\": \"PDAs3wowQ\", \"not_usable\": false, \"fe_key\": \"PDAs3wowQ_PDAs3wowQ\", \"font_color\": \"#181c25\" }, { \"label\": \"酒、饮料和精制茶制造业\", \"value\": \"3O57epppA\", \"not_usable\": false, \"fe_key\": \"3O57epppA_3O57epppA\", \"font_color\": \"#181c25\" }, { \"label\": \"烟草制品业\", \"value\": \"hW7CXY50Y\", \"not_usable\": false, \"fe_key\": \"hW7CXY50Y_hW7CXY50Y\", \"font_color\": \"#181c25\" }, { \"label\": \"纺织业\", \"value\": \"22Vp8j5m0\", \"not_usable\": false, \"fe_key\": \"22Vp8j5m0_22Vp8j5m0\", \"font_color\": \"#181c25\" }, { \"label\": \"纺织服装、服饰业\", \"value\": \"e94z9TfVU\", \"not_usable\": false, \"fe_key\": \"e94z9TfVU_e94z9TfVU\", \"font_color\": \"#181c25\" }, { \"label\": \"皮革、毛皮、羽毛及其制品和制鞋业\", \"value\": \"R2Fe1zcNh\", \"not_usable\": false, \"fe_key\": \"R2Fe1zcNh_R2Fe1zcNh\", \"font_color\": \"#181c25\" }, { \"label\": \"木材加工和木、竹、藤、棕、草制品业\", \"value\": \"QhH96QsI5\", \"not_usable\": false, \"fe_key\": \"QhH96QsI5_QhH96QsI5\", \"font_color\": \"#181c25\" }, { \"label\": \"家具制造业\", \"value\": \"otiM1Y8Cy\", \"not_usable\": false, \"fe_key\": \"otiM1Y8Cy_otiM1Y8Cy\", \"font_color\": \"#181c25\" }, { \"label\": \"造纸和纸制品业\", \"value\": \"7cf1Wwt2v\", \"not_usable\": false, \"fe_key\": \"7cf1Wwt2v_7cf1Wwt2v\", \"font_color\": \"#181c25\" }, { \"label\": \"印刷和记录媒介复制业\", \"value\": \"f4zSH0xb1\", \"not_usable\": false, \"fe_key\": \"f4zSH0xb1_f4zSH0xb1\", \"font_color\": \"#181c25\" }, { \"label\": \"文教、工美、体育和娱乐用品制造业\", \"value\": \"0VhN4w35z\", \"not_usable\": false, \"fe_key\": \"0VhN4w35z_0VhN4w35z\", \"font_color\": \"#181c25\" }, { \"label\": \"石油、煤炭及其他燃料加工业\", \"value\": \"iq4TM2zmi\", \"not_usable\": false, \"fe_key\": \"iq4TM2zmi_iq4TM2zmi\", \"font_color\": \"#181c25\" }, { \"label\": \"化学原料和化学制品制造业\", \"value\": \"WPH51njhv\", \"not_usable\": false, \"fe_key\": \"WPH51njhv_WPH51njhv\", \"font_color\": \"#181c25\" }, { \"label\": \"医药制造业\", \"value\": \"y2rFe6lrE\", \"not_usable\": false, \"fe_key\": \"y2rFe6lrE_y2rFe6lrE\", \"font_color\": \"#181c25\" }, { \"label\": \"化学纤维制造业\", \"value\": \"2A4V56p00\", \"not_usable\": false, \"fe_key\": \"2A4V56p00_2A4V56p00\", \"font_color\": \"#181c25\" }, { \"label\": \"橡胶和塑料制品业\", \"value\": \"0vQutx9T4\", \"not_usable\": false, \"fe_key\": \"0vQutx9T4_0vQutx9T4\", \"font_color\": \"#181c25\" }, { \"label\": \"非金属矿物制品业\", \"value\": \"ciVrEzxMc\", \"not_usable\": false, \"fe_key\": \"ciVrEzxMc_ciVrEzxMc\", \"font_color\": \"#181c25\" }, { \"label\": \"黑色金属冶炼和压延加工业\", \"value\": \"MXisfNHJp\", \"not_usable\": false, \"fe_key\": \"MXisfNHJp_MXisfNHJp\", \"font_color\": \"#181c25\" }, { \"label\": \"有色金属冶炼和压延加工业\", \"value\": \"5xU907qZX\", \"not_usable\": false, \"fe_key\": \"5xU907qZX_5xU907qZX\", \"font_color\": \"#181c25\" }, { \"label\": \"金属制品业\", \"value\": \"j0Sf2oX1U\", \"not_usable\": false, \"fe_key\": \"j0Sf2oX1U_j0Sf2oX1U\", \"font_color\": \"#181c25\" }, { \"label\": \"通用设备制造业\", \"value\": \"7asCZkV6I\", \"not_usable\": false, \"fe_key\": \"7asCZkV6I_7asCZkV6I\", \"font_color\": \"#181c25\" }, { \"label\": \"专用设备制造业\", \"value\": \"89DRKuo1Z\", \"not_usable\": false, \"fe_key\": \"89DRKuo1Z_89DRKuo1Z\", \"font_color\": \"#181c25\" }, { \"label\": \"汽车制造业\", \"value\": \"vt0w4011w\", \"not_usable\": false, \"fe_key\": \"vt0w4011w_vt0w4011w\", \"font_color\": \"#181c25\" }, { \"label\": \"铁路、船舶、航空航天和其他运输设备制造业\", \"value\": \"1gS91lSga\", \"not_usable\": false, \"fe_key\": \"1gS91lSga_1gS91lSga\", \"font_color\": \"#181c25\" }, { \"label\": \"电气机械和器材制造业\", \"value\": \"r2Nm0xM53\", \"not_usable\": false, \"fe_key\": \"r2Nm0xM53_r2Nm0xM53\", \"font_color\": \"#181c25\" }, { \"label\": \"计算机、通信和其他电子设备制造业\", \"value\": \"a1ssBf7QM\", \"not_usable\": false, \"fe_key\": \"a1ssBf7QM_a1ssBf7QM\", \"font_color\": \"#181c25\" }, { \"label\": \"仪器仪表制造业\", \"value\": \"1238yygph\", \"not_usable\": false, \"fe_key\": \"1238yygph_1238yygph\", \"font_color\": \"#181c25\" }, { \"label\": \"其他制造业\", \"value\": \"f7329iCdK\", \"not_usable\": false, \"fe_key\": \"f7329iCdK_f7329iCdK\", \"font_color\": \"#181c25\" }, { \"label\": \"废弃资源综合利用业\", \"value\": \"j9iye81N4\", \"not_usable\": false, \"fe_key\": \"j9iye81N4_j9iye81N4\", \"font_color\": \"#181c25\" }, { \"label\": \"金属制品、机械和设备修理业\", \"value\": \"1yJ73F8pm\", \"not_usable\": false, \"fe_key\": \"1yJ73F8pm_1yJ73F8pm\", \"font_color\": \"#181c25\" }, { \"label\": \"电力、热力生产和供应业\", \"value\": \"51F5tLX30\", \"not_usable\": false, \"fe_key\": \"51F5tLX30_51F5tLX30\", \"font_color\": \"#181c25\" }, { \"label\": \"燃气生产和供应业\", \"value\": \"iOSE0Kp93\", \"not_usable\": false, \"fe_key\": \"iOSE0Kp93_iOSE0Kp93\", \"font_color\": \"#181c25\" }, { \"label\": \"水的生产和供应业\", \"value\": \"2pIGMv1w6\", \"not_usable\": false, \"fe_key\": \"2pIGMv1w6_2pIGMv1w6\", \"font_color\": \"#181c25\" }, { \"label\": \"房屋建筑业\", \"value\": \"4jF60dvEB\", \"not_usable\": false, \"fe_key\": \"4jF60dvEB_4jF60dvEB\", \"font_color\": \"#181c25\" }, { \"label\": \"土木工程建筑业\", \"value\": \"pCaSio6y5\", \"not_usable\": false, \"fe_key\": \"pCaSio6y5_pCaSio6y5\", \"font_color\": \"#181c25\" }, { \"label\": \"建筑安装业\", \"value\": \"j0u2K5to5\", \"not_usable\": false, \"fe_key\": \"j0u2K5to5_j0u2K5to5\", \"font_color\": \"#181c25\" }, { \"label\": \"建筑装饰、装修和其他建筑业\", \"value\": \"XvBqrDZAU\", \"not_usable\": false, \"fe_key\": \"XvBqrDZAU_XvBqrDZAU\", \"font_color\": \"#181c25\" }, { \"label\": \"批发业\", \"value\": \"Xvk20i4sJ\", \"not_usable\": false, \"fe_key\": \"Xvk20i4sJ_Xvk20i4sJ\", \"font_color\": \"#181c25\" }, { \"label\": \"零售业\", \"value\": \"Y7GFRfy5N\", \"not_usable\": false, \"fe_key\": \"Y7GFRfy5N_Y7GFRfy5N\", \"font_color\": \"#181c25\" }, { \"label\": \"铁路运输业\", \"value\": \"8sgX2j28E\", \"not_usable\": false, \"fe_key\": \"8sgX2j28E_8sgX2j28E\", \"font_color\": \"#181c25\" }, { \"label\": \"道路运输业\", \"value\": \"D24Q2jfvO\", \"not_usable\": false, \"fe_key\": \"D24Q2jfvO_D24Q2jfvO\", \"font_color\": \"#181c25\" }, { \"label\": \"水上运输业\", \"value\": \"v44s6g2tb\", \"not_usable\": false, \"fe_key\": \"v44s6g2tb_v44s6g2tb\", \"font_color\": \"#181c25\" }, { \"label\": \"航空运输业\", \"value\": \"3lZFH1r1z\", \"not_usable\": false, \"fe_key\": \"3lZFH1r1z_3lZFH1r1z\", \"font_color\": \"#181c25\" }, { \"label\": \"管道运输业\", \"value\": \"1KcrU9wqi\", \"not_usable\": false, \"fe_key\": \"1KcrU9wqi_1KcrU9wqi\", \"font_color\": \"#181c25\" }, { \"label\": \"多式联运和运输代理业\", \"value\": \"yUpU0CMlt\", \"not_usable\": false, \"fe_key\": \"yUpU0CMlt_yUpU0CMlt\", \"font_color\": \"#181c25\" }, { \"label\": \"装卸搬运和仓储业\", \"value\": \"4x4Jr1dku\", \"not_usable\": false, \"fe_key\": \"4x4Jr1dku_4x4Jr1dku\", \"font_color\": \"#181c25\" }, { \"label\": \"邮政业\", \"value\": \"r3254RVah\", \"not_usable\": false, \"fe_key\": \"r3254RVah_r3254RVah\", \"font_color\": \"#181c25\" }, { \"label\": \"住宿业\", \"value\": \"dO85SFj2M\", \"not_usable\": false, \"fe_key\": \"dO85SFj2M_dO85SFj2M\", \"font_color\": \"#181c25\" }, { \"label\": \"餐饮业\", \"value\": \"a0471KDzg\", \"not_usable\": false, \"fe_key\": \"a0471KDzg_a0471KDzg\", \"font_color\": \"#181c25\" }, { \"label\": \"电信、广播电视和卫星传输服务\", \"value\": \"pU2x5391N\", \"not_usable\": false, \"fe_key\": \"pU2x5391N_pU2x5391N\", \"font_color\": \"#181c25\" }, { \"label\": \"互联网和相关服务\", \"value\": \"W1QnfZ358\", \"not_usable\": false, \"fe_key\": \"W1QnfZ358_W1QnfZ358\", \"font_color\": \"#181c25\" }, { \"label\": \"软件和信息技术服务业\", \"value\": \"YkVr6TsLB\", \"not_usable\": false, \"fe_key\": \"YkVr6TsLB_YkVr6TsLB\", \"font_color\": \"#181c25\" }, { \"label\": \"货币金融服务\", \"value\": \"l9aSzS26v\", \"not_usable\": false, \"fe_key\": \"l9aSzS26v_l9aSzS26v\", \"font_color\": \"#181c25\" }, { \"label\": \"资本市场服务\", \"value\": \"2h5mA682S\", \"not_usable\": false, \"fe_key\": \"2h5mA682S_2h5mA682S\", \"font_color\": \"#181c25\" }, { \"label\": \"保险业\", \"value\": \"ovCJqdmd0\", \"not_usable\": false, \"fe_key\": \"ovCJqdmd0_ovCJqdmd0\", \"font_color\": \"#181c25\" }, { \"label\": \"其他金融业\", \"value\": \"D6901E0u3\", \"not_usable\": false, \"fe_key\": \"D6901E0u3_D6901E0u3\", \"font_color\": \"#181c25\" }, { \"label\": \"房地产业\", \"value\": \"xV1hPDyMs\", \"not_usable\": false, \"fe_key\": \"xV1hPDyMs_xV1hPDyMs\", \"font_color\": \"#181c25\" }, { \"label\": \"租赁业\", \"value\": \"omn36erl1\", \"not_usable\": false, \"fe_key\": \"omn36erl1_omn36erl1\", \"font_color\": \"#181c25\" }, { \"label\": \"商务服务业\", \"value\": \"dbq2ERa4j\", \"not_usable\": false, \"fe_key\": \"dbq2ERa4j_dbq2ERa4j\", \"font_color\": \"#181c25\" }, { \"label\": \"研究和试验发展\", \"value\": \"Rxr4MbRdP\", \"not_usable\": false, \"fe_key\": \"Rxr4MbRdP_Rxr4MbRdP\", \"font_color\": \"#181c25\" }, { \"label\": \"专业技术服务业\", \"value\": \"MfP0UaKuO\", \"not_usable\": false, \"fe_key\": \"MfP0UaKuO_MfP0UaKuO\", \"font_color\": \"#181c25\" }, { \"label\": \"科技推广和应用服务业\", \"value\": \"43e65syTS\", \"not_usable\": false, \"fe_key\": \"43e65syTS_43e65syTS\", \"font_color\": \"#181c25\" }, { \"label\": \"水利管理业\", \"value\": \"d1Ugm1aIA\", \"not_usable\": false, \"fe_key\": \"d1Ugm1aIA_d1Ugm1aIA\", \"font_color\": \"#181c25\" }, { \"label\": \"生态保护和环境治理业\", \"value\": \"WQw7wKf1t\", \"not_usable\": false, \"fe_key\": \"WQw7wKf1t_WQw7wKf1t\", \"font_color\": \"#181c25\" }, { \"label\": \"公共设施管理业\", \"value\": \"k6xDvl56m\", \"not_usable\": false, \"fe_key\": \"k6xDvl56m_k6xDvl56m\", \"font_color\": \"#181c25\" }, { \"label\": \"土地管理业\", \"value\": \"6ah4bvqAb\", \"not_usable\": false, \"fe_key\": \"6ah4bvqAb_6ah4bvqAb\", \"font_color\": \"#181c25\" }, { \"label\": \"居民服务业\", \"value\": \"i3uqdpqSW\", \"not_usable\": false, \"fe_key\": \"i3uqdpqSW_i3uqdpqSW\", \"font_color\": \"#181c25\" }, { \"label\": \"机动车、电子产品和日用产品修理业\", \"value\": \"86J1gYzbZ\", \"not_usable\": false, \"fe_key\": \"86J1gYzbZ_86J1gYzbZ\", \"font_color\": \"#181c25\" }, { \"label\": \"其他服务业\", \"value\": \"1v1b7cc2y\", \"not_usable\": false, \"fe_key\": \"1v1b7cc2y_1v1b7cc2y\", \"font_color\": \"#181c25\" }, { \"label\": \"教育\", \"value\": \"272jITO4c\", \"not_usable\": false, \"fe_key\": \"272jITO4c_272jITO4c\", \"font_color\": \"#181c25\" }, { \"label\": \"卫生\", \"value\": \"id0J4c0kb\", \"not_usable\": false, \"fe_key\": \"id0J4c0kb_id0J4c0kb\", \"font_color\": \"#181c25\" }, { \"label\": \"社会工作\", \"value\": \"H12v0loeh\", \"not_usable\": false, \"fe_key\": \"H12v0loeh_H12v0loeh\", \"font_color\": \"#181c25\" }, { \"label\": \"新闻和出版业\", \"value\": \"la7vC1R9V\", \"not_usable\": false, \"fe_key\": \"la7vC1R9V_la7vC1R9V\", \"font_color\": \"#181c25\" }, { \"label\": \"广播、电视、电影和录音制作业\", \"value\": \"E13554vaZ\", \"not_usable\": false, \"fe_key\": \"E13554vaZ_E13554vaZ\", \"font_color\": \"#181c25\" }, { \"label\": \"文化艺术业\", \"value\": \"98r5jlLaC\", \"not_usable\": false, \"fe_key\": \"98r5jlLaC_98r5jlLaC\", \"font_color\": \"#181c25\" }, { \"label\": \"体育\", \"value\": \"z1uFz641p\", \"not_usable\": false, \"fe_key\": \"z1uFz641p_z1uFz641p\", \"font_color\": \"#181c25\" }, { \"label\": \"娱乐业\", \"value\": \"uB0ayCwaa\", \"not_usable\": false, \"fe_key\": \"uB0ayCwaa_uB0ayCwaa\", \"font_color\": \"#181c25\" }, { \"label\": \"中国共产党机关\", \"value\": \"8m1DRkceW\", \"not_usable\": false, \"fe_key\": \"8m1DRkceW_8m1DRkceW\", \"font_color\": \"#181c25\" }, { \"label\": \"国家机构\", \"value\": \"reucxySd1\", \"not_usable\": false, \"fe_key\": \"reucxySd1_reucxySd1\", \"font_color\": \"#181c25\" }, { \"label\": \"人民政协、民主党派\", \"value\": \"oBetfnJir\", \"not_usable\": false, \"fe_key\": \"oBetfnJir_oBetfnJir\", \"font_color\": \"#181c25\" }, { \"label\": \"社会保障\", \"value\": \"iOf2lNyA8\", \"not_usable\": false, \"fe_key\": \"iOf2lNyA8_iOf2lNyA8\", \"font_color\": \"#181c25\" }, { \"label\": \"群众团体、社会团体和其他成员组织\", \"value\": \"0u5Q71WB5\", \"not_usable\": false, \"fe_key\": \"0u5Q71WB5_0u5Q71WB5\", \"font_color\": \"#181c25\" }, { \"label\": \"基层群众自治组织\", \"value\": \"2eFqZsfdk\", \"not_usable\": false, \"fe_key\": \"2eFqZsfdk_2eFqZsfdk\", \"font_color\": \"#181c25\" }, { \"label\": \"国际组织\", \"value\": \"Cz2fE4l8l\", \"not_usable\": false, \"fe_key\": \"Cz2fE4l8l_Cz2fE4l8l\", \"font_color\": \"#181c25\" } ] }}";// ignoreI18n
        optionSet = JSON.parseObject(body, SaveOptionSet.Arg.class);
         result = objectOptionSetService.createOptionSet(optionSet,serviceContext);
        if(ObjectUtils.isEmpty(result)){
//            throw new ValidateException("批量新建通用字符集--二级行业 失败！！");
            String msg = I18N.text(SFAI18NKeyUtil.BATCH_CREATE_UNIVERSAL_CHARACTER_SET) + "--"
                    + I18N.text("BizQuerySearchObj.field.categorySecond.label") + " "
                    + I18N.text("sfa.fail") + "!!";
            throw new ValidateException(msg);
        }

        //2
        AddDescribeCustomField.Arg describeCustomFieldArg = new AddDescribeCustomField.Arg();
        body = "{ \"M1\": \"LeadsObj\", \"M4\": \"[]\", \"M2\": \"{\\\"is_index\\\": true, \\\"is_active\\\": true, \\\"is_unique\\\": false, \\\"label\\\": \\\"1级行业\\\", \\\"type\\\": \\\"select_one\\\", \\\"is_need_convert\\\": false, \\\"is_required\\\": false, \\\"api_name\\\": \\\"industry_level1\\\",\\\"option_type\\\": \\\"general\\\",\\\"option_api_name\\\": \\\"option_h1ir0__c\\\",\\\"options\\\": [],\\\"define_type\\\": \\\"package\\\", \\\"is_index_field\\\": false, \\\"is_single\\\": false, \\\"status\\\": \\\"released\\\"}\"}";// ignoreI18n
        describeCustomFieldArg = JSON.parseObject(body, AddDescribeCustomField.Arg.class);
        AddDescribeCustomField.Result describeResult = objectDesignerService.addDescribeCustomField(describeCustomFieldArg,serviceContext);
        if(ObjectUtils.isEmpty(describeResult)){
//            throw new ValidateException("给线索描述中添加字段--一级行业 失败！！");
            String msg = I18N.text(SFAI18NKeyUtil.ADD_FIELDS_TO_THE_CLUE_DESCRIPTION) + "--"
                    + I18N.text("BizQuerySearchObj.field.categoryFirst.label") + " "
                    + I18N.text("sfa.fail") + "!!";
            throw new ValidateException(msg);
        }
        body = "{ \"M1\": \"LeadsObj\", \"M4\": \"[]\", \"M2\": \"{ \\\"is_index\\\": true, \\\"is_active\\\": true, \\\"is_unique\\\": false, \\\"label\\\": \\\"2级行业\\\", \\\"type\\\": \\\"select_one\\\", \\\"is_need_convert\\\": false, \\\"is_required\\\": false, \\\"api_name\\\": \\\"industry_level2\\\",\\\"option_type\\\": \\\"general\\\",\\\"option_api_name\\\": \\\"option_kZ61D__c\\\",\\\"options\\\": [], \\\"define_type\\\": \\\"package\\\", \\\"is_index_field\\\": false, \\\"is_single\\\": false, \\\"status\\\": \\\"released\\\"}\"}";// ignoreI18n
        describeCustomFieldArg = JSON.parseObject(body, AddDescribeCustomField.Arg.class);
        describeResult = objectDesignerService.addDescribeCustomField(describeCustomFieldArg,serviceContext);
        if(ObjectUtils.isEmpty(describeResult)){
//            throw new ValidateException("给线索描述中添加字段--二级行业 失败！！");
            String msg = I18N.text(SFAI18NKeyUtil.ADD_FIELDS_TO_THE_CLUE_DESCRIPTION) + "--"
                    + I18N.text("BizQuerySearchObj.field.categorySecond.label") + " "
                    + I18N.text("sfa.fail") + "!!";
            throw new ValidateException(msg);
        }

        //3
        CreateOptionDependence.Arg optionDependenceArg = new CreateOptionDependence.Arg();
        body = "{ \"fieldDependence\": { \"describeApiName\": \"LeadsObj\", \"fieldApiName\": \"industry_level1\", \"childFieldName\": \"industry_level2\", \"dependence\": [ { \"value\": \"MfO2aDzo1\", \"childOptions\": [ \"pxFT5Z8H9\", \"a2EdgWy03\", \"72t1WFHEj\", \"Un7m9yy18\", \"5KQjgduEu\" ] }, { \"value\": \"oY38F8e12\", \"childOptions\": [ \"zslOo5uPO\", \"CydqnxLcp\", \"r51JYtlKj\", \"bq8dIPOG4\", \"dDa21mxY2\", \"DtQjla1Eo\", \"VBm7oe31G\" ] }, { \"value\": \"h7oDN8Vbn\", \"childOptions\": [ \"8GH61KlLk\", \"PDAs3wowQ\", \"3O57epppA\", \"hW7CXY50Y\", \"22Vp8j5m0\", \"e94z9TfVU\", \"R2Fe1zcNh\",\"QhH96QsI5\", \"otiM1Y8Cy\", \"7cf1Wwt2v\", \"f4zSH0xb1\", \"0VhN4w35z\", \"iq4TM2zmi\", \"WPH51njhv\", \"y2rFe6lrE\", \"2A4V56p00\", \"0vQutx9T4\", \"ciVrEzxMc\", \"MXisfNHJp\", \"5xU907qZX\", \"j0Sf2oX1U\", \"7asCZkV6I\", \"89DRKuo1Z\", \"vt0w4011w\", \"1gS91lSga\", \"r2Nm0xM53\", \"a1ssBf7QM\", \"1238yygph\", \"f7329iCdK\", \"j9iye81N4\", \"1yJ73F8pm\" ] }, { \"value\": \"e34hK3CrE\", \"childOptions\": [ \"51F5tLX30\", \"iOSE0Kp93\", \"2pIGMv1w6\" ] }, { \"value\": \"G06S9GaEQ\", \"childOptions\": [ \"4jF60dvEB\", \"pCaSio6y5\", \"j0u2K5to5\", \"XvBqrDZAU\" ] }, { \"value\": \"xhj1bVR31\", \"childOptions\": [ \"Xvk20i4sJ\", \"Y7GFRfy5N\" ] }, { \"value\": \"At2dwudpS\", \"childOptions\": [ \"8sgX2j28E\", \"D24Q2jfvO\", \"v44s6g2tb\", \"3lZFH1r1z\", \"1KcrU9wqi\", \"yUpU0CMlt\", \"4x4Jr1dku\", \"r3254RVah\" ] }, { \"value\": \"VC1exe4eS\", \"childOptions\": [ \"dO85SFj2M\", \"a0471KDzg\" ] }, { \"value\": \"6wrsk1T8x\", \"childOptions\": [ \"pU2x5391N\", \"W1QnfZ358\", \"YkVr6TsLB\" ] }, { \"value\": \"s49O92tu0\", \"childOptions\": [ \"l9aSzS26v\", \"2h5mA682S\", \"ovCJqdmd0\", \"D6901E0u3\" ] }, { \"value\": \"lSIhX1Y24\", \"childOptions\": [ \"xV1hPDyMs\" ] }, { \"value\": \"tNzVSxH1e\", \"childOptions\": [ \"omn36erl1\", \"dbq2ERa4j\" ] }, { \"value\": \"2Tl8x0GfB\", \"childOptions\": [ \"Rxr4MbRdP\", \"MfP0UaKuO\", \"43e65syTS\" ] }, { \"value\": \"1rXMphDY6\", \"childOptions\": [ \"d1Ugm1aIA\", \"WQw7wKf1t\", \"k6xDvl56m\", \"6ah4bvqAb\" ] }, { \"value\": \"i12pW9E4j\", \"childOptions\": [ \"i3uqdpqSW\", \"86J1gYzbZ\", \"1v1b7cc2y\" ] }, { \"value\": \"gt903b1c2\", \"childOptions\": [ \"272jITO4c\" ] }, { \"value\": \"Almx5FMEo\", \"childOptions\": [ \"id0J4c0kb\", \"H12v0loeh\" ] }, { \"value\": \"qDtJ814Nl\", \"childOptions\": [ \"la7vC1R9V\", \"E13554vaZ\", \"98r5jlLaC\", \"z1uFz641p\", \"uB0ayCwaa\" ] }, { \"value\": \"ulQVZhy7a\", \"childOptions\": [ \"8m1DRkceW\", \"reucxySd1\", \"oBetfnJir\", \"iOf2lNyA8\", \"0u5Q71WB5\", \"2eFqZsfdk\" ] }, { \"value\": \"7uWY1qs56\", \"childOptions\": [ \"Cz2fE4l8l\" ] } ] }}";
        optionDependenceArg = JSON.parseObject(body, CreateOptionDependence.Arg.class);
        CreateOptionDependence.Result optionDependenceResult = objectOptionDependenceService.createOptionDependence(serviceContext,optionDependenceArg);
        if(ObjectUtils.isEmpty(optionDependenceResult)){
//            throw new ValidateException("创建字段的依赖关系 失败！！");
            String msg = I18N.text(SFAI18NKeyUtil.CREATE_DEPENDENCY_RELATIONSHIPS_FOR_FIELDS) + " " + I18N.text("sfa.fail");
            throw new ValidateException(msg);
        }

        //4
        body = String.format("{\"tenantIds\":[\"%s\"],\"refreshAllNet\":false,\"describeApiNames\":[\"LeadsObj\"]}", serviceContext.getTenantId());
        crmMetaDataServiceProxy.add_industryLevel_field_to_layout(body);

        //5
        body = String.format("{\"tenantIds\":[\"%s\"], \"ruleApiNames\":[\"rule_leadsobj2accountobj_update__c\"], \"refreshAllNet\":false}", serviceContext.getTenantId());
        crmMetaDataServiceProxy.addFieldMappingOfLeads(body);
        return null;
    }

    @ServiceMethod("findByApiName")
    public MappingByApiName.Result findByApiName(ServiceContext serviceContext, MappingByApiName.Arg arg) {
        if (arg == null || StringUtils.isBlank(arg.getRuleApiName())) {
            return MappingByApiName.Result.builder().build();
        }
        List<IObjectMappingRuleInfo> ruleList = objectMappingService.findByApiName(serviceContext.getUser(), arg.getRuleApiName());
        return MappingByApiName.Result.builder().ruleList(ruleList).build();
    }

    @ServiceMethod("create_or_update_rule")
    public MappingRuleModel.Result createOrUpdateRule(ServiceContext serviceContext, MappingRuleModel.Arg arg) {
        multiSourceCommonService.createOrUpdateRule(serviceContext, arg.getCreateRuleArg(), arg.getBizType());
        return MappingRuleModel.Result.builder().success(true).build();
    }
    @ServiceMethod("delete_rule")
    public MappingRuleModel.Result createOrUpdateRule(ServiceContext serviceContext, DeleteRule.Arg arg) {
        multiSourceCommonService.deleteRule(serviceContext, arg);
        return MappingRuleModel.Result.builder().success(true).build();
    }
}
