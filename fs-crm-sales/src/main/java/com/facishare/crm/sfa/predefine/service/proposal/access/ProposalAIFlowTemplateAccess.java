package com.facishare.crm.sfa.predefine.service.proposal.access;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.lto.utils.Safes;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Component
@Slf4j
public class ProposalAIFlowTemplateAccess {
    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Resource
    private ObjectDataAccess objectDataAccess;
    @Resource
    private ServiceFacade serviceFacade;

    private static final String PROPOSAL_AI_FLOW_TEMPLATE_OBJ = "ProposalAIFlowTemplateObj";

    public IObjectData create(User user, IObjectData objectData) {
        return objectDataAccess.create(user, objectData, PROPOSAL_AI_FLOW_TEMPLATE_OBJ);
    }

    public IObjectData delete(User user, String id) {
        IObjectData data = metaDataFindServiceExt.findObjectByIdIgnoreAll(user.getTenantId(), id, PROPOSAL_AI_FLOW_TEMPLATE_OBJ);
        if (data == null) {
            return null;
        }
        List<IObjectData> dataList = serviceFacade.bulkDeleteDirect(Lists.newArrayList(data), user);
        return Safes.first(dataList);
    }

    public IObjectData queryById(User user, String id) {
        return metaDataFindServiceExt.findObjectByIdIgnoreAll(user, id, PROPOSAL_AI_FLOW_TEMPLATE_OBJ);
    }

    public void updateByFields(User user, IObjectData originalData, Set<String> fieldApiNames) {
        objectDataAccess.updateByFields(user, originalData, fieldApiNames);
    }

    public List<IObjectData> list(User user) {
        return metaDataFindServiceExt.findAllObjectIgnoreAll(user, PROPOSAL_AI_FLOW_TEMPLATE_OBJ);
    }
}
