package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.PriceBookImportUtils;
import com.facishare.crm.sfa.utilities.validator.PriceBookImportValidator;
import com.facishare.crm.sfa.utilities.validator.PriceBookValidator;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2019-11-05
 * @instruction
 */
public class PriceBookProductUpdateImportDataAction extends StandardUpdateImportDataAction {
    @Override
    protected void validReferenceID(List<ImportError> errorList, Map<IFieldDescribe, List<String>> defObjMap) {
        if (CollectionUtils.notEmpty(defObjMap)) {
            defObjMap.keySet().removeIf(iFieldDescribe -> "product_id".equals(iFieldDescribe.getApiName()));
        }
        super.validReferenceID(errorList, defObjMap);
    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        List<ImportError> errorList = Lists.newArrayList();
        PriceBookImportValidator.pricebookSpecialDeal(actionContext.getUser(), dataList, errorList);
        PriceBookImportValidator.validatePriceRange(errorList, dataList);
        //如增加其他校验，请放到该校验之前
        PriceBookValidator.validatePriceBookProductRepeat(errorList, dataList, objectDescribe, actionContext, arg, allErrorList);
        mergeErrorList(errorList);
    }

    @Override
    protected void customDefaultValue(List<IObjectData> validList) {
        PriceBookImportUtils.handleCurrency(actionContext.getTenantId(), validList);
        super.customDefaultValue(validList);
    }
}
