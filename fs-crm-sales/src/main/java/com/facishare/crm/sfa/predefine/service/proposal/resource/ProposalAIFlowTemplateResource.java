package com.facishare.crm.sfa.predefine.service.proposal.resource;

import com.facishare.crm.sfa.prm.platform.model.ApiResponse;
import com.facishare.crm.sfa.predefine.service.proposal.dto.*;
import com.facishare.crm.sfa.predefine.service.proposal.dto.request.UpdateProposalFlowRequest;
import com.facishare.crm.sfa.predefine.service.proposal.service.ProposalAIFlowTemplateService;
import com.facishare.crm.sfa.predefine.service.proposal.vo.*;
import com.facishare.crm.sfa.utilities.proxy.model.OneFlowOfRemote;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@ServiceModule("proposal-flow")
@Component
@Slf4j
public class ProposalAIFlowTemplateResource {
    @Resource
    private ProposalAIFlowTemplateService proposalAIFlowTemplateService;

    @ServiceMethod("create")
    public ApiResponse<ProposalAIFlowTemplateVO> create(ServiceContext serviceContext, ProposalAIFlowTemplateDTO proposalAIFlowTemplate) {
        ProposalAIFlowTemplateVO proposalAIFlowTemplateVo = proposalAIFlowTemplateService.createFlowTemplate(serviceContext.getUser(), proposalAIFlowTemplate);
        return ApiResponse.success(proposalAIFlowTemplateVo);
    }

    @ServiceMethod("delete")
    public ApiResponse<OperationResultVO> delete(ServiceContext serviceContext, RequestId requestId) {
        boolean deleted = proposalAIFlowTemplateService.deleteFlowTemplate(serviceContext.getUser(), requestId.getId());
        OperationResultVO operationResultVo = OperationResultVO
                .builder()
                .id(requestId.getId())
                .success(deleted)
                .build();
        return ApiResponse.success(operationResultVo);
    }

    @ServiceMethod("update")
    public ApiResponse<OperationResultVO> update(ServiceContext serviceContext, UpdateProposalFlowRequest updateCaseSourceRequest) {
        boolean updated = proposalAIFlowTemplateService.updateFlowTemplate(serviceContext.getUser(), updateCaseSourceRequest);
        OperationResultVO operationResultVo = OperationResultVO
                .builder()
                .id(updateCaseSourceRequest.getId())
                .success(updated)
                .build();
        return ApiResponse.success(operationResultVo);
    }

    @ServiceMethod("query")
    public ApiResponse<ProposalAIFlowTemplateVO> query(ServiceContext serviceContext, RequestId requestId) {
        ProposalAIFlowTemplateVO proposalAIFlowTemplateVo = proposalAIFlowTemplateService.queryFlowTemplate(serviceContext.getUser(), requestId.getId());
        return ApiResponse.success(proposalAIFlowTemplateVo);
    }

    @ServiceMethod("list")
    public ApiResponse<List<ProposalAIFlowTemplateListVO>> list(ServiceContext serviceContext) {
        List<ProposalAIFlowTemplateListVO> caseOverviewSources = proposalAIFlowTemplateService.queryFlowTemplates(serviceContext.getUser());
        return ApiResponse.success(caseOverviewSources);
    }

    @ServiceMethod("flow-list")
    public ApiResponse<List<OneFlowOfRemote.Flow>> flowList(ServiceContext serviceContext) {
        List<OneFlowOfRemote.Flow> result = proposalAIFlowTemplateService.flowList(serviceContext.getUser());
        return ApiResponse.success(result);
    }
}
