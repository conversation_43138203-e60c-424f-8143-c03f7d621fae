package com.facishare.crm.sfa.predefine.service.model;

import com.facishare.paas.I18N;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;

import java.util.List;

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.ProductCategoryI18N.*;

/**
 * <AUTHOR>
 * @date 2021/11/30 15:45
 * @IgnoreI18n
 */
public interface ProductCategoryModel {
    class Filed {
        public final static String PID = "pid";
        public final static String CATEGORY_NAME = "name";
        public static final String PARENT_CODE = "parent_code";
        public static final String PARENT_NAME = "partner_name";
        public static final String PCODE = "pcode";
        public static final String CODE = "code";
        public static final String PRODUCT_CATEGORY_PATH = "product_category_path";
        public static final String CATEGORY_CODE = "category_code";
        public static final String PRODUCT_CATEGORY_PATH_ID = "product_category_path_id";
        public static final String PRODUCT_CATEGORY_PATH_NAME = "product_category_path_name";
        public static final String CATEGORY_NAME__R = "category_name__r";
        public static final String ORDER_FIELD = "order_field";
        public static final String CATEGORY = "category";
        public static final String HAS_CHILDREN = "has_children";
        /**
         * 商品、产品查找关联产品分类
         */
        public static final String PRODUCT_CATEGORY_ID__R = "product_category_id__r";
        public static final String PRODUCT_CATEGORY_NAME__R = "product_category_name__r";
        public static final String PRODUCT_CATEGORY_ID = "product_category_id";
        public static final String SHOP_CATEGORY_ID = "shop_category_id";
        public static final String SHOP_CATEGORY = "shop_category";
        public static final String PRODUCT_CATEGORY_ID_SEARCH = "product_category_id_search";
        public static final String SHOP_CATEGORY_RECORD_TYPE = "shop_category__c";
        /**
         * 价目表明细引用产品分类
         */
        public static final String PRODUCT_CATEGORY = "product_category";
        public static final String PRODUCT_CATEGORY__V = "product_category__v";

        public static final String CATEGORY_PATH = "category_path";

        public static final List<String> LIST_DEFAULT_FIELDS = Lists.newArrayList(DBRecord.ID, PID, CATEGORY_NAME, CODE, ORDER_FIELD, CATEGORY_CODE,
                IObjectData.DESCRIBE_API_NAME, "record_type");
    }

    class Metadata {
        public final static String API_NAME = "ProductCategoryObj";
        public final static String CATEGORY_CUSTOM_DISPLAY_FIELDS = "category_custom_display_fields";
    }

    class ProductCategoryI18N {
        public static final String PRODUCT_CATEGORY_EDIT = "product_category_edit";
        public static final String PRODUCT_CATEGORY_DELETE = "product_category_delete";
        public static final String PRODUCT_CATEGORY_ADD_CHILD = "product_category_add_child";
        public static final String PRODUCT_CATEGORY_ADD_SAME = "product_category_add_same";
        public static final String PRODUCT_CATEGORY_SET_ATTRIBUTE = "product_category_set_attribute";
        public static final String PRODUCT_CATEGORY_STRUCTURE_CHANGE = "product.category.structure.change";
    }

    enum ProductCategoryButton {
        /**
         * 编辑分类
         */
        EDIT("Edit", "default", "编辑", "Edit_button_default", PRODUCT_CATEGORY_EDIT),
        /**
         * 删除分类
         */
        DELETE("Abolish", "default", "删除", "Delete_button_default", PRODUCT_CATEGORY_DELETE),
        /**
         * 新建子级分类
         */
        ADD_CHILD("Add", "default", "新建子级", "Add_button_default_child", PRODUCT_CATEGORY_ADD_CHILD),
        /**
         * 新建平级分类
         */
        ADD_SAME("Add", "default", "新建平级", "Add_button_default_same", PRODUCT_CATEGORY_ADD_SAME),
        /**
         * 开启属性之后，配置属性按钮
         */
        SET_ATTRIBUTE("SetAttribute", "default", "配置属性", "SetAttribute_button_custom", PRODUCT_CATEGORY_SET_ATTRIBUTE),
        /**
         * 分类结构样式 按钮
         */
        STRUCTURE_CHANGE("StructureChange", "default", "分类结构样式", "StructureChange_custom", PRODUCT_CATEGORY_STRUCTURE_CHANGE);

        private final String i18nKey;
        private final String name;
        private final String action;
        private final String actionType;
        private final String label;

        ProductCategoryButton(String action, String actionType, String label, String name, String i18nKey) {
            this.name = name;
            this.action = action;
            this.actionType = actionType;
            this.label = label;
            this.i18nKey = i18nKey;
        }

        public IButton getButton() {
            IButton button = new Button();
            button.setAction(this.action);
            button.setActionType(this.actionType);
            button.setLabel(this.label);
            button.setName(this.name);
            button.setLabel(I18N.text(this.i18nKey));
            return button;
        }

        public String getI18nKey() {
            return i18nKey;
        }

        public String getAction() {
            return action;
        }

        public String getActionType() {
            return actionType;
        }

        public String getLabel() {
            return label;
        }

        public String getName() {
            return this.name;
        }
    }
}
