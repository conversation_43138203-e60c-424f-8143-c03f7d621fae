package com.facishare.crm.sfa.predefine.service.pivotTableRule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.constants.CommonConstants;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.sfa.lto.common.LtoOrgCommonService;
import com.facishare.crm.sfa.lto.quality.models.QualityInspectionConstants;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.sfa.lto.utils.StringUtil;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.model.procurement.SettingRuleMember;
import com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.PivotTableMappingConstants;
import com.facishare.crm.sfa.utilities.constant.PivotTableRuleConstants;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.util.CommonSqlUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController;
import com.facishare.paas.appframework.metadata.MetaDataActionService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.util.CommonSqlUtils.convert2ActionContext;


/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/11/15 10:20
 */
@Service
@Slf4j
public class PivotTableRuleServiceImpl implements PivotTableRuleService {
    @Autowired
    LtoOrgCommonService ltoOrgCommonService;
    @Autowired
    ServiceFacade serviceFacade;
    @Autowired
    MetaDataActionService metaDataActionService;
    @Autowired
    ObjectDataServiceImpl objectDataService;
    @Autowired
    protected ICommonSqlService commonSqlService;
    public static final String QUARTER = "Q";
    public static final String SPLIT = "-";

    /**
     * 组织穿透规则实例的保存数据
     */
    public void batchSavePivtoInstance(User user, List<Map<String,String>> timeListByType, IObjectData data) {
        List<IObjectData> datalist = Lists.newArrayList();
        for (Map time : timeListByType) {
            ObjectDataDocument pivotTableInstaance = new ObjectDataDocument();
            pivotTableInstaance.put(PivotTableRuleConstants.ID, serviceFacade.generateId());
            pivotTableInstaance.put(PivotTableRuleConstants.NAME, data.getName());
            pivotTableInstaance.put(Tenantable.TENANT_ID, user.getTenantId());
            pivotTableInstaance.put(PivotTableRuleConstants.START_DATE,time.get(PivotTableRuleConstants.START_DATE));
            pivotTableInstaance.put(PivotTableRuleConstants.END_DATE, time.get(PivotTableRuleConstants.END_DATE));
            pivotTableInstaance.put(PivotTableRuleConstants.PIVOT_PERIOD_NAME, time.get(PivotTableRuleConstants.PIVOT_PERIOD_NAME));
            pivotTableInstaance.put(PivotTableRuleConstants.PVIOT_TABLE_RULE_ID, data.getId());
            pivotTableInstaance.put(CommonConstants.RECORD_TYPE, "default__c");
            pivotTableInstaance.put(SystemConstants.ObjectDescribeApiName,PivotTableRuleConstants.API_NAME_INSTANCE);
            datalist.add(pivotTableInstaance.toObjectData());
        }
        serviceFacade.bulkSaveObjectData(datalist, user);

    }

    @Override
    public void updateIObjectDataBymap(IObjectData data, User user) {
        String status = data.get(PivotTableRuleConstants.STATUS, String.class);
        Map<String, Object> updateParam = Maps.newHashMap();
        if (PivotTableRuleConstants.Status.ON.getStatus().equals(status)) {
            updateParam.put(PivotTableRuleConstants.STATUS, PivotTableRuleConstants.Status.OFF.getStatus());
            serviceFacade.updateWithMap(user, data, updateParam);
        }else {
            updateParam.put(PivotTableRuleConstants.STATUS, PivotTableRuleConstants.Status.ON.getStatus());
            serviceFacade.updateWithMap(user, data, updateParam);
        }
    }

    @Override
    public void deleteDataById(String id, User user) {
        if(StringUtils.isNotEmpty(id)){
            List<IObjectData> pivotTableInstanceList = findDataByRuleId(id, user);
            serviceFacade.bulkDeleteDirect(pivotTableInstanceList,user);
        }
    }
    @Override
    public List<IObjectData> findDataByRuleId(String id, User user) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, user.getTenantId());
        SearchUtil.fillFilterEq(filters, "pivot_table_rule_id", id);
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setFilters(filters);
        QueryResult<IObjectData> pivotTableInstanceList = serviceFacade.findBySearchQuery(user,PivotTableRuleConstants.API_NAME_INSTANCE , searchQuery);
        if (!pivotTableInstanceList.getData().isEmpty()) {
            return pivotTableInstanceList.getData();
        }
        return Lists.newArrayList();
    }


    @Override
    public List<IObjectData> findinstanceDataByRuleId(String pivotTableRuleId, User user,SearchTemplateQuery query) {
        List<IFilter> filters = Lists.newArrayList();
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, user.getTenantId());
        SearchUtil.fillFilterEq(filters, "is_deleted", 0);
        SearchUtil.fillFilterEq(filters, "pivot_table_rule_id", pivotTableRuleId);
        List<OrderBy> orderByList = Lists.newArrayList();
        orderByList.add(new OrderBy("last_modified_time", false));
        searchQuery.setOrders(orderByList);
        searchQuery.setLimit(query.getLimit());
        searchQuery.setOffset(query.getOffset());
        searchQuery.setFilters(filters);
        QueryResult<IObjectData> pivotTableInstanceList = serviceFacade.findBySearchQuery(user,PivotTableRuleConstants.API_NAME_INSTANCE , searchQuery);
        if (!pivotTableInstanceList.getData().isEmpty()) {
            return pivotTableInstanceList.getData();
        }
        return Lists.newArrayList();
    }
    @Override
    public AbstractStandardDetailController.Result queryPivotMappingData(AbstractStandardDetailController.Result result,User user){
        IObjectData iObjectData = result.getData().toObjectData();
        //特殊处理报表名称
        JSONObject summaryTableId = JSON.parseObject(iObjectData.get(PivotTableRuleConstants.SUMMARY_TABLE_ID).toString());
        String viewId = summaryTableId.getString("viewId");
        String biType = summaryTableId.getString("biType");
        if(StringUtils.isNotEmpty(biType)&&"1".equals(biType)){
            //1.是统计图：Bi.Custom.Realtime.StatName.统计图图表Id.Label
            if(StringUtils.isNotEmpty(I18N.text("Bi.Custom.Realtime.StatName."+viewId+".Label"))){
                iObjectData.set(PivotTableRuleConstants.SUMMARY_TABLE_ID_LABEL,I18N.text("Bi.Custom.Realtime.StatName."+viewId+".Label"));
            }
        }else if(StringUtils.isNotEmpty(biType)&&"4".equals(biType)){
            //1.是拼表：Bi.Custom.Realtime.RptName.报表图表Id.Label
            if(StringUtils.isNotEmpty(I18N.text("Bi.Custom.Realtime.RptName."+viewId+".Label"))){
                iObjectData.set(PivotTableRuleConstants.SUMMARY_TABLE_ID_LABEL,I18N.text("Bi.Custom.Realtime.RptName."+viewId+".Label"));
            }
        }
        String id = result.getData().toObjectData().getId();
        List<OrderBy> orders =  new ArrayList<>();
        orders.add(new OrderBy("level",Boolean.TRUE));
        orders.add(new OrderBy("value_no",Boolean.TRUE));
        // 每次查询个数
        int limit = 2000;
        List<IFilter> filters = Lists.newArrayList();
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, user.getTenantId());
        SearchUtil.fillFilterEq(filters, "is_deleted", 0);
        SearchUtil.fillFilterEq(filters, "pivot_table_rule_id", id);
        searchQuery.setLimit(limit);
        searchQuery.setFilters(filters);
        searchQuery.setOrders(orders);
        QueryResult<IObjectData> pivotTableMappingList = serviceFacade.findBySearchQuery(user, PivotTableMappingConstants.API_NAME , searchQuery);
        if (pivotTableMappingList.getData().isEmpty()) {
            return  result;
        }
        List<Map<String,Object>> firstdata = pivotTableMappingList.getData().stream()
                .filter(o -> o.get("level").toString().equals("0"))
                .map(ObjectDataExt::toMap)
                .collect(Collectors.toList());
        List<IObjectData> seconddata = pivotTableMappingList.getData().stream()
                .filter(o -> (o.get("level").toString().equals("1")&& o.get("type").toString().equals("1")))
                .collect(Collectors.toList());
        List<IObjectData> seconddataRuntime = pivotTableMappingList.getData().stream()
                .filter(o -> (o.get("level").toString().equals("1")&& o.get("type").toString().equals("2")))
                .collect(Collectors.toList());
        List<IObjectData> thirddata = pivotTableMappingList.getData().stream()
                .filter(o -> o.get("level").toString().equals("2"))
                .collect(Collectors.toList());
        if(ObjectUtils.isNotEmpty(firstdata)){
            firstdata.sort(Comparator.comparing( o->Integer.valueOf(o.get("value_no").toString())));
            for (Map<String,Object> firsttemp:firstdata){
                List<String> secondobjectFields =new ArrayList<>();
                if(ObjectUtils.isNotEmpty(seconddata)) {
                    seconddata.sort(Comparator.comparing( o->Integer.valueOf(o.get("value_no").toString())));
                    for (IObjectData secondtemp : seconddata) {
                        if (secondtemp.get("parent_value_no").toString().equals(firsttemp.get("value_no").toString())) {
                            secondobjectFields.add(secondtemp.get("target_field_api_name").toString());
                        }
                    }
                }
                firsttemp.put("objectFields", secondobjectFields);
                List<String> seconddataRuntimeFields =new ArrayList<>();
                if(ObjectUtils.isNotEmpty(seconddataRuntime)) {
                    seconddataRuntime.sort(Comparator.comparing( o->Integer.valueOf(o.get("value_no").toString())));
                    for (IObjectData secondtempRuntime : seconddataRuntime) {
                        if (secondtempRuntime.get("parent_value_no").toString().equals(firsttemp.get("value_no").toString())) {
                            seconddataRuntimeFields.add(secondtempRuntime.get("target_field_api_name").toString());
                        }
                    }
                }
                firsttemp.put("objectFieldsRunTime", seconddataRuntimeFields);
                if(ObjectUtils.isNotEmpty(thirddata)){
                    //thirddata.sort(Comparator.comparing( o->Integer.valueOf(o.get("value_no").toString())));
                    for (IObjectData thirdtemp: thirddata){
                        if (thirdtemp.get("parent_value_no").toString().equals(firsttemp.get("value_no").toString())){
                            firsttemp.put(PivotTableMappingConstants.SHOW_RELEATED,true);
                            firsttemp.put(PivotTableMappingConstants.SHOW_RELATED_OBJECT_API_NAME,thirdtemp.get("target_object_api_name"));
                            firsttemp.put(PivotTableMappingConstants.OBJECT_FIELD_RELATED_LIST_NAME,thirdtemp.get("target_field_api_name"));
                        }
                    }
                }else{
                    firsttemp.put(PivotTableMappingConstants.SHOW_RELEATED,false);
                    firsttemp.put(PivotTableMappingConstants.SHOW_RELATED_OBJECT_API_NAME,"");
                    firsttemp.put(PivotTableMappingConstants.OBJECT_FIELD_RELATED_LIST_NAME,"");
                }

            }

        }
        iObjectData.set("extend_data",firstdata);
        result.setData(ObjectDataDocument.of(iObjectData));
        return result;
    }


    @Override
    public void dealSave(List<Map<String,Object>> pivotTableMappingList,IObjectData iObjectData,User user) {
        int valueNo= 1;
        if(ObjectUtils.isEmpty(pivotTableMappingList)){
            return;
        }
        //获取需要保存的最终数据
        List<IObjectData> dealList =new ArrayList<>();
        for (Map<String,Object> vo:pivotTableMappingList) {
            int secondvalueNo= 1;
            int secondruntimevalueNo= 1;
            vo.put(PivotTableMappingConstants.API_NAME,"PivotTableMappingObj");
            vo.put(PivotTableMappingConstants.OBJECT_DESCRIBE_API_NAME,"PivotTableMappingObj");
            vo.put(PivotTableMappingConstants.LEVEL,"0");
            vo.put(PivotTableMappingConstants.PIVOT_TABLE_RULE_ID,iObjectData.getId());
            vo.put(PivotTableMappingConstants.VALUE_NO,String.valueOf(valueNo++));
            vo.put(PivotTableMappingConstants.ID,serviceFacade.generateId());
            vo.put(PivotTableMappingConstants.TENANT_ID,user.getTenantId());
            vo.put(PivotTableMappingConstants.TYPE,"1");//区分第二层，1是用来保存实时数据的字段，2是用来保存版本保存的数据
            dealList.add(ObjectDataExt.of(vo).getObjectData());
           // List<Map<String,Object>> pivotSecondList = (List<Map<String,Object>>)vo.get("objectFields");
            List<String> targetFieldapiNameList = (List<String>)vo.get("objectFields");
            if(ObjectUtils.isNotEmpty(targetFieldapiNameList)){
                for (String targetFieldapiName:targetFieldapiNameList){
                    Map secondvo=Maps.newHashMap();
                    secondvo.put(PivotTableMappingConstants.API_NAME,"PivotTableMappingObj");
                    secondvo.put(PivotTableMappingConstants.OBJECT_DESCRIBE_API_NAME,"PivotTableMappingObj");
                    secondvo.put(PivotTableMappingConstants.TARGET_OBJECT_API_NAME,vo.get(PivotTableMappingConstants.TARGET_OBJECT_API_NAME));
                    secondvo.put(PivotTableMappingConstants.TARGET_FIELD_API_NAME,targetFieldapiName);
                    secondvo.put(PivotTableMappingConstants.LEVEL,"1");
                    secondvo.put(PivotTableMappingConstants.VALUE_NO,String.valueOf(secondvalueNo++));
                    secondvo.put(PivotTableMappingConstants.PIVOT_TABLE_RULE_ID,iObjectData.getId());
                    secondvo.put(PivotTableMappingConstants.ID,serviceFacade.generateId());
                    secondvo.put(PivotTableMappingConstants.PARENT_VALUE_NO,vo.get("value_no"));
                    secondvo.put(PivotTableMappingConstants.TENANT_ID,user.getTenantId());
                    secondvo.put(PivotTableMappingConstants.SOURCE_FIELD_ID,vo.get(PivotTableMappingConstants.SOURCE_FIELD_ID));
                    secondvo.put(PivotTableMappingConstants.TYPE,"1");
                    dealList.add(ObjectDataExt.of(secondvo).getObjectData());
                }
            }
            List<String> targetFieldRuntimeList = (List<String>)vo.get("objectFieldsRunTime");
            if(ObjectUtils.isNotEmpty(targetFieldRuntimeList)){
                for (String targetFeildRuntime:targetFieldRuntimeList){
                    Map secondvoRuntime=Maps.newHashMap();
                    secondvoRuntime.put(PivotTableMappingConstants.API_NAME,"PivotTableMappingObj");
                    secondvoRuntime.put(PivotTableMappingConstants.OBJECT_DESCRIBE_API_NAME,"PivotTableMappingObj");
                    secondvoRuntime.put(PivotTableMappingConstants.TARGET_OBJECT_API_NAME,vo.get(PivotTableMappingConstants.TARGET_OBJECT_API_NAME));
                    secondvoRuntime.put(PivotTableMappingConstants.TARGET_FIELD_API_NAME,targetFeildRuntime);
                    secondvoRuntime.put(PivotTableMappingConstants.LEVEL,"1");
                    secondvoRuntime.put(PivotTableMappingConstants.VALUE_NO,String.valueOf(secondruntimevalueNo++));
                    secondvoRuntime.put(PivotTableMappingConstants.PIVOT_TABLE_RULE_ID,iObjectData.getId());
                    secondvoRuntime.put(PivotTableMappingConstants.ID,serviceFacade.generateId());
                    secondvoRuntime.put(PivotTableMappingConstants.PARENT_VALUE_NO,vo.get("value_no"));
                    secondvoRuntime.put(PivotTableMappingConstants.TENANT_ID,user.getTenantId());
                    secondvoRuntime.put(PivotTableMappingConstants.SOURCE_FIELD_ID,vo.get(PivotTableMappingConstants.SOURCE_FIELD_ID));
                    secondvoRuntime.put(PivotTableMappingConstants.TYPE,"2");
                    dealList.add(ObjectDataExt.of(secondvoRuntime).getObjectData());
                }
            }
            if(ObjectUtils.isNotEmpty(vo.get("showRelatedObjectApiName"))){
                Map<String,Object> thirdvo = new HashMap<String,Object>();
                thirdvo.put(PivotTableMappingConstants.API_NAME,"PivotTableMappingObj");
                thirdvo.put(PivotTableMappingConstants.PARENT_VALUE_NO,vo.get("value_no"));
                thirdvo.put(PivotTableMappingConstants.OBJECT_DESCRIBE_API_NAME,"PivotTableMappingObj");
                thirdvo.put(PivotTableMappingConstants.PIVOT_TABLE_RULE_ID,iObjectData.getId());
                thirdvo.put(PivotTableMappingConstants.SOURCE_FIELD_ID,vo.get(PivotTableMappingConstants.SOURCE_FIELD_ID));
                thirdvo.put(PivotTableMappingConstants.LEVEL,"2");
                thirdvo.put(PivotTableMappingConstants.ID,serviceFacade.generateId());
                thirdvo.put(PivotTableMappingConstants.TENANT_ID,user.getTenantId());
                thirdvo.put(PivotTableMappingConstants.TARGET_OBJECT_API_NAME,vo.get("showRelatedObjectApiName"));
                thirdvo.put(PivotTableMappingConstants.TARGET_FIELD_API_NAME,vo.get("object_field_related_list_name"));
                thirdvo.put(PivotTableMappingConstants.TYPE,"1");
                dealList.add(ObjectDataExt.of(thirdvo).getObjectData());
            }
        }
        //保存数据
        serviceFacade.bulkSaveObjectData(dealList,user);
    }
    /**
     * 获取两个时间段对应类型的所有可能时间
     *
     * @param type      日期类型，包含month、quarter,year
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return
     */
    @Override
    public List<Map<String,String>> getTimeListByType(String type, long startDate, long endDate) {
        Calendar calendarStart = Calendar.getInstance();
        calendarStart.setTimeInMillis(startDate);
        int startMonth = calendarStart.get(Calendar.MONTH) + 1;
        int startyear = calendarStart.get(Calendar.YEAR);
        // 根据月份计算当前季度
        int startQuarter = (startMonth - 1) / 3 + 1;
        Calendar calendarEnd = Calendar.getInstance();
        calendarEnd.setTimeInMillis(endDate);
        int endMonth = calendarEnd.get(Calendar.MONTH) + 1;
        int endyear = calendarEnd.get(Calendar.YEAR);
        int endQuarter = (endMonth - 1) / 3 + 1;
        List<Map<String,String>> result = new ArrayList<>();
        if (type.equals("2")) {
            if (startyear == endyear) {
                for (int i = startMonth; i <= endMonth; i++) {
                    Map<String,String> map=new HashMap<String,String>();
                    long startTime = getMonthStartTime(startyear,i);
                    long endTime = getMonthEndTime(startyear,i);
                    map.put("start_date",String.valueOf(startTime));
                    map.put("end_date",String.valueOf(endTime));
                    map.put("pivot_period_name", generatePeriodName(startyear, i));
                    result.add(map);
                }
            } else {
                for (int i = startMonth; i <= 12; i++) {
                    Map<String,String> map=new HashMap<String,String>();
                    long startTime = getMonthStartTime(startyear,i);
                    long endTime = getMonthEndTime(startyear,i);
                    map.put("start_date",String.valueOf(startTime));
                    map.put("end_date",String.valueOf(endTime));
                    map.put("pivot_period_name", generatePeriodName(startyear, i));
                    result.add(map);
                }
                for (int i = startyear + 1; i < endyear; i++) {
                    for (int j = 1; j <= 12; j++) {
                        Map<String,String> map=new HashMap<String,String>();
                        long startTime = getMonthStartTime(i,j);
                        long endTime = getMonthEndTime(i,j);
                        map.put("start_date",String.valueOf(startTime));
                        map.put("end_date",String.valueOf(endTime));
                        map.put("pivot_period_name", generatePeriodName(i, j));
                        result.add(map);
                    }
                }
                for (int i = 1; i <= endMonth; i++) {
                    Map<String,String> map=new HashMap<String,String>();
                    long startTime = getMonthStartTime(endyear,i);
                    long endTime = getMonthEndTime(endyear,i);
                    map.put("start_date",String.valueOf(startTime));
                    map.put("end_date",String.valueOf(endTime));
                    map.put("pivot_period_name", generatePeriodName(endyear, i));
                    result.add(map);
                }

            }
        } else if (type.equals("3")) {
            if (startyear == endyear) {
                for (int i = startQuarter; i <= endQuarter; i++) {
                    Map<String,String> map=new HashMap<String,String>();
                    long seasonStartDate = getSeasonStartDate(i,startyear);
                    long seasonEndDate = getSeasonEndDate(i,startyear);
                    map.put("start_date",String.valueOf(seasonStartDate));
                    map.put("end_date",String.valueOf(seasonEndDate));
                    map.put("pivot_period_name",startyear + SPLIT + QUARTER + i);
                    result.add(map);
                }
            } else {
                for (int i = startQuarter; i <= 4; i++) {
                    Map<String,String> map=new HashMap<String,String>();
                    long seasonStartDate = getSeasonStartDate(i,startyear);
                    long seasonEndDate = getSeasonEndDate(i,startyear);
                    map.put("start_date",String.valueOf(seasonStartDate));
                    map.put("end_date",String.valueOf(seasonEndDate));
                    map.put("pivot_period_name",startyear + SPLIT + QUARTER + i);
                    result.add(map);
                }
                for (int i = startyear + 1; i < endyear; i++) {
                    for (int j = 1; j <= 4; j++) {
                        Map<String,String> map=new HashMap<String,String>();
                        long seasonStartDate = getSeasonStartDate(j,i);
                        long seasonEndDate = getSeasonEndDate(j,i);
                        map.put("start_date",String.valueOf(seasonStartDate));
                        map.put("end_date",String.valueOf(seasonEndDate));
                        map.put("pivot_period_name",i + SPLIT + QUARTER + j);
                        result.add(map);
                    }
                }
                for (int i = 1; i <= endQuarter; i++) {
                    Map<String,String> map=new HashMap<String,String>();
                    long seasonStartDate = getSeasonStartDate(i,endyear);
                    long seasonEndDate = getSeasonEndDate(i,endyear);
                    map.put("start_date",String.valueOf(seasonStartDate));
                    map.put("end_date",String.valueOf(seasonEndDate));
                    map.put("pivot_period_name",endyear + SPLIT + QUARTER + i);
                    result.add(map);
                }
            }
        } else {
            for (int i = startyear; i <= endyear; i++) {
                Map<String,String> map=new HashMap<String,String>();
                Calendar cal = Calendar.getInstance();
                cal.set(i, 0, 1, 0, 0, 0);
                cal.getTimeInMillis();
                map.put("start_date",String.valueOf(cal.getTimeInMillis()));
                cal.set(i, 11, 31, 23, 59, 59);
                map.put("end_date",String.valueOf(cal.getTimeInMillis()));
                cal.getTimeInMillis();
                map.put("pivot_period_name", "" + i);
                result.add(map);
            }

        }
        return result;
    }

    private static String generatePeriodName(int year, int i) {
        if (0 < i && i < 10) {
            return year + SPLIT + "0" + i;
        } else {
            return year + SPLIT + i;
        }
    }

    public static void main(String[] args) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map<String,String> map=new HashMap<String,String>();
        Calendar cal = Calendar.getInstance();
        cal.set(2023, 0, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.getTimeInMillis();
        map.put("start_date",String.valueOf(cal.getTimeInMillis()));
        cal.set(2023, 11, 31, 23, 59, 59);
        cal.set(Calendar.MILLISECOND, 0);
        map.put("end_date",String.valueOf(cal.getTimeInMillis()));
        cal.getTimeInMillis();
    }
    public static long getSeasonStartDate (int startQuarter ,int startYear) {
        if(startQuarter==1){
            //yijidu 2023-01-01 00:00:00  1672502400000   2023-03-31 23:59:59  1680278399000
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar = Calendar.getInstance();
            // 将日期设置为该月的第一天
            calendar.set(startYear, 0, 1,0,0,0);
            calendar.set(Calendar.MILLISECOND, 0);
            log.info("时间戳为："+calendar.getTimeInMillis());
            log.info("本季度结束时间：" + dateFormat.format(calendar.getTimeInMillis()));
            return calendar.getTimeInMillis();
        }else if(startQuarter==2){
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar = Calendar.getInstance();
            // 将日期设置为该月的第一天
            calendar.set(startYear, 3, 1,0,0,0);
            calendar.set(Calendar.MILLISECOND, 0);
            log.info("时间戳为："+calendar.getTimeInMillis());
            log.info("本季度结束时间：" + dateFormat.format(calendar.getTimeInMillis()));
            return calendar.getTimeInMillis();

        }else  if(startQuarter==3){
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar = Calendar.getInstance();
            // 将日期设置为该月的第一天
            calendar.set(startYear, 6, 1,0,0,0);
            calendar.set(Calendar.MILLISECOND, 0);
            log.info("时间戳为："+calendar.getTimeInMillis());
            log.info("本季度结束时间：" + dateFormat.format(calendar.getTimeInMillis()));
            return calendar.getTimeInMillis();
        }else if(startQuarter==4){
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar = Calendar.getInstance();
            // 将日期设置为该月的第一天
            calendar.set(startYear, 9, 1,0,0,0);
            calendar.set(Calendar.MILLISECOND, 0);
            log.info("时间戳为："+calendar.getTimeInMillis());
            log.info("本季度结束时间：" + dateFormat.format(calendar.getTimeInMillis()));
            return calendar.getTimeInMillis();

        }else {
            log.info("传参有误，请检查：{}",startQuarter,startYear);
            return 0l;
        }

    }
    public static long getSeasonEndDate (int endQuarter ,int endYear) {
        if(endQuarter==1){
            //yijidu 2023-01-01 00:00:00  1672502400000   2023-03-31 23:59:59  1680278399000
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar = Calendar.getInstance();
            calendar.set(endYear, 2, 31, 23, 59, 59);
            calendar.set(Calendar.MILLISECOND, 0);
            log.info("时间戳为："+calendar.getTimeInMillis());
            log.info("本季度结束时间：" + dateFormat.format(calendar.getTimeInMillis()));
            return calendar.getTimeInMillis();

        }else if(endQuarter==2){
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar = Calendar.getInstance();
            calendar.set(endYear, 5, 30, 23, 59, 59);
            calendar.set(Calendar.MILLISECOND, 0);
            log.info("时间戳为："+calendar.getTimeInMillis());
            log.info("本季度结束时间：" + dateFormat.format(calendar.getTimeInMillis()));
            return calendar.getTimeInMillis();

        }else  if(endQuarter==3){
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar = Calendar.getInstance();
            calendar.set(endYear, 8, 30, 23, 59, 59);
            calendar.set(Calendar.MILLISECOND, 0);
            log.info("时间戳为："+calendar.getTimeInMillis());
            log.info("本季度结束时间：" + dateFormat.format(calendar.getTimeInMillis()));
            return calendar.getTimeInMillis();

        }else if(endQuarter==4){
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar = Calendar.getInstance();
            calendar.set(endYear, 11, 31, 23, 59, 59);
            calendar.set(Calendar.MILLISECOND, 0);
            log.info("时间戳为："+calendar.getTimeInMillis());
            log.info("本季度结束时间：" + dateFormat.format(calendar.getTimeInMillis()));
            return calendar.getTimeInMillis();
        }else {
            log.info("传参有误，请检查：{}",endQuarter,endYear);
            return 0l;
        }
    }
    public static  long getMonthStartTime(long date){
        // 创建Calendar实例
        Calendar calendar = Calendar.getInstance();
        // 设置日期为当前日期
        calendar.setTimeInMillis(date);
        // 将日期设置为该月的第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 获取本月的开始时间
        Date startTime = calendar.getTime();
        long start = calendar.getTimeInMillis();
        // 将日期设置为该月的最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        // 输出结果
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return start;


    }
    public static  long getMonthEndTime(long date){
        // 创建Calendar实例
        Calendar calendar = Calendar.getInstance();
        // 设置日期为当前日期
        calendar.setTimeInMillis(date);
        Date endTime = calendar.getTime();
        // 将日期设置为该月的最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        // 输出结果
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 获取本月的结束时间
        return calendar.getTimeInMillis();
    }

    public static  long getMonthStartTime(int year,int month){
        Calendar cale = Calendar.getInstance();
        cale.set(Calendar.YEAR,year);    //赋值年份
        cale.set(Calendar.MONTH, month-1);//赋值月份
        int fistDay = cale.getActualMinimum(Calendar.DAY_OF_MONTH);//获取月最大天数
        cale.set(Calendar.DAY_OF_MONTH, fistDay);
        cale.set(Calendar.HOUR_OF_DAY, 0); //时
        cale.set(Calendar.MINUTE, 0); //分
        cale.set(Calendar.SECOND, 0); //秒
        cale.set(Calendar.MILLISECOND, 0); //毫秒
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String lastDayOfMonth = sdf.format(cale.getTime());
        System.out.println(lastDayOfMonth);
        System.out.println(cale.getTimeInMillis());
        return cale.getTimeInMillis();
    }
    public static  long getMonthEndTime(int year,int month){
        Calendar cale = Calendar.getInstance();
        cale.clear();
        cale.set(Calendar.YEAR,year);    //赋值年份
        cale.set(Calendar.MONTH, month-1);//赋值月份
        int lastDay = cale.getActualMaximum(Calendar.DAY_OF_MONTH);//获取月最大天数
        cale.set(Calendar.DAY_OF_MONTH, lastDay);//设置日历中月份的最大天数
        cale.set(Calendar.HOUR_OF_DAY, 23); //时
        cale.set(Calendar.MINUTE, 59); //分
        cale.set(Calendar.SECOND, 59); //秒
        cale.set(Calendar.MILLISECOND, 0); //毫秒
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String lastDayOfMonth = sdf.format(cale.getTime());
        System.out.println(lastDayOfMonth);
        System.out.println(cale.getTimeInMillis());
        return cale.getTimeInMillis();
    }

    @Override
    public void dealVisibility(IObjectData iObjectData, User user, ActionContext actionContext){

        if(actionContext.getActionCode().equals("Edit")){
            String sql="";
            try {
                sql= String.format("delete from biz_setting_rule_member where tenant_id='%s' and rule_id = '%s'"
                        , actionContext.getTenantId(), iObjectData.getId());
                int count =objectDataService.deleteBySql(actionContext.getTenantId(), String.format(sql));
                log.info("deleteBySql count:{}", count);
            } catch (MetadataServiceException e) {
                log.error("deleteBySql error,sql:{}", sql, e);
                throw new MetaDataBusinessException(e);
            }
        }
        PivotTableRuleConstants.User User = JSON.parseObject(String.valueOf(iObjectData.get("visibility")), PivotTableRuleConstants.User.class);
        log.warn("PivotTableRuleAddAction doAct {}, {},, {}", iObjectData.getId(), User, iObjectData);
        addRuleMember(actionContext, User, iObjectData.getId());
        //走相关团队功能
        addTeamMembers(iObjectData,actionContext);
        log.info("PivotTableRuleAddAction doAct end");
    }

    @Override
    public void dealRuntimeData(List<Map<String, Object>> pivotTableMappingList, IObjectData iObjectData, ActionContext actionContext) {

        log.info("PivotTableRuleAddAction dealRuntimeData start#####,{}", pivotTableMappingList);
        if(ObjectUtils.isEmpty(pivotTableMappingList)){
            return;
        }
        //先删除type=2的数据，然后在插入
        String sql="";
        try {
            sql= String.format("delete from biz_pivot_table_mapping  where tenant_id='%s' and pivot_table_rule_id = '%s' and type='2'"
                    , actionContext.getTenantId(), iObjectData.getId());
            int count =objectDataService.deleteBySql(actionContext.getTenantId(), String.format(sql));
            log.info("deleteBySql count:{},{},{}", sql,count,iObjectData.getId());
        } catch (MetadataServiceException e) {
            log.error("deleteBySql error,sql:{}", sql, e);
            throw new MetaDataBusinessException(e);
        }
        //获取需要保存的最终数据
        List<IObjectData> dealList =new ArrayList<>();
        for (Map<String,Object> vo:pivotTableMappingList) {
            int secondruntimevalueNo= 1;
            List<String> targetFieldRuntimeList = (List<String>) vo.get("objectFieldsRunTime");
            log.info("targetFieldRuntimeList,start#####,{}", targetFieldRuntimeList);
            if (ObjectUtils.isNotEmpty(targetFieldRuntimeList)) {
                for (String targetFeildRuntime : targetFieldRuntimeList) {
                    Map secondvoRuntime = Maps.newHashMap();
                    secondvoRuntime.put(PivotTableMappingConstants.API_NAME, "PivotTableMappingObj");
                    secondvoRuntime.put(PivotTableMappingConstants.OBJECT_DESCRIBE_API_NAME, "PivotTableMappingObj");
                    secondvoRuntime.put(PivotTableMappingConstants.TARGET_OBJECT_API_NAME, vo.get(PivotTableMappingConstants.TARGET_OBJECT_API_NAME));
                    secondvoRuntime.put(PivotTableMappingConstants.TARGET_FIELD_API_NAME, targetFeildRuntime);
                    secondvoRuntime.put(PivotTableMappingConstants.LEVEL, "1");
                    secondvoRuntime.put(PivotTableMappingConstants.VALUE_NO, String.valueOf(secondruntimevalueNo++));
                    secondvoRuntime.put(PivotTableMappingConstants.PIVOT_TABLE_RULE_ID, iObjectData.getId());
                    secondvoRuntime.put(PivotTableMappingConstants.ID, serviceFacade.generateId());
                    secondvoRuntime.put(PivotTableMappingConstants.PARENT_VALUE_NO, vo.get("value_no").toString());
                    secondvoRuntime.put(PivotTableMappingConstants.TENANT_ID, actionContext.getUser().getTenantId());
                    secondvoRuntime.put(PivotTableMappingConstants.SOURCE_FIELD_ID, vo.get(PivotTableMappingConstants.SOURCE_FIELD_ID));
                    secondvoRuntime.put(PivotTableMappingConstants.TYPE, "2");
                    dealList.add(ObjectDataExt.of(secondvoRuntime).getObjectData());
                }
            }
        }
        log.info("PivotTableRuleAddAction dealRuntimeData dealList:{}", dealList);
         List<IObjectData> dataList = serviceFacade.bulkSaveObjectData(dealList, actionContext.getUser());
        log.info("PivotTableRuleAddAction dealRuntimeData dataList:{}", dataList);
    }

    private void addTeamMembers(IObjectData objectData,ActionContext actionContext){
        log.info("addTeamMembers start#####,{}");
        if(objectData != null) {
            try {
                String ruleId = String.valueOf(objectData.getId());
                List<Map> members = findMembers(actionContext, Lists.newArrayList(ruleId));
                log.info("ruleId #####,{}",ruleId);
                QIGetWechatConversionModel.QIUser qiUser = msgUserMembers(members);
                if (qiUser != null) {
                    addTeamMembers(actionContext.getTenantId(), qiUser, actionContext.getUser(), objectData);
                }
            } catch (Exception ex) {
                log.error("QualityInspectionWebDetailController addTeamMembers err:", ex);
            }
        }
        log.info("addTeamMembers end#####,{}");
    }

    public void addTeamMembers(String tenantId, QIGetWechatConversionModel.QIUser qiUser, User user, IObjectData objectData) {
        List<String> msgMembers = msgMembers(tenantId, qiUser);
        if (CollectionUtil.isNotEmpty(msgMembers)) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> teamMembers = new ArrayList<>();
            msgMembers.forEach(x ->
                    teamMembers.add(new TeamMember(x, TeamMember.Role.NORMAL_STAFF, TeamMember.Permission.READONLY, TeamMember.MemberType.EMPLOYEE ))
            );
            objectDataExt.addTeamMembers(teamMembers);

            try {
                 List<IObjectData> dataList = objectDataService.batchUpdateRelevantTeam(Lists.newArrayList(objectData), AccountUtil.getDefaultActionContext(user));
                 log.info("addTeamMembers dataList:{}", dataList);
            } catch (Exception e) {
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
            }
        }
    }

    private List<String> msgMembers(String tenantId, QIGetWechatConversionModel.QIUser qiUser){
        Set<String> msgMembers = Sets.newHashSet();
        if (qiUser == null) return Lists.newArrayList();

        List<String> members = qiUser.getMember();
        if(CollectionUtil.isNotEmpty(members)){
            msgMembers.addAll(members);
        }

        List<String> groupIds = qiUser.getGroup();
        if(CollectionUtil.isNotEmpty(groupIds)){
            //取所属组的用户
           // List<String> userGroupMembers = ltoOrgCommonService.batchGetMembersByUserGroupIds(tenantId, groupIds, true);
            List<String> userGroupMembers = ltoOrgCommonService.getMembersByDeptIds(tenantId, groupIds, true);
            if(CollectionUtil.isNotEmpty(userGroupMembers)){
                msgMembers.addAll(userGroupMembers);
            }
        }

        List<String> roleIds = qiUser.getRole();
        if(CollectionUtil.isNotEmpty(roleIds)) {
            //取所属于权限的用户
            List<String> userRoleMemberss = ltoOrgCommonService.batchGetRoleUsersByRoleIds(tenantId, roleIds, true);
            if(CollectionUtil.isNotEmpty(userRoleMemberss)){
                msgMembers.addAll(userRoleMemberss);
            }
        }
        return Lists.newArrayList(msgMembers);
    }


    private QIGetWechatConversionModel.QIUser msgUserMembers(List<Map> mapList) {
        if (CollectionUtil.isNotEmpty(mapList)) {
            List<String> msgUserMember = Lists.newArrayList();
            List<String> msgUserGroup = Lists.newArrayList();
            List<String> msgUserRole = Lists.newArrayList();

            for (Map map : mapList) {
                int member_type = (Integer) map.get("member_type");
                String member_id = StringUtil.convertString((String) map.get("member_id"), "");
                if (member_type == 2) {
                    msgUserMember.add(member_id);
                } else if (member_type == 4) {
                    msgUserGroup.add(member_id);
                } else if (member_type == 6) {
                    msgUserRole.add(member_id);
                }
            }
            return QIGetWechatConversionModel.QIUser.builder().member(msgUserMember).group(msgUserGroup).role(msgUserRole).build();
        }
        return null;
    }

    public List<Map> findMembers(ActionContext context, List<Object> ids) {
        List<WhereParam> wheres = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
        CommonSqlUtils.addWhereParam(wheres, "rule_id", CommonSqlOperator.IN, ids);
        CommonSqlUtils.addWhereParam(wheres, QualityInspectionConstants.CommonConstants.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList("0"));

        List<Map> mapList = Lists.newArrayList();
        try {
            mapList = commonSqlService.select("biz_setting_rule_member", wheres, convert2ActionContext(context));
        } catch (MetadataServiceException e) {
            log.error("findRule error,{}", wheres, e);
        }
        return mapList;
    }

    public void addRuleMember(ActionContext context, PivotTableRuleConstants.User user, String ruleId) {
        log.info("addRuleMember start:{},{}", user,ruleId);
        List<Map<String, Object>> insertMemberData = Lists.newArrayList();
        if(user != null) {
            memberToDb(context, distinctList(user.getMember()), ruleId, 2, insertMemberData);
            memberToDb(context, distinctList(user.getGroup()), ruleId, 4, insertMemberData);
            memberToDb(context, distinctList(user.getRole()), ruleId, 6, insertMemberData);
        }
        log.info("addRuleMember insertMemberData:{}", insertMemberData);
        insertRuleMember(context, insertMemberData);
        log.info("addRuleMember end:{},{}", user,ruleId);
    }

    private List<String> distinctList(List<String> list){
        if(CollectionUtil.isNotEmpty(list)){
            return list.stream().distinct().collect(Collectors.toList());
        }
        return null;
    }

    private void memberToDb(ActionContext context, List<String> userlist, String ruleId, int type, List<Map<String, Object>> insertMemberData) {
        long currentTimeMillis = System.currentTimeMillis();
        if(CollectionUtil.isNotEmpty(userlist) ){
            for( String user:  userlist ) {
                String _id = serviceFacade.generateId();
                Map<String, Object> memberItem = Maps.newHashMap();
                memberItem.put(SettingRuleMember.ID, _id);
                memberItem.put(SettingRuleMember.TENANT_ID, context.getTenantId());
                memberItem.put(SettingRuleMember.OBJECT_DESCRIBE_API_NAME,PivotTableRuleConstants.API_NAME);
                memberItem.put(SettingRuleMember.MEMBER_ID, user);
                memberItem.put(SettingRuleMember.MEMBER_TYPE, type);
                memberItem.put(SettingRuleMember.RULE_ID, ruleId);
                memberItem.put(SettingRuleMember.IS_DELETED, 0);
                memberItem.put(SettingRuleMember.CREATED_BY, context.getUser().getUpstreamOwnerIdOrUserId());
                memberItem.put(SettingRuleMember.CREATE_TIME, currentTimeMillis);
                memberItem.put(SettingRuleMember.LAST_MODIFIED_BY,  context.getUser().getUpstreamOwnerIdOrUserId());
                memberItem.put(SettingRuleMember.LAST_MODIFIED_TIME, currentTimeMillis);
                insertMemberData.add(memberItem);
            }
        }
    }

    public void insertRuleMember(ActionContext context, List<Map<String, Object>> insertMemberData) {
        if (CollectionUtil.isNotEmpty(insertMemberData)) {
            try {
                commonSqlService.insert("biz_setting_rule_member", insertMemberData, convert2ActionContext(context));
            } catch (MetadataServiceException e) {
                log.error("addRuleMember error", e);
            }
        }
    }




}
