# ASR服务配置示例
# 请根据实际情况修改配置参数

asr:
  # 默认ASR服务提供商
  default-provider: aliyun
  
  # 阿里云听悟配置
  aliyun:
    # 是否启用阿里云ASR服务
    enabled: true
    
    # 阿里云访问密钥ID（建议通过环境变量设置）
    access-key-id: ${ALIYUN_ACCESS_KEY_ID:your-access-key-id}
    
    # 阿里云访问密钥Secret（建议通过环境变量设置）
    access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:your-access-key-secret}
    
    # 听悟应用Key（建议通过环境变量设置）
    app-key: ${ALIYUN_APP_KEY:your-app-key}
    
    # 听悟服务端点
    endpoint: tingwu.cn-beijing.aliyuncs.com
    
    # 服务区域
    region: cn-beijing
    
    # 最大并发数
    max-concurrency: 200
  
  # 腾讯云语音识别配置
  tencent:
    # 是否启用腾讯云ASR服务
    enabled: true
    
    # 腾讯云应用ID（建议通过环境变量设置）
    app-id: ${TENCENT_APP_ID:your-app-id}
    
    # 腾讯云密钥ID（建议通过环境变量设置）
    secret-id: ${TENCENT_SECRET_ID:your-secret-id}
    
    # 腾讯云密钥Key（建议通过环境变量设置）
    secret-key: ${TENCENT_SECRET_KEY:your-secret-key}
    
    # 默认引擎模型类型
    # 可选值：16k_zh, 16k_zh_large, 16k_multi_lang, 16k_zh_en, 8k_zh, 8k_zh_large
    default-engine-model-type: 16k_zh_large
    
    # WebSocket请求URL
    request-url: wss://asr.cloud.tencent.com/asr/v2
    
    # 最大并发数
    max-concurrency: 200

# 环境变量设置示例（在部署时设置）
# export ALIYUN_ACCESS_KEY_ID="your-actual-access-key-id"
# export ALIYUN_ACCESS_KEY_SECRET="your-actual-access-key-secret"
# export ALIYUN_APP_KEY="your-actual-app-key"
# export TENCENT_APP_ID="your-actual-app-id"
# export TENCENT_SECRET_ID="your-actual-secret-id"
# export TENCENT_SECRET_KEY="your-actual-secret-key"

# 日志配置（可选）
logging:
  level:
    com.facishare.crm.sfa.activity.predefine.service.asr: DEBUG
    com.tencent.asrv2: INFO
    com.aliyuncs: INFO

# 业务配置示例
business:
  asr:
    # 自动提供商选择规则
    auto-selection:
      # 英文语音优先使用腾讯云
      english-preferred-provider: tencent
      # 中文语音优先使用阿里云
      chinese-preferred-provider: aliyun
      # 其他语言的默认提供商
      default-fallback-provider: aliyun
    
    # 重试配置
    retry:
      # 最大重试次数
      max-attempts: 3
      # 重试间隔（毫秒）
      retry-interval: 1000
      # 是否启用提供商切换重试
      enable-provider-fallback: true
    
    # 超时配置
    timeout:
      # 连接超时（毫秒）
      connection-timeout: 10000
      # 读取超时（毫秒）
      read-timeout: 30000
      # 任务超时（毫秒）
      task-timeout: 300000
