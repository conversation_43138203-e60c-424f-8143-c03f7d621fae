package com.facishare.crm.sfa.activity.predefine.service.asr.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * ASR服务配置
 * 
 * <AUTHOR>
 * @date 2025/1/22
 * @description ASR服务的配置参数
 */
@Data
@Component
@ConfigurationProperties(prefix = "asr")
public class AsrConfig {

    /**
     * 默认ASR服务提供商
     */
    private String defaultProvider = "aliyun";

    /**
     * 阿里云配置
     */
    private AliyunConfig aliyun = new AliyunConfig();

    /**
     * 腾讯云配置
     */
    private TencentConfig tencent = new TencentConfig();

    @Data
    public static class AliyunConfig {
        /**
         * 是否启用
         */
        private boolean enabled = true;

        /**
         * 访问密钥ID
         */
        private String accessKeyId;

        /**
         * 访问密钥Secret
         */
        private String accessKeySecret;

        /**
         * 应用Key
         */
        private String appKey;

        /**
         * 端点
         */
        private String endpoint = "tingwu.cn-beijing.aliyuncs.com";

        /**
         * 区域
         */
        private String region = "cn-beijing";

        /**
         * 最大并发数
         */
        private int maxConcurrency = 200;
    }

    @Data
    public static class TencentConfig {
        /**
         * 是否启用
         */
        private boolean enabled = true;

        /**
         * 应用ID
         */
        private String appId;

        /**
         * 密钥ID
         */
        private String secretId;

        /**
         * 密钥Key
         */
        private String secretKey;

        /**
         * 默认引擎模型类型
         */
        private String defaultEngineModelType = "16k_zh";

        /**
         * 请求URL
         */
        private String requestUrl = "wss://asr.cloud.tencent.com/asr/v2";

        /**
         * 最大并发数
         */
        private int maxConcurrency = 200;
    }
}
