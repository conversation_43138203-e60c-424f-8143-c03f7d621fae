package com.facishare.crm.sfa.activity.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Collection;
import java.util.List;

public interface ActivityInteractiveJourneyModel {
    @Data
    class Arg {
        @JSONField(name = "active_record_id")
        @JsonProperty("active_record_id")
        String activeRecordId;
        @JSONField(name = "account_id")
        @JsonProperty("account_id")
        String accountId;
        @JSONField(name = "new_opportunity_id")
        @JsonProperty("new_opportunity_id")
        String opportunityId;
        @JSONField(name = "show_latest_profile_items")
        @JsonProperty("show_latest_profile_items")
        boolean showLatestProfileItems;
    }

    @Data
    class Result {
        @JSONField(name = "interactive_score_list")
        @JsonProperty("interactive_score_list")
        Collection<List<ObjectDataDocument>> interactiveScoreList;
        @JSONField(name = "interactive_score_detail")
        @JsonProperty("interactive_score_detail")
        ObjectDataDocument interactiveScoreDetail;
        @JSONField(name = "profile_integrated_score_list")
        @JsonProperty("profile_integrated_score_list")
        List<ObjectDataDocument> profileIntegratedScoreList;
        @JSONField(name = "profile_item_score_list")
        @JsonProperty("profile_item_score_list")
        List<ObjectDataDocument> prefileItemScoreList;
        @JSONField(name = "latest_profile_item_score")
        @JsonProperty("latest_profile_item_score")
        ObjectDataDocument latestProfileItemScore;
        @JSONField(name = "latest_profile_advice")
        @JsonProperty("latest_profile_advice")
        ObjectDataDocument latestProfileAdvice;
    }
}
