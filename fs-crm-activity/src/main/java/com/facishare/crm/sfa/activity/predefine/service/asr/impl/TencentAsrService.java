package com.facishare.crm.sfa.activity.predefine.service.asr.impl;

import com.facishare.crm.sfa.activity.predefine.service.asr.AbstractAsrService;
import com.facishare.crm.sfa.activity.predefine.service.model.ActivityText;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.google.gson.Gson;
import com.tencent.asrv2.*;
import com.tencent.core.ws.Credential;
import com.tencent.core.ws.SpeechClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * 腾讯云ASR服务实现
 * 
 * <AUTHOR>
 * @date 2025/1/22
 * @description 基于腾讯云语音识别服务的ASR实现
 */
@Component
@Slf4j
public class TencentAsrService extends AbstractAsrService {

    @Value("${tencent.asr.app-id:}")
    private String appId;

    @Value("${tencent.asr.secret-id:}")
    private String secretId;

    @Value("${tencent.asr.secret-key:}")
    private String secretKey;

    @Value("${tencent.asr.engine-model-type:16k_zh}")
    private String defaultEngineModelType;

    // 全局SpeechClient实例
    private SpeechClient speechClient;
    
    // 存储活跃的识别任务
    private final Map<String, SpeechRecognizer> activeRecognizers = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        if (StringUtils.isNotBlank(appId) && StringUtils.isNotBlank(secretId) && StringUtils.isNotBlank(secretKey)) {
            speechClient = new SpeechClient(AsrConstant.DEFAULT_RT_REQ_URL);
            log.info("Tencent ASR service initialized successfully");
        } else {
            log.warn("Tencent ASR service not configured properly, some features may not work");
        }
    }

    @PreDestroy
    public void destroy() {
        if (speechClient != null) {
            speechClient.shutdown();
            log.info("Tencent ASR service shutdown");
        }
        
        // 关闭所有活跃的识别器
        activeRecognizers.values().forEach(recognizer -> {
            try {
                recognizer.close();
            } catch (Exception e) {
                log.warn("Failed to close recognizer", e);
            }
        });
        activeRecognizers.clear();
    }

    @Override
    public String getProviderName() {
        return "tencent";
    }

    @Override
    public TaskResult startRealtimeTranscription(ServiceContext context, ActivityText.ActivityRealtimeStartArg arg) {
        TaskResult result = new TaskResult();
        result.setProvider(getProviderName());
        
        try {
            if (speechClient == null) {
                throw new IllegalStateException("Tencent ASR service not properly configured");
            }

            // 创建凭证
            Credential credential = new Credential(appId, secretId, secretKey);
            
            // 创建识别请求
            SpeechRecognizerRequest request = SpeechRecognizerRequest.init();
            
            // 设置引擎模型类型
            String engineModelType = mapLanguageToEngineModel(arg.getSourceLanguage());
            request.setEngineModelType(engineModelType);
            
            // 设置语音格式
            request.setVoiceFormat(1); // PCM格式
            
            // 生成唯一的voiceId
            String voiceId = UUID.randomUUID().toString();
            request.setVoiceId(voiceId);
            
            // 设置说话人分离
            if (arg.isDiarizationEnabled()) {
                request.set("needvad", "1");
            }
            
            // 设置热词（如果需要）
            // request.set("hotword_list", "腾讯云|10,语音识别|5,ASR|11");
            
            log.debug("Tencent ASR voice_id: {}", voiceId);

            // 创建识别监听器
            CountDownLatch startLatch = new CountDownLatch(1);
            final String[] wsUrl = new String[1];
            final Exception[] startException = new Exception[1];
            
            SpeechRecognizerListener listener = new SpeechRecognizerListener() {
                @Override
                public void onRecognitionStart(SpeechRecognizerResponse response) {
                    log.info("Tencent ASR recognition started: voice_id={}", response.getVoiceId());
                    wsUrl[0] = "wss://asr.cloud.tencent.com/asr/v2/" + appId; // 构造WebSocket URL
                    startLatch.countDown();
                }

                @Override
                public void onSentenceBegin(SpeechRecognizerResponse response) {
                    log.debug("Tencent ASR sentence begin: voice_id={}", response.getVoiceId());
                }

                @Override
                public void onRecognitionResultChange(SpeechRecognizerResponse response) {
                    log.debug("Tencent ASR result change: voice_id={}, result={}", 
                             response.getVoiceId(), new Gson().toJson(response));
                    
                    // 处理实时识别结果
                    handleRealtimeResult(context, arg, response, false);
                }

                @Override
                public void onSentenceEnd(SpeechRecognizerResponse response) {
                    log.info("Tencent ASR sentence end: voice_id={}, result={}", 
                            response.getVoiceId(), new Gson().toJson(response));
                    
                    // 处理稳定识别结果
                    handleRealtimeResult(context, arg, response, true);
                }

                @Override
                public void onRecognitionComplete(SpeechRecognizerResponse response) {
                    log.info("Tencent ASR recognition complete: voice_id={}", response.getVoiceId());
                    
                    // 清理资源
                    activeRecognizers.remove(voiceId);
                    
                    // 发送完成消息
                    sendMQ(arg.getObjectId(), context.getTenantId(), context.getUser().getUserId(), 
                           "realtime2textDone", "realtime2textDone", context.getLang().getValue());
                }

                @Override
                public void onFail(SpeechRecognizerResponse response) {
                    log.error("Tencent ASR recognition failed: voice_id={}, error={}", 
                             response.getVoiceId(), new Gson().toJson(response));
                    
                    startException[0] = new RuntimeException("Tencent ASR recognition failed: " + response.getMessage());
                    startLatch.countDown();
                    
                    // 清理资源
                    activeRecognizers.remove(voiceId);
                }

                @Override
                public void onMessage(SpeechRecognizerResponse response) {
                    log.debug("Tencent ASR message: voice_id={}, message={}", 
                             response.getVoiceId(), new Gson().toJson(response));
                }
            };

            // 创建语音识别器
            SpeechRecognizer speechRecognizer = new SpeechRecognizer(speechClient, credential, request, listener);
            
            // 启动识别
            long startTime = System.currentTimeMillis();
            speechRecognizer.start();
            
            // 等待启动完成
            boolean started = startLatch.await(10, TimeUnit.SECONDS);
            if (!started) {
                speechRecognizer.close();
                throw new RuntimeException("Tencent ASR start timeout");
            }
            
            if (startException[0] != null) {
                speechRecognizer.close();
                throw startException[0];
            }
            
            log.info("Tencent ASR start latency: {} ms", System.currentTimeMillis() - startTime);
            
            // 保存识别器实例
            activeRecognizers.put(voiceId, speechRecognizer);
            
            // 设置返回结果
            result.setTaskId(voiceId);
            result.setWsUrl(wsUrl[0]);
            
            // 发送任务ID到MQ
            sendTaskId(context, arg, voiceId, null);
            
            log.info("Tencent ASR task started successfully: taskId={}", voiceId);
            
        } catch (Exception e) {
            log.error("Start Tencent realtime transcription failed", e);
            throw new RuntimeException("Failed to start Tencent ASR: " + e.getMessage(), e);
        }
        
        return result;
    }

    @Override
    public Map<String, String> stopRealtimeTranscription(ServiceContext context, ActivityText.ActivityRealtimeStopArg arg) {
        Map<String, String> result = new HashMap<>();
        
        if (StringUtils.isBlank(arg.getTaskId())) {
            updateInteractiveProcesses(context.getUser(), arg.getObjectId());
            result.put("status", "success");
            return result;
        }
        
        try {
            // 重置任务状态
            try {
                activityGeneralService.initEndRecordingState(context.getTenantId(), 
                                                           context.getUser().getUserId(), 
                                                           arg.getObjectId());
            } catch (Exception e) {
                log.error("initEndRecordingState error", e);
            }

            // 获取并停止识别器
            SpeechRecognizer recognizer = activeRecognizers.get(arg.getTaskId());
            if (recognizer != null) {
                long stopTime = System.currentTimeMillis();
                recognizer.stop();
                log.info("Tencent ASR stop latency: {} ms", System.currentTimeMillis() - stopTime);
                
                // 关闭连接
                recognizer.close();
                activeRecognizers.remove(arg.getTaskId());
            }
            
            sendMQ(arg.getObjectId(), context.getTenantId(), context.getUser().getUserId(), 
                   "realtime2textDone", "realtime2textDone", context.getLang().getValue());
            
            updateInteractiveProcesses(context.getUser(), arg.getObjectId());
            result.put("status", "success");
            
        } catch (Exception e) {
            log.error("停止腾讯云实时转写任务失败", e);
            result.put("status", "error");
            result.put("message", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getServiceConfig(ServiceContext context) {
        Map<String, Object> config = new HashMap<>();
        config.put("provider", getProviderName());
        config.put("supportedLanguages", new String[]{"zh", "en", "ja", "ko", "th", "id", "vi", "ms", "fil", "pt", "tr", "ar", "es", "hi", "fr", "de"});
        config.put("supportedFormats", new String[]{"pcm", "wav", "opus", "speex", "silk", "mp3", "m4a", "aac"});
        config.put("maxConcurrency", 200);
        config.put("engineModels", new String[]{"16k_zh", "16k_zh_large", "16k_multi_lang", "16k_zh_en", "8k_zh", "8k_zh_large"});
        return config;
    }

    @Override
    protected boolean checkServiceDependencies(ServiceContext context) {
        return speechClient != null && 
               StringUtils.isNotBlank(appId) && 
               StringUtils.isNotBlank(secretId) && 
               StringUtils.isNotBlank(secretKey);
    }

    /**
     * 将语言映射到腾讯云引擎模型
     */
    private String mapLanguageToEngineModel(String sourceLanguage) {
        if (StringUtils.isBlank(sourceLanguage)) {
            return defaultEngineModelType;
        }
        
        switch (sourceLanguage.toLowerCase()) {
            case "cn":
            case "zh":
            case "chinese":
                return "16k_zh_large";
            case "en":
            case "english":
                return "16k_en";
            case "ja":
            case "japanese":
                return "16k_ja";
            case "ko":
            case "korean":
                return "16k_ko";
            case "yue":
            case "cantonese":
                return "16k_yue";
            default:
                return defaultEngineModelType;
        }
    }

    /**
     * 处理实时识别结果
     */
    private void handleRealtimeResult(ServiceContext context, ActivityText.ActivityRealtimeStartArg arg, 
                                    SpeechRecognizerResponse response, boolean isFinal) {
        // 这里需要将腾讯云的识别结果转换为系统内部格式
        // 并调用相应的处理逻辑
        // 由于涉及复杂的业务逻辑，建议从原有代码中提取相关处理方法
        log.debug("Handle realtime result: final={}, text={}", isFinal, response.getResult());
    }

    // 以下方法需要从原Activity2TextService中提取实现
    
    private void sendTaskId(ServiceContext context, ActivityText.ActivityRealtimeStartArg arg, 
                           String taskId, String ossPath) {
        // 实现发送任务ID的逻辑
    }

    private void updateInteractiveProcesses(Object user, String objectId) {
        // 实现更新交互进程的逻辑
    }
}
