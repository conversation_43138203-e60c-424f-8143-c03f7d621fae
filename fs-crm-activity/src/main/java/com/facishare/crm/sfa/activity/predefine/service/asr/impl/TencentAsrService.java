package com.facishare.crm.sfa.activity.predefine.service.asr.impl;

import com.facishare.crm.sfa.activity.predefine.service.asr.AbstractAsrService;
import com.facishare.crm.sfa.activity.predefine.service.model.ActivityText;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * 腾讯云ASR服务实现
 *
 * <AUTHOR>
 * @date 2025/1/22
 * @description 基于腾讯云语音识别服务的ASR实现，直接拼接WSS链接
 */
@Component
@Slf4j
public class TencentAsrService extends AbstractAsrService {

    @Value("${tencent.asr.app-id:}")
    private String appId;

    @Value("${tencent.asr.secret-id:}")
    private String secretId;

    @Value("${tencent.asr.secret-key:}")
    private String secretKey;

    @Value("${tencent.asr.engine-model-type:16k_zh}")
    private String defaultEngineModelType;

    @Override
    public String getProviderName() {
        return "tencent";
    }

    @Override
    public TaskResult startRealtimeTranscription(ServiceContext context, ActivityText.ActivityRealtimeStartArg arg) {
        TaskResult result = new TaskResult();
        result.setProvider(getProviderName());

        try {
            // 检查配置
            if (StringUtils.isBlank(appId) || StringUtils.isBlank(secretId) || StringUtils.isBlank(secretKey)) {
                throw new IllegalStateException("Tencent ASR service not properly configured");
            }

            // 生成唯一的voiceId
            String voiceId = UUID.randomUUID().toString();

            // 构建WSS URL
            String wsUrl = buildWebSocketUrl(arg, voiceId);

            // 设置返回结果
            result.setTaskId(voiceId);
            result.setWsUrl(wsUrl);

            log.info("Tencent ASR task created: taskId={}, wsUrl={}", voiceId, wsUrl);

            return result;

        } catch (Exception e) {
            log.error("Start Tencent realtime transcription failed", e);
            throw new RuntimeException("Failed to start Tencent ASR: " + e.getMessage(), e);
        }
    }

    /**
     * 构建腾讯云WebSocket URL
     * 按照官方文档要求拼接参数和签名
     */
    private String buildWebSocketUrl(ActivityText.ActivityRealtimeStartArg arg, String voiceId) {
        try {
            // 当前时间戳
            long timestamp = System.currentTimeMillis() / 1000;
            // 签名过期时间（90天后）
            long expired = timestamp + 90 * 24 * 3600;
            // 随机数
            int nonce = (int) (Math.random() * 1000000000);

            // 引擎模型类型
            String engineModelType = mapLanguageToEngineModel(arg.getSourceLanguage());

            // 构建参数Map
            Map<String, String> params = new TreeMap<>();
            params.put("secretid", secretId);
            params.put("timestamp", String.valueOf(timestamp));
            params.put("expired", String.valueOf(expired));
            params.put("nonce", String.valueOf(nonce));
            params.put("engine_model_type", engineModelType);
            params.put("voice_id", voiceId);
            params.put("voice_format", "1"); // PCM格式

            // 可选参数
            if (arg.isDiarizationEnabled()) {
                params.put("needvad", "1");
            }

            // 构建签名原文
            StringBuilder signStr = new StringBuilder();
            signStr.append("asr.cloud.tencent.com/asr/v2/").append(appId).append("?");

            boolean first = true;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (!first) {
                    signStr.append("&");
                }
                signStr.append(entry.getKey()).append("=").append(entry.getValue());
                first = false;
            }

            // 生成签名
            String signature = generateSignature(signStr.toString(), secretKey);

            // 构建最终URL
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append("wss://asr.cloud.tencent.com/asr/v2/").append(appId).append("?");

            first = true;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (!first) {
                    urlBuilder.append("&");
                }
                urlBuilder.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8.name()));
                first = false;
            }

            urlBuilder.append("&signature=").append(URLEncoder.encode(signature, StandardCharsets.UTF_8.name()));

            return urlBuilder.toString();

        } catch (Exception e) {
            log.error("Failed to build WebSocket URL", e);
            throw new RuntimeException("Failed to build WebSocket URL: " + e.getMessage(), e);
        }
    }

    /**
     * 生成HMAC-SHA1签名
     */
    private String generateSignature(String signStr, String secretKey) throws NoSuchAlgorithmException, InvalidKeyException {
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA1");
        mac.init(secretKeySpec);
        byte[] hash = mac.doFinal(signStr.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hash);
    }

    @Override
    public Map<String, String> stopRealtimeTranscription(ServiceContext context, ActivityText.ActivityRealtimeStopArg arg) {
        Map<String, String> result = new HashMap<>();

        try {
            // 重置任务状态
            try {
                activityGeneralService.initEndRecordingState(context.getTenantId(),
                                                           context.getUser().getUserId(),
                                                           arg.getObjectId());
            } catch (Exception e) {
                log.error("initEndRecordingState error", e);
            }

            // 发送完成消息
            sendMQ(arg.getObjectId(), context.getTenantId(), context.getUser().getUserId(),
                   "realtime2textDone", "realtime2textDone", context.getLang().getValue());

            updateInteractiveProcesses(context.getUser(), arg.getObjectId());
            result.put("status", "success");

            log.info("Tencent ASR task stopped: taskId={}", arg.getTaskId());

        } catch (Exception e) {
            log.error("停止腾讯云实时转写任务失败", e);
            result.put("status", "error");
            result.put("message", e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getServiceConfig(ServiceContext context) {
        Map<String, Object> config = new HashMap<>();
        config.put("provider", getProviderName());
        config.put("supportedLanguages", new String[]{"zh", "en", "ja", "ko", "th", "id", "vi", "ms", "fil", "pt", "tr", "ar", "es", "hi", "fr", "de"});
        config.put("supportedFormats", new String[]{"pcm", "wav", "opus", "speex", "silk", "mp3", "m4a", "aac"});
        config.put("maxConcurrency", 200);
        config.put("engineModels", new String[]{"16k_zh", "16k_zh_large", "16k_multi_lang", "16k_zh_en", "8k_zh", "8k_zh_large"});
        return config;
    }

    @Override
    protected boolean checkServiceDependencies(ServiceContext context) {
        return StringUtils.isNotBlank(appId) &&
               StringUtils.isNotBlank(secretId) &&
               StringUtils.isNotBlank(secretKey);
    }

    /**
     * 将语言映射到腾讯云引擎模型
     */
    private String mapLanguageToEngineModel(String sourceLanguage) {
        if (StringUtils.isBlank(sourceLanguage)) {
            return defaultEngineModelType;
        }
        
        switch (sourceLanguage.toLowerCase()) {
            case "cn":
            case "zh":
            case "chinese":
                return "16k_zh_large";
            case "en":
            case "english":
                return "16k_en";
            case "ja":
            case "japanese":
                return "16k_ja";
            case "ko":
            case "korean":
                return "16k_ko";
            case "yue":
            case "cantonese":
                return "16k_yue";
            default:
                return defaultEngineModelType;
        }
    }

    // 以下方法需要从原Activity2TextService中提取实现

    private void updateInteractiveProcesses(Object user, String objectId) {
        // 实现更新交互进程的逻辑
        log.debug("Update interactive processes for objectId: {}", objectId);
    }
}
