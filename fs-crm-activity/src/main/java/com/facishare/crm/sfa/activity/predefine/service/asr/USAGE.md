# ASR服务使用指南

## 腾讯云ASR服务配置和使用

### 1. 配置参数

在 `application.yml` 中添加腾讯云ASR配置：

```yaml
tencent:
  asr:
    app-id: ${TENCENT_APP_ID:your-app-id}
    secret-id: ${TENCENT_SECRET_ID:your-secret-id}
    secret-key: ${TENCENT_SECRET_KEY:your-secret-key}
    engine-model-type: 16k_zh
```

### 2. 环境变量设置

```bash
export TENCENT_APP_ID="your-actual-app-id"
export TENCENT_SECRET_ID="your-actual-secret-id"
export TENCENT_SECRET_KEY="your-actual-secret-key"
```

### 3. API调用示例

#### 开始实时转写（指定腾讯云）

```bash
curl -X POST "http://localhost:8080/api/activity_text/service/realtime_start" \
  -H "Content-Type: application/json" \
  -d '{
    "objectId": "sales-record-123",
    "sourceLanguage": "cn",
    "diarizationEnabled": true,
    "translationEnabled": false,
    "provider": "tencent"
  }'
```

**响应示例：**
```json
{
  "taskId": "uuid-generated-task-id",
  "wsUrl": "wss://asr.cloud.tencent.com/asr/v2/your-app-id?secretid=xxx&timestamp=xxx&expired=xxx&nonce=xxx&engine_model_type=16k_zh_large&voice_id=uuid&voice_format=1&needvad=1&signature=xxx",
  "provider": "tencent"
}
```

#### 停止实时转写

```bash
curl -X POST "http://localhost:8080/api/activity_text/service/realtime_stop" \
  -H "Content-Type: application/json" \
  -d '{
    "objectId": "sales-record-123",
    "taskId": "uuid-generated-task-id",
    "provider": "tencent"
  }'
```

**响应示例：**
```json
{
  "status": "success"
}
```

### 4. WebSocket连接使用

获得WSS URL后，前端可以直接使用该URL建立WebSocket连接：

```javascript
// 使用返回的WSS URL建立连接
const wsUrl = "wss://asr.cloud.tencent.com/asr/v2/your-app-id?...";
const ws = new WebSocket(wsUrl);

ws.onopen = function(event) {
    console.log("WebSocket连接已建立");
    
    // 发送音频数据
    // 建议每40ms发送40ms时长的音频数据
    sendAudioData();
};

ws.onmessage = function(event) {
    const result = JSON.parse(event.data);
    console.log("识别结果:", result);
    
    // 处理识别结果
    if (result.code === 0 && result.result) {
        const text = result.result.voice_text_str;
        const sliceType = result.result.slice_type;
        
        if (sliceType === 2) {
            // 稳定结果
            console.log("最终识别结果:", text);
        } else if (sliceType === 1) {
            // 中间结果
            console.log("临时识别结果:", text);
        }
    }
};

ws.onerror = function(error) {
    console.error("WebSocket错误:", error);
};

ws.onclose = function(event) {
    console.log("WebSocket连接已关闭");
};

// 发送音频数据
function sendAudioData() {
    // 发送PCM音频数据
    // 16k采样率，16bit，单声道
    const audioData = getAudioData(); // 获取音频数据
    ws.send(audioData);
}

// 结束识别
function stopRecognition() {
    ws.send(JSON.stringify({type: "end"}));
}
```

### 5. 支持的参数

#### 语言类型映射

| 输入语言 | 腾讯云引擎模型 | 说明 |
|---------|---------------|------|
| cn/zh/chinese | 16k_zh_large | 中文大模型 |
| en/english | 16k_en | 英文通用 |
| ja/japanese | 16k_ja | 日语 |
| ko/korean | 16k_ko | 韩语 |
| yue/cantonese | 16k_yue | 粤语 |

#### 可选参数

- `diarizationEnabled`: 是否启用说话人分离（对应`needvad`参数）
- `translationEnabled`: 是否启用翻译（暂未实现）
- `targetLanguages`: 目标翻译语言（暂未实现）

### 6. 错误处理

常见错误码：

- `4001`: 参数不合法
- `4002`: 鉴权失败
- `4003`: AppID服务未开通
- `4004`: 资源包耗尽
- `4005`: 账户欠费
- `4006`: 并发超限
- `4007`: 音频解码失败
- `4008`: 客户端超时

### 7. 最佳实践

1. **音频格式**: 使用PCM格式，16k采样率，16bit，单声道
2. **发送频率**: 建议每40ms发送40ms时长的音频数据
3. **连接管理**: 及时关闭WebSocket连接，避免资源泄露
4. **错误重试**: 实现适当的错误重试机制
5. **并发控制**: 注意腾讯云的并发限制（默认200路）

### 8. 调试技巧

1. **查看生成的WSS URL**: 检查参数是否正确
2. **验证签名**: 确保时间戳、随机数和签名计算正确
3. **测试连接**: 使用WebSocket测试工具验证连接
4. **监控日志**: 查看服务端日志了解详细错误信息

### 9. 性能优化

1. **复用连接**: 避免频繁创建和销毁WebSocket连接
2. **批量处理**: 合理批量处理识别结果
3. **缓存配置**: 缓存常用的配置参数
4. **异步处理**: 使用异步方式处理识别结果
