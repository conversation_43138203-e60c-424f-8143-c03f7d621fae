package com.facishare.crm.sfa.activity.predefine.service.asr.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.MethodType;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.rest.StoneAuthProxy;
import com.facishare.crm.rest.dto.StoneAuthModels;
import com.facishare.crm.sfa.activity.predefine.service.asr.AbstractAsrService;
import com.facishare.crm.sfa.activity.predefine.service.model.ActivityText;
import com.facishare.crm.sfa.lto.activity.service.ActivityTaskStateService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 阿里云ASR服务实现
 * 
 * <AUTHOR>
 * @date 2025/1/22
 * @description 基于阿里云听悟服务的ASR实现
 */
@Component
@Slf4j
public class AliyunAsrService extends AbstractAsrService {

    @Resource
    private ActivityTaskStateService activityTaskStateService;

    @Resource
    private StoneAuthProxy stoneAuthProxy;

    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;

    @Override
    public String getProviderName() {
        return "aliyun";
    }

    @Override
    public TaskResult startRealtimeTranscription(ServiceContext context, ActivityText.ActivityRealtimeStartArg arg) {
        TaskResult result = new TaskResult();
        result.setProvider(getProviderName());
        
        try {
            StoneAuthModels.TingWuCredentialData credentialData = getTingWuCredential(context);
            
            // 创建请求
            CommonRequest request = createCommonRequest(credentialData);
            request.putQueryParameter("type", "realtime");

            // 构建请求体
            JSONObject root = new JSONObject();
            root.put("AppKey", credentialData.getAppKey());

            JSONObject input = new JSONObject();
            input.put("SourceLanguage", StringUtils.isBlank(arg.getSourceLanguage()) ? "cn" : arg.getSourceLanguage());
            input.put("SampleRate", 16000);
            input.put("Format", "pcm");
            input.put("EnableRealTimeTranscription", true);
            input.put("DiarizationEnabled", arg.isDiarizationEnabled());

            // 翻译配置
            if (arg.isTranslationEnabled() && StringUtils.isNotBlank(arg.getTargetLanguages())) {
                input.put("TranslationEnabled", true);
                input.put("TargetLanguages", new String[]{arg.getTargetLanguages()});
            }

            JSONObject parameters = new JSONObject();
            parameters.put("TranscriptionConfig", new JSONObject());

            String ossPath = null;
            // 检查是否保存录音
            String dontSaveRealTimeRecordAudio = bizConfigThreadLocalCacheService.getBizConfig(
                    context.getTenantId(), ConfigType.DONT_SAVE_REAL_TIME_RECORD_AUDIO.getKey());
            
            if ("0".equals(dontSaveRealTimeRecordAudio)) {
                ossPath = buildOssPath(context, credentialData);
                input.put("OutputPath", ossPath);
                JSONObject transcoding = new JSONObject();
                transcoding.put("TargetAudioFormat", "mp3");
                transcoding.put("SpectrumEnabled", false);
                parameters.put("Transcoding", transcoding);
            }

            root.put("Input", input);
            root.put("Parameters", parameters);

            // 设置请求内容
            request.setHttpContent(root.toJSONString().getBytes(), "utf-8", FormatType.JSON);

            // 发送请求
            CommonResponse commonResponse = getCommonResponse(request, credentialData);
            
            if (commonResponse.getHttpStatus() == 200) {
                JSONObject responseJson = JSONObject.parseObject(commonResponse.getData());
                JSONObject data = responseJson.getJSONObject("Data");
                
                if (data != null) {
                    result.setTaskId(data.getString("TaskId"));
                    result.setWsUrl(data.getString("MeetingJoinUrl"));
                    
                    // 发送任务ID到MQ
                    sendTaskId(context, arg, result.getTaskId(), ossPath);
                    
                    log.info("Aliyun ASR task started successfully: taskId={}", result.getTaskId());
                } else {
                    log.error("Aliyun ASR start failed: {}", commonResponse.getData());
                    throw new RuntimeException("Failed to start Aliyun ASR task");
                }
            } else {
                log.error("Aliyun ASR request failed: status={}, response={}", 
                         commonResponse.getHttpStatus(), commonResponse.getData());
                throw new RuntimeException("Aliyun ASR request failed");
            }
            
        } catch (Exception e) {
            log.error("Start Aliyun realtime transcription failed", e);
            throw new RuntimeException("Failed to start Aliyun ASR: " + e.getMessage(), e);
        }
        
        return result;
    }

    @Override
    public Map<String, String> stopRealtimeTranscription(ServiceContext context, ActivityText.ActivityRealtimeStopArg arg) {
        Map<String, String> result = new HashMap<>();
        
        if (StringUtils.isBlank(arg.getTaskId())) {
            updateInteractiveProcesses(context.getUser(), arg.getObjectId());
            result.put("status", "success");
            return result;
        }
        
        try {
            // 重置任务状态
            try {
                activityGeneralService.initEndRecordingState(context.getTenantId(), 
                                                           context.getUser().getUserId(), 
                                                           arg.getObjectId());
            } catch (Exception e) {
                log.error("initEndRecordingState error", e);
            }

            StoneAuthModels.TingWuCredentialData credentialData = getTingWuCredential(context);
            
            // 创建停止请求
            CommonRequest request = createCommonRequest(credentialData);
            
            // 构建请求体
            JSONObject root = new JSONObject();
            JSONObject input = new JSONObject();
            input.put("TaskId", arg.getTaskId());
            root.put("Input", input);
            
            // 设置请求内容
            request.setHttpContent(root.toJSONString().getBytes(), "utf-8", FormatType.JSON);
            
            // 发送请求
            CommonResponse commonResponse = getCommonResponse(request, credentialData);
            
            // 停止任务后，再次获取任务结果
            String queryUrl = String.format("/openapi/tingwu/v2/tasks/%s", arg.getTaskId());
            CommonRequest getRequest = createCommonRequest(credentialData, queryUrl, MethodType.GET);
            CommonResponse resultResponse = getCommonResponse(getRequest, credentialData);
            
            log.info("Get task result after stop: taskId={}, response={}", arg.getTaskId(), resultResponse.getData());
            
            sendMQ(arg.getObjectId(), context.getTenantId(), context.getUser().getUserId(), 
                   "realtime2textDone", "realtime2textDone", context.getLang().getValue());
            
            updateInteractiveProcesses(context.getUser(), arg.getObjectId());
            result.put("status", "success");
            
        } catch (Exception e) {
            log.error("停止实时转写任务失败", e);
            result.put("status", "error");
            result.put("message", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getServiceConfig(ServiceContext context) {
        Map<String, Object> config = new HashMap<>();
        config.put("provider", getProviderName());
        config.put("supportedLanguages", new String[]{"cn", "en", "yue", "ja", "ko"});
        config.put("supportedFormats", new String[]{"pcm", "wav", "mp3"});
        config.put("maxConcurrency", 200);
        return config;
    }

    @Override
    protected boolean checkServiceDependencies(ServiceContext context) {
        try {
            StoneAuthModels.TingWuCredentialData credentialData = getTingWuCredential(context);
            return credentialData != null && 
                   StringUtils.isNotBlank(credentialData.getAppKey()) &&
                   StringUtils.isNotBlank(credentialData.getAccessKeyId()) &&
                   StringUtils.isNotBlank(credentialData.getAccessKeySecret());
        } catch (Exception e) {
            log.error("Check Aliyun ASR dependencies failed", e);
            return false;
        }
    }

    // 以下是从原Activity2TextService中提取的私有方法
    
    private StoneAuthModels.TingWuCredentialData getTingWuCredential(ServiceContext context) {
        // 实现获取听悟凭证的逻辑
        // 这里需要从原代码中提取具体实现
        throw new UnsupportedOperationException("getTingWuCredential method needs to be implemented");
    }

    private CommonRequest createCommonRequest(StoneAuthModels.TingWuCredentialData credentialData) {
        return createCommonRequest(credentialData, "/openapi/tingwu/v2/tasks", MethodType.PUT);
    }

    private CommonRequest createCommonRequest(StoneAuthModels.TingWuCredentialData credentialData, 
                                            String path, MethodType method) {
        // 实现创建通用请求的逻辑
        // 这里需要从原代码中提取具体实现
        throw new UnsupportedOperationException("createCommonRequest method needs to be implemented");
    }

    private CommonResponse getCommonResponse(CommonRequest request, 
                                           StoneAuthModels.TingWuCredentialData credentialData) throws ClientException {
        // 实现获取通用响应的逻辑
        // 这里需要从原代码中提取具体实现
        throw new UnsupportedOperationException("getCommonResponse method needs to be implemented");
    }

    private void sendTaskId(ServiceContext context, ActivityText.ActivityRealtimeStartArg arg, 
                           String taskId, String ossPath) {
        // 实现发送任务ID的逻辑
        // 这里需要从原代码中提取具体实现
    }

    private void updateInteractiveProcesses(Object user, String objectId) {
        // 实现更新交互进程的逻辑
        // 这里需要从原代码中提取具体实现
    }
}
