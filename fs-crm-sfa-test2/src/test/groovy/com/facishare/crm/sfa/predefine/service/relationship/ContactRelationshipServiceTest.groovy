package com.facishare.crm.sfa.predefine.service.relationship

import com.beust.jcommander.internal.Maps

import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.predefine.SFAPreDefineObject
import com.facishare.crm.sfa.predefine.service.model.ContactRelationshipModel
import com.facishare.crm.sfa.task.RecalculateProducer
import com.facishare.crm.util.CommonBizOrgUtils
import com.facishare.paas.appframework.common.service.OrgService
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.license.Result.ModuleInfoResult
import com.facishare.paas.license.arg.QueryModuleArg
import com.facishare.paas.license.http.LicenseClient
import com.facishare.paas.license.pojo.ModuleInfoPojo
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.IObjectDescribeService
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl
import com.facishare.paas.metadata.util.SpringUtil
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.Logger
import org.spockframework.runtime.Sputnik
import spock.lang.Shared

import java.lang.reflect.Method

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([SFAPreDefineObject.class,CommonBizOrgUtils.class, SpringUtil.class])
// 限制 RequestUtil, AccountAddrService 类里的静态代码块初始化, 这里会加载一些静态资源，可以禁止初始化。
@SuppressStaticInitializationFor([
        "com.facishare.crm.sfa.predefine.SFAPreDefineObject",
        "com.facishare.paas.appframework.core.util.RequestUtil"
])
class ContactRelationshipServiceTest extends EnhancedBaseGroovyTest {
    @Shared
    ContactRelationshipService contactRelationshipService
    @Shared
    RecalculateProducer recalculateProducer
    @Shared
    ServiceFacade serviceFacade
    @Shared
    IObjectDescribeService objectDescribeService
    @Shared
    ObjectDataServiceImpl objectDataService
    @Shared
    ConfigService configService
    @Shared
    User user
    def setupSpec() {

        removeConfigFactory()
        removeI18N()
        initSpringContext()
        user = PowerMockito.spy(new User("71568", "-10000"));
        configService = PowerMockito.mock(ConfigService)
        objectDataService = PowerMockito.mock(ObjectDataServiceImpl)
        objectDescribeService = PowerMockito.mock(IObjectDescribeService)
        serviceFacade = PowerMockito.mock(ServiceFacade)
        recalculateProducer = PowerMockito.mock(RecalculateProducer)
        contactRelationshipService = PowerMockito.spy(new ContactRelationshipService())
        Whitebox.setInternalState(ContactRelationshipService, "log", Mock(Logger))
////        // 设置静态变量的值
        Whitebox.setInternalState(ContactRelationshipService.class, "categoriesList", Lists.newArrayList());
        Whitebox.setInternalState(ContactRelationshipService.class, "apiNameList", Lists.newArrayList("LeadsObj"));

        SFAPreDefineObject AccountObj = PowerMockito.mock(SFAPreDefineObject);
        PowerMockito.when(AccountObj.getApiName()).thenReturn("AccountObj");
        Whitebox.setInternalState(SFAPreDefineObject.class, "Account", AccountObj);
    }

/**
 *
 * @return
 */
    def "uploadPhone"() {
        given:

        def arg = new ContactRelationshipModel.Arg()
        arg.setPhoneList(phoneList)

        Whitebox.setInternalState(contactRelationshipService, recalculateProducer, recalculateProducer)

        Whitebox.setInternalState(contactRelationshipService, "configService", configService)

        PowerMockito.doReturn(queryRst).when(configService, "findTenantConfig", any(), any())
        when:
        contactRelationshipService.uploadPhone(getServiceContext(user,"ContactRelationshipService", "uploadPhone"), arg)
        then:
        notThrown(Exception)
        where:
        getAddressBookSettingConfigValue | phoneList                    |queryRst
        false                            | null                         |"xxxx"
        true                             | null                         |"2"
        true                             | Lists.newArrayList("xxxx")   |"2";
    }
/**
 *
 * @return
 */
    def "getRelationshiData"() {
        given:

        def arg = new ContactRelationshipModel.Arg()
        arg.setObjectApiName("xxxx")
        arg.setObjectDataId(objectDataId)

        Whitebox.setInternalState(contactRelationshipService, "objectDataService", objectDataService)
        Whitebox.setInternalState(contactRelationshipService, "recalculateProducer", recalculateProducer)
        Whitebox.setInternalState(contactRelationshipService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(contactRelationshipService, "objectDescribeService", objectDescribeService)
        Whitebox.setInternalState(contactRelationshipService, "configService", configService)

        PowerMockito.doReturn(queryRst).when(configService, "findTenantConfig", any(), any())
        PowerMockito.doReturn(result).when(objectDataService, "findBySql", anyString(), anyString())
        PowerMockito.doReturn(isAdmin).when(serviceFacade, "isAdmin", any())
        PowerMockito.doReturn(iObjectDescribe).when(objectDescribeService, "findByTenantIdAndDescribeApiName", any(), any())

        when:
        contactRelationshipService.getRelationshiData(getServiceContext(user,"ContactRelationshipService", "getRelationshiData"), arg)
        then:
        true

        where:
        objectDataId | queryRst | isAdmin | accountIdList              | accountNameMap                                                                   | result                                                                       | iObjectDescribe
        null         | "xx"     | true    | null                       | null                                                                             | null                                                                         | null
        "xxxx"       | "xx"     | true    | null                       | null                                                                             | null                                                                         | null
        "xxxx"       | "xx"     | false   | null                       | null                                                                             | null                                                                         | null
        "xxxx"       | "2"      | false   | null                       | null                                                                             | null                                                                         | null
        "xxxx"       | "2"      | false   | Lists.newArrayList("xxxx") | null                                                                             | null                                                                         | null
        "xxxx"       | "2"      | false   | Lists.newArrayList("xxxx") | Maps.newHashMap("xxxx", Maps.newHashMap("name", "xxx", "life_status", "normal")) | null                                                                         | null
        "xxxx"       | "2"      | false   | Lists.newArrayList("xxxx") | Maps.newHashMap("xxxx", Maps.newHashMap("name", "xxx", "life_status", "normal")) | Lists.newArrayList(Maps.newHashMap("data_id", "xxxx", "create_time", 43435)) | getDescribe("xxxx");
    }
/**
 *
 * @return
 */
    def "getDataById"() {
        given:

        def arg = new ContactRelationshipModel.Arg()
        arg.setObjectApiName("xxxx")
        arg.setObjectDataId(objectDataId)

        Whitebox.setInternalState(contactRelationshipService, "objectDataService", objectDataService)
        Whitebox.setInternalState(contactRelationshipService, "recalculateProducer", recalculateProducer)
        Whitebox.setInternalState(contactRelationshipService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(contactRelationshipService, "objectDescribeService", objectDescribeService)

        PowerMockito.when(serviceFacade.findBySearchQuery(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)

        PowerMockito.doReturn(iObjectDescribe).when(objectDescribeService, "findByTenantIdAndDescribeApiName", any(), any())

        when:
        contactRelationshipService.getDataById(getServiceContext(user,"ContactRelationshipService", "getDataById"), arg)
        then:
        true

        where:
        objectDataId | iObjectDescribe    | queryResult
        null         | null               | null
        "xxx"        | getDescribe("xxx") | null
        "xxx"        | getDescribe("xxx") | getQueryResult();
    }
/**
 *
 * @return
 */
    def "get_contact_member_relationship_license"() {
        given:

        def arg = new ContactRelationshipModel.Arg()

        def licenseClient = Mock(LicenseClient) {
            queryModule(_ as QueryModuleArg) >> result
        }
        Whitebox.setInternalState(contactRelationshipService, "licenseClient", licenseClient)


        when:
        contactRelationshipService.getContactMemberRelationshipLicense(getServiceContext(user,"ContactRelationshipService", "getContactMemberRelationshipLicense"), arg)
        then:
        true

        where:
        result                                  | iObjectDescribe
        null                                    | null
        getModuleInfoResult("xx")               | null
        getModuleInfoResult("networking_radar") | null;
    }
/**
 *
 * @return
 */
    def "getAccountId"() {
        given:


        Whitebox.setInternalState(contactRelationshipService, "objectDataService", objectDataService)

        PowerMockito.doReturn(result).when(objectDataService, "findBySql", any(), any())

        when:
        // 使用反射调用私有方法
        Method method = ContactRelationshipService.class.getDeclaredMethod("getAccountId", String.class, String.class, String.class)
        method.setAccessible(true)
        method.invoke(contactRelationshipService, this.user.getTenantId(), "xxxx", "xxxx")
        then:
        true

        where:
        result                                                                          | getConfigValue
        null                                                                            | null
        Lists.newArrayList(Maps.newHashMap("account_id", "xxxx", "create_time", 43435)) | null;
    }
/**
 *
 * @return
 */
    def "getNameById"() {
        given:


        Whitebox.setInternalState(contactRelationshipService, "objectDataService", objectDataService)

        PowerMockito.doReturn(result).when(objectDataService, "findBySql", any(), any())

        when:
        // 使用反射调用私有方法
        Method method = ContactRelationshipService.class.getDeclaredMethod("getNameById", String.class, String.class, List.class)
        method.setAccessible(true)
        method.invoke(contactRelationshipService, this.user.getTenantId(), "xxxx", Lists.newArrayList("xxxx"))
        then:
        true

        where:
        result                                                                  | getConfigValue
        null                                                                    | null
        Lists.newArrayList(Maps.newHashMap("id", "xxxx", "create_time", 43435)) | null;
    }
/**
 *
 * @return
 */
    def "getMemberNameByUserId"() {
        given:


        Whitebox.setInternalState(contactRelationshipService, "objectDataService", objectDataService)

        PowerMockito.doReturn(result).when(objectDataService, "findBySql", any(), any())

        when:
        // 使用反射调用私有方法
        Method method = ContactRelationshipService.class.getDeclaredMethod("getMemberNameByUserId", String.class, Map.class, Map.class)
        method.setAccessible(true)
        method.invoke(contactRelationshipService, this.user.getTenantId(), memberMap, memberName)
        then:
        true

        where:
        memberMap                                          | memberName                                                   | result
        null                                               | null                                                         | null
        Maps.newHashMap("xxxx", Sets.newHashSet("xxxxxx")) | Maps.newHashMap("xxxxxx", Maps.newHashMap("xxxxxx", "xxxx")) | null
        Maps.newHashMap("xxxx", Sets.newHashSet("xxxxxx")) | Maps.newHashMap("xxx", Maps.newHashMap("xxxxxx", "xxxx"))    | null
        Maps.newHashMap("xxxx", Sets.newHashSet("xxxxxx")) | Maps.newHashMap("xxx", Maps.newHashMap("xxxxxx", "xxxx"))    | Lists.newArrayList(Maps.newHashMap("user_id", "xxxx", "create_time", 43435));
    }
/**
 *
 * @return
 */
    def "createrelationshiData"() {
        given:


        when:
        // 使用反射调用私有方法
        Method method = ContactRelationshipService.class.getDeclaredMethod("createrelationshiData", User.class, String.class, Map.class, List.class, Map.class, List.class, List.class, Integer.class, Integer.class, IObjectDescribe.class, Map.class)
        method.setAccessible(true)
        method.invoke(contactRelationshipService, this.user, accountId, apiNameDataNameMap, ids, memberNameMap, nodes, links, categoriesPositionOfApiName, categoriesPositionOfMember, iObjectDescribe, memberMap)
        then:
        true

        where:
        accountId | apiNameDataNameMap                                                               | ids                       | memberNameMap                                                           | nodes                | links                | categoriesPositionOfApiName | categoriesPositionOfMember | iObjectDescribe    | memberMap
        "xx"      | Maps.newHashMap("xxx", Maps.newHashMap("name", "xxxx", "life_status", "normal")) | Lists.newArrayList("xxx") | Maps.newHashMap("xxxx", Maps.newHashMap("name", "xxxx", "status", "1")) | Lists.newArrayList() | Lists.newArrayList() | 20                          | 20                         | getDescribe("xxx") | Maps.newHashMap("xxx", Sets.newHashSet("xxxx"));
    }
/**
 *
 * @return
 */
    def "getPositionOfCategories"() {
        given:


        when:
        // 使用反射调用私有方法
        Method method = ContactRelationshipService.class.getDeclaredMethod("getPositionOfCategories", List.class, String.class)
        method.setAccessible(true)
        method.invoke(contactRelationshipService, categories, apiName)
        then:
        true

        where:
        categories                          | apiName
        Lists.newArrayList(getCategories()) | "xxx";
    }
/**
 *
 * @return
 */
    def "flattenMemberFormDtTeam"() {
        given:

        def orgService = PowerMockito.mock(OrgService)
        Whitebox.setInternalState(contactRelationshipService, "orgService", orgService)

        PowerMockito.doReturn(deptMemberIds).when(orgService, "getMembersByDeptIds", any(), any())
        PowerMockito.doReturn(deptMemberIds).when(orgService, "getMembersByGroupIds", any(), any())
        when:
        // 使用反射调用私有方法
        Method method = ContactRelationshipService.class.getDeclaredMethod("flattenMemberFormDtTeam", User.class, Map.class)
        method.setAccessible(true)
        method.invoke(contactRelationshipService, this.user, relationshiMap)
        then:
        true

        where:
        relationshiMap                                                                                     | deptMemberIds
        null                                                                                               | Lists.newArrayList("xxx")
        Maps.newHashMap("xxxx", Lists.newArrayList(Maps.newHashMap("xxxxxxxxxxx", "xxx")))                 | Lists.newArrayList("xxx")
        Maps.newHashMap("xxxx", Lists.newArrayList(Maps.newHashMap("member_type", "0", "member", "xxxx"))) | Lists.newArrayList("xxx")
        Maps.newHashMap("xxxx", Lists.newArrayList(Maps.newHashMap("member_type", "2", "member", "xxxx"))) | Lists.newArrayList("xxx")
        Maps.newHashMap("xxxx", Lists.newArrayList(Maps.newHashMap("member_type", "1", "member", "xxxx"))) | Lists.newArrayList("xxx")
        Maps.newHashMap("xxxx", Lists.newArrayList(Maps.newHashMap("member_type", "4", "member", "xxxx"))) | Lists.newArrayList("xxx");
    }


/**
 *
 * @return
 */
    def "getConfigValue"() {
        given:


        Whitebox.setInternalState(contactRelationshipService, "configService", configService)

        PowerMockito.doReturn(queryRst).when(configService, "findTenantConfig", any(), any())

        when:
        contactRelationshipService.getConfigValue(this.user)
        then:
        true

        where:
        queryRst | iObjectDescribe
        null     | null
        "xx"     | null;
    }
/**
 *
 * @return
 */
    def "getAddressBookSettingConfigValue"() {
        given:


        Whitebox.setInternalState(contactRelationshipService, "configService", configService)

        PowerMockito.doReturn(queryRst).when(configService, "findTenantConfig", any(), any())

        when:
        contactRelationshipService.getAddressBookSettingConfigValue(this.user)
        then:
        true

        where:
        queryRst | iObjectDescribe
        null     | null
        "2"      | null;
    }
/**
 *
 * @return
 */
    def "handleLifeStatus"() {
        given:

        when:
        // 使用反射调用私有方法
        Method method = ContactRelationshipService.class.getDeclaredMethod("handleLifeStatus", String.class)
        method.setAccessible(true)
        method.invoke(contactRelationshipService, lifeStatus)
        then:
        true

        where:
        lifeStatus | deptMemberIds
        "invalid"  | Lists.newArrayList("xxx")
        "normal"   | Lists.newArrayList("xxx");
    }

    ModuleInfoResult getModuleInfoResult(String code) {
        ModuleInfoResult result = new ModuleInfoResult();
        List<ModuleInfoPojo> modules = new ArrayList<>();
        ModuleInfoPojo ModuleInfoPojo = new ModuleInfoPojo();
        ModuleInfoPojo.setModuleCode(code)
        modules.add(ModuleInfoPojo)
        result.setResult(modules)
        return result;
    }

    ContactRelationshipModel.Categories getCategories() {
        ContactRelationshipModel.Categories categories = ContactRelationshipModel.Categories.builder().build();
        categories.setKey("xxx")
        return categories;
    }
}
