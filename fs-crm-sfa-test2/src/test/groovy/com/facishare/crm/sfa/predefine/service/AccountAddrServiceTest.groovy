package com.facishare.crm.sfa.predefine.service

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.beust.jcommander.internal.Maps
import com.facishare.crm.describebuilder.QuoteFieldDescribeBuilder
import com.facishare.crm.openapi.Utils
import com.facishare.crm.privilege.service.PrivilegeService
import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.lto.utils.SearchUtil
import com.facishare.crm.sfa.utilities.util.AccountUtil
import com.facishare.crm.sfa.predefine.SFAPreDefineObject
import com.facishare.crm.sfa.predefine.service.manager.TableComponentBuilderNew
import com.facishare.crm.sfa.predefine.service.model.FindAccountAddrList
import com.facishare.crm.sfa.predefine.service.model.FindNearby
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission
import com.facishare.crm.sfa.predefine.service.model.updateOrInsertAccountAddResult
import com.facishare.crm.sfa.utilities.constant.AccountsReceivableNoteObjConstants
import com.facishare.crm.sfa.utilities.util.*
import com.facishare.paas.appframework.common.util.StopWatch
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.log.ActionType
import com.facishare.paas.appframework.log.EventType
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.metadata.api.DBRecord
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.api.search.ISearchTemplate
import com.facishare.paas.metadata.api.search.Wheres
import com.facishare.paas.metadata.common.MetadataConstant
import com.facishare.paas.metadata.impl.describe.QuoteFieldDescribe
import com.facishare.paas.metadata.impl.describe.SelectManyFieldDescribe
import com.facishare.paas.metadata.impl.search.DataRightsParameter
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.impl.search.Where
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent
import com.facishare.paas.metadata.ui.layout.ILayout
import com.facishare.paas.metadata.util.SpringUtil
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.Logger
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.*

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([AccountAddrUtil.class, LayoutUtils.class, SpringUtil.class, LogUtil.class,ObjectPoolUtil.class, AccountUtil.class,UdobjGrayConfig.class,ContactAtlasUtil.class])
// 限制 RequestUtil, AccountAddrService 类里的静态代码块初始化, 这里会加载一些静态资源，可以禁止初始化。
@SuppressStaticInitializationFor(["com.facishare.paas.appframework.core.util.RequestUtil", "com.facishare.crm.sfa.predefine.service.AccountAddrService"])
class AccountAddrServiceTest extends EnhancedBaseGroovyTest {
    @Shared
    private User user
    @Shared
    private String tenantId
    @Shared
    private ServiceFacade serviceFacade
    @Shared
    ObjectPoolService objectPoolService
    @Shared
    ConfigService configService
    @Shared
    AccountAddrService accountAddrService

    def setupSpec() {

        removeConfigFactory()
        removeI18N()
        initSpringContext()
        System.out.println("setupSpec："+System.currentTimeMillis())
        tenantId = "1"
        System.out.println("initSpringContext："+System.currentTimeMillis())
        //user = createUser(tenantId: tenantId, userId: "1000")
        user = User.systemUser("82681")
        System.out.println("createUser："+System.currentTimeMillis())
        System.out.println("createRequestContext："+System.currentTimeMillis())
        accountAddrService = PowerMockito.spy(new AccountAddrService())
        serviceFacade = PowerMockito.mock(ServiceFacade)
        objectPoolService = PowerMockito.mock(ObjectPoolService)
        configService = PowerMockito.mock(ConfigService.class)
        System.out.println("serviceFacade："+System.currentTimeMillis())
        //PowerMockito.mockStatic(AccountAddrService.class)
        // 设置静态变量的值
        Whitebox.setInternalState(AccountAddrService.class, "landingFieldsMap", Maps.newHashMap("name", "1","111","biz_status","biz_status","111"));
        Whitebox.setInternalState(AccountAddrService.class, "accountMustNeedFields",  Lists.newArrayList("owner","name", DBRecord.ID, "high_seas_name", "high_seas_id"));
        Whitebox.setInternalState(AccountAddrService.class, "landingFieldsList",  Lists.newArrayList("owner","name", DBRecord.ID, "high_seas_name", "high_seas_id"));
        Whitebox.setInternalState(AccountAddrService.class, "maxAccountNum", 1);
        Whitebox.setInternalState(AccountAddrService.class, "nearbyKilometers", "dewdew");
        Whitebox.setInternalState(AccountAddrService, "log", Mock(Logger))
    }
    def setup() {
        def applicationContext = PowerMockito.mock(ApplicationContext)
        PowerMockito.mockStatic(SpringUtil)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(applicationContext)
        PowerMockito.stub(PowerMockito.method(UdobjGrayConfig.class, "isAllow", String.class, String.class))
                .toReturn(true)
    }


/**
 *
 * @return
 */
    def "findNearby"() {
        given:

        def arg = new FindNearby.Arg()
        arg.setLongitude(longitude)
        arg.setLatitude(latitude)
        arg.setSearchQueryInfo(searchQueryInfo)
        arg.setSkipPrivilege(skipPrivilege)
        arg.setType(type)
        arg.setNeedFieldMap(new HashMap<>())
        arg.setIncludeAllData(includeAllData)
        arg.setIncludeDescribe(includeDescribe)
        arg.setDescribeVersionMap(new HashMap<>())
        arg.setNeedSuppleLayout(needSuppleLayout)
        arg.setNeedSuppleConfig(needSuppleConfig)
        arg.setIncludeLayout(includeLayout)
        
        Whitebox.setInternalState(accountAddrService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn([
                (Utils.ACCOUNT_API_NAME)     : getDescribe(Utils.ACCOUNT_API_NAME),
                (Utils.ACCOUNT_ADDR_API_NAME): getDescribe(Utils.ACCOUNT_ADDR_API_NAME)
        ]).when(serviceFacade, "findObjects", any(), any(List));
        PowerMockito.stub(PowerMockito.method(LayoutUtils.class, "findMobileLayouts", User.class, ObjectDescribeExt.class))
                .toReturn(Lists.newArrayList())
        PowerMockito.stub(PowerMockito.method(AccountUtil.class, "isGrayIncludeLayout", String.class))
                .toReturn(true)
        PowerMockito.doReturn(new HashMap()).when(accountAddrService, "getNeedFieldMap", any(), any(), any(), anyBoolean(), eq(needSuppleConfig), eq(needSuppleConfig), any(), any())
        PowerMockito.doReturn(hasFieldPrivilege).when(accountAddrService, "checkFieldPrivilege", any(User) as User)
        PowerMockito.doReturn(highSeasIds).when(accountAddrService, "getHighSeasIds", any(User) as User)
        PowerMockito.doReturn(accountAddrList).when(accountAddrService, "findAccountAddrList", any(), any(), any(), any(), any(), any(), any())
        PowerMockito.doReturn(null).when(accountAddrService, "findAccountList", any(), any(), any());
        PowerMockito.doNothing().when(accountAddrService, "fillInfo", any(), any(), any(), eq(includeAllData), any(), any(), any());
        PowerMockito.doReturn(null).when(accountAddrService, "buildResult", any(), eq(includeDescribe), anyBoolean(), any(), any(), any(), any(),);
        if (accountAddrList == null) {
            PowerMockito.doReturn(null).when(accountAddrService, "buildResult", any(), eq(includeDescribe), any(), eq(0), eq(includeLayout), any(), any(), any(), any());
        } else {
            PowerMockito.doReturn(null).when(accountAddrService, "buildResult", any(), eq(includeDescribe), any(), eq(accountAddrList.size()), eq(includeLayout), any(), any(), any(), any());
        }
        when:
        accountAddrService.findNearby(getServiceContext(user,"AccountAddrService", "findNearby"), arg)
        then:
        true

        where:
        longitude | latitude | searchQueryInfo | skipPrivilege | type         | needFieldMap    | includeAllData | includeDescribe | describeVersionMap | needSuppleLayout | needSuppleConfig | includeLayout | highSeasIds   | hasFieldPrivilege | accountAddrList                             | exception2
        1.0       | 1.0      | "{}"            | false         | "un_claimed" | new HashMap<>() | true           | true            | new HashMap<>()    | true             | true             | true          | ["xxxxxxxxx"] | true              | getObjectDataList(["account_id", "_id"], 3) | OK
        1.0       | 1.0      | "{}"            | false         | "un_claimed" | new HashMap<>() | true           | true            | new HashMap<>()    | true             | true             | true          | ["xxxxxxxxx"] | false             | getObjectDataList(["account_id", "_id"], 3) | OK
        1.0       | 1.0      | "{}"            | false         | "un_claimed" | new HashMap<>() | true           | true            | new HashMap<>()    | true             | true             | true          | null          | true              | getObjectDataList(["account_id", "_id"], 3) | OK
        1.0       | 1.0      | "{}"            | false         | "follow_up"  | new HashMap<>() | true           | true            | new HashMap<>()    | true             | true             | true          | null          | false             | null                                        | OK
        1.0       | 1.0      | "{}"            | false         | "follow_up"  | new HashMap<>() | true           | true            | new HashMap<>()    | true             | true             | true          | null          | false             | getObjectDataList(["account_id", "_id"], 1) | OK
    }


    def "fillInfo method"() {
        given:
        Whitebox.setInternalState(accountAddrService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(Sets.newHashSet("name")).when(serviceFacade, "getUnauthorizedFields",any(User) as User, anyString())

        PowerMockito.stub(PowerMockito.method(ContactAtlasUtil.class, "asyncFillData", User.class, List.class, IObjectDescribe.class))
                .toReturn()
        Whitebox.setInternalState(accountAddrService, "objectPoolService", objectPoolService)
        PowerMockito.doReturn(Maps.newHashMap("xxxx", ObjectPoolPermission.ObjectPoolPermissions.POOL_MEMBER)).when(objectPoolService, "batchGetPoolPermission", anyString(), anyString(), anyString(), anyList(), anyString())
        when:
        Whitebox.invokeMethod(accountAddrService, "fillInfo", accountAddrList,accountList,this.user,includeAllData,accountAddrDescribe,accountDescribe,needFieldMap)
        then:
        notThrown(Exception)
        where:
        accountAddrList                             | accountList                           | includeAllData  | accountAddrDescribe                      | accountDescribe                     | needFieldMap
        getObjectDataList(["account_id", "_id"], 3) | getObjectDataList(["name", "_id"], 3) | true            | getDescribe(Utils.ACCOUNT_ADDR_API_NAME) | getDescribe(Utils.ACCOUNT_API_NAME) | Maps.newHashMap()
        getObjectDataList(["account_id", "_id"], 3) | getObjectDataList(["name", "_id"], 3) | true            | getDescribe(Utils.ACCOUNT_ADDR_API_NAME) | getDescribe(Utils.ACCOUNT_API_NAME) | Maps.newHashMap(Utils.ACCOUNT_API_NAME, Lists.newArrayList("name"))
        getObjectDataList(["account_id", "_id"], 3) | getObjectDataList(["name", "_id"], 3) | false           | getDescribe(Utils.ACCOUNT_ADDR_API_NAME) | getDescribe(Utils.ACCOUNT_API_NAME) | Maps.newHashMap(Utils.ACCOUNT_API_NAME, Lists.newArrayList("name"));
    }
    def "find_account_addr_list"() {
        
        given:
        def arg = new FindAccountAddrList.Arg()
        searchQueryInfo.setDataRightsParameter(dataRightsParameter)
        arg.setSearchQueryInfo(getSearchQuery(searchQueryInfo))
        arg.setSearchTemplateId(searchTemplateId)
        arg.setIncludeLayout(includeLayout)
        arg.setDescribeVersionMap(new HashMap<>())
        arg.setNeedFieldMap(new HashMap<>())
        arg.setNeedSuppleLayout(true)
        arg.setNeedSuppleConfig(true)


        Whitebox.setInternalState(accountAddrService, "serviceFacade", serviceFacade)

        Map<String, String> landingFieldsMap = new HashMap<>();
        landingFieldsMap.put("name", "account_name");
        landingFieldsMap.put("record_type", "account_record_type");
        Whitebox.setInternalState(AccountAddrService.class, "landingFieldsMap", landingFieldsMap)

        PowerMockito.doReturn([
                (Utils.ACCOUNT_API_NAME)     : getDescribe(Utils.ACCOUNT_API_NAME),
                (Utils.ACCOUNT_ADDR_API_NAME): getDescribe(Utils.ACCOUNT_ADDR_API_NAME)
        ]).when(serviceFacade, "findObjects", anyString(), anyList())
        PowerMockito.doReturn(new SearchTemplateQuery()).when(serviceFacade, "getSearchTemplateQuery",  any(), any(), any(), any())
        PowerMockito.stub(PowerMockito.method(AccountUtil.class, "isGrayIncludeLayout", String.class))
                .toReturn(true)
        PowerMockito.stub(PowerMockito.method(LayoutUtils.class, "findMobileLayouts", User.class, ObjectDescribeExt.class))
                .toReturn(Lists.newArrayList())
        PowerMockito.stub(PowerMockito.method(AccountUtil.class, "isGrayFindAccountAddrUseDB", String.class)).toReturn(true)
        when:
        def result = accountAddrService.findAccountAddrList(getServiceContext(user,"AccountAddrService", "findNearby"), arg)
        throw new OK()
        then:
        thrown(exception)
        where:
        searchQueryInfo                      | dataRightsParameter       | searchTemplateId | includeLayout | describeVersionMap | needFieldMap    | needSuppleLayout | needSuppleConfig | getIsGoNewMethod | queryResult        | exception
        buildSearchQuery()                   | new DataRightsParameter() | null             | true          | new HashMap<>()    | new HashMap<>() | true             | true             | true             | buildQueryResult() | OK
        buildSearchQuery()                   | null                      | null             | false         | new HashMap<>()    | new HashMap<>() | true             | true             | false            | buildQueryResult() | OK
        isMasterField(Boolean.TRUE)          | null                      | null             | true          | new HashMap<>()    | new HashMap<>() | true             | true             | true             | null               | OK
        isMasterField(Boolean.FALSE)         | null                      | null             | true          | new HashMap<>()    | new HashMap<>() | true             | true             | false            | null               | OK
        isMasterFieldNoFilter(Boolean.TRUE)  | null                      | null             | true          | new HashMap<>()    | new HashMap<>() | true             | true             | true             | null               | OK
        isMasterFieldNoFilter(Boolean.FALSE) | null                      | null             | true          | new HashMap<>()    | new HashMap<>() | true             | true             | false            | null               | OK
    }


    def "updateOrInsertAccountAddr"() {
        given:

        def arg = new updateOrInsertAccountAddResult.Arg()
        arg.setAccountId(accountId)
        arg.setAddress(address)
        arg.setCity(city)
        arg.setCountry(country)
        arg.setDistrict(district)
        arg.setIncludeOtherField(includeOtherField)
        arg.setLocation(location)
        arg.setLocationId(locationId)
        arg.setProvince(province)
        arg.setTown(town)

        Whitebox.setInternalState(accountAddrService, "serviceFacade", serviceFacade)

        PowerMockito.doReturn([
                (Utils.ACCOUNT_API_NAME)     : getDescribe(Utils.ACCOUNT_API_NAME),
                (Utils.ACCOUNT_ADDR_API_NAME): getDescribe(Utils.ACCOUNT_ADDR_API_NAME)
        ]).when(serviceFacade, "findObjects", any(String), any(List));

        PowerMockito.doReturn(objectData).when(serviceFacade, "findObjectDataIgnoreAll", any(), any(), any());
        PowerMockito.doReturn(buildQueryResult()).when(serviceFacade, "findBySearchQuery", any(User), any(), any());
        PowerMockito.stub(PowerMockito.method(AccountAddrUtil.class, "handleGeoPointField", IObjectData.class)).toReturn(null)
        PowerMockito.stub(PowerMockito.method(AccountAddrUtil.class, "batchUpdateIgnoreOther", User.class,List.class,List.class)).toReturn(new ArrayList())
        PowerMockito.stub(PowerMockito.method(AccountAddrUtil.class, "insertAccountAddr", User.class,IObjectData.class,boolean.class,boolean.class,boolean.class)).toReturn(null)
        PowerMockito.stub(PowerMockito.method(AccountAddrUtil.class, "bulkUpdateAccountLocation", User.class,List.class,List.class,boolean.class)).toReturn(null)

        PowerMockito.stub(PowerMockito.method(LogUtil.class, "asyncRecordLogs", User.class,List.class,List.class, EventType.class, ActionType.class)).toReturn(null)

        when:
        accountAddrService.updateOrInsertAccountAddr(getServiceContext(user,"AccountAddrService", "updateOrInsertAccountAddr"), arg)
        then:
        OK
        where:

        accountId | location  | address | country | province | city  | district | town  | includeOtherField | locationId | objectData                                                                       | queryResult        | exception
//        null      | "xxx"     | "xxx"   | "xxx"   | "xxx"    | "xxx" | "xxx"    | "xxx" | true              | "xxx"      | null                                                        | null               | ValidateException
//        "xxx"     | null      | "xxx"   | "xxx"   | "xxx"    | "xxx" | "xxx"    | "xxx" | true              | "xxx"      | null                                                        | null               | ValidateException
//        "xxx"     | "xxx#%\$" | "xxx"   | "xxx"   | "xxx"    | "xxx" | "xxx"    | "xxx" | true              | "xxx"      | null                                                        | null               | ValidateException
//        "xxx"     | "xxx"     | "xxx"   | "xxx"   | "xxx"    | "xxx" | "xxx"    | "xxx" | true              | "xxx"      | null                                                        | null               | ValidateException
//        "xxx"     | "xxx"     | "xxx"   | "xxx"   | "xxx"    | "xxx" | "xxx"    | "xxx" | true              | "xxx"      | getObjectData(["object_describe_api_name"], ["LeadsObj"])   | null               | ValidateException
        "xxx"     | "xxx"     | "xxx"   | "xxx"   | "xxx"    | "xxx" | "xxx"    | "xxx" | true              | null       | getObjectData(["_id","object_describe_api_name"], ["dasd127635","AccountObj"])   | buildQueryResult() | OK
        "xxx"     | "xxx"     | "xxx"   | "xxx"   | "xxx"    | "xxx" | "xxx"    | "xxx" | true              | "wqw"      | getObjectData(["_id","object_describe_api_name"], ["dasd12763523","AccountObj"]) | buildQueryResult() | OK
    }

    def "changeSerchQuery method"() {
        given:
        
        when:
        Whitebox.invokeMethod(accountAddrService, "changeSerchQuery", searchTemplateQuery)
        then:
        notThrown(Exception)
        where:
        searchTemplateQuery << [null, buildSearchQuery(), isMasterField(true)]
    }

    def "getIsGoNewMethod method"() {
        given:
        
        def dataRightsParameter = buildDataRightsParameter(cascadeSubordinates, roleType, sceneType, isDetailObject)

        if (searchTemplateQuery != null) {
            searchTemplateQuery.setDataRightsParameter(dataRightsParameter)
        }
        when:
        def result = Whitebox.invokeMethod(accountAddrService, "getIsGoNewMethod", searchTemplateQuery)
        then:
        result == ret
        where:

        searchTemplateQuery         | cascadeSubordinates | roleType                            | sceneType                                  | isDetailObject | ret
        null                        | true                | null                                | null                                       | true           | true
        buildSearchQuery()          | true                | MetadataConstant.RoleTypeEnum.OWNER | MetadataConstant.SceneTypeEnum.USER        | true           | false
        buildSearchQuery()          | true                | MetadataConstant.RoleTypeEnum.OWNER | MetadataConstant.SceneTypeEnum.SUBORDINATE | true           | false
        buildSearchQuery()          | true                | MetadataConstant.RoleTypeEnum.OWNER | MetadataConstant.SceneTypeEnum.DEPT        | true           | false
        isMasterField(true)         | true                | null                                | null                                       | true           | false
        isMasterFieldNoFilter(true) | true                | null                                | null                                       | true           | false
        buildSearchQueryNoFilter()  | true                | null                                | null                                       | true           | true
    }
    def "handleInvisibleFields method"() {
        given:
        
        Whitebox.setInternalState(accountAddrService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(accountAddrService, "objectPoolService", objectPoolService)
        PowerMockito.doReturn(invisibleFields).when(serviceFacade, "getUnauthorizedFields",any(User) as User, anyString())
        PowerMockito.doReturn(poolPermissionsMap).when(objectPoolService, "batchGetPoolPermission", anyString(), anyString(), anyString(), anyList(), anyString())
        PowerMockito.stub(PowerMockito.method(ObjectPoolUtil.class, "getHideFields", String.class, String.class,List.class)).toReturn(Lists.newArrayList(Maps.newHashMap("pool_id", "xxxx","field_name","xxxx")))
        when:
        Whitebox.invokeMethod(accountAddrService, "handleInvisibleFields", this.user,objectDataList)
        then:
        notThrown(Exception)
        where:
        objectDataList                                                     | invisibleFields        |poolHideFields                                     |poolPermissionsMap
        getObjectDataList(["account_id", "_id", "name","high_seas_id"], 3) | Sets.newHashSet("name")|Maps.newHashMap("xxxx", Lists.newArrayList("name"))|new HashMap<>()
        getObjectDataList(["account_id", "_id", "name","high_seas_id"], 3) | Sets.newHashSet("name")|Maps.newHashMap("xxxx", Lists.newArrayList("name"))|Maps.newHashMap("xxxx", ObjectPoolPermission.ObjectPoolPermissions.POOL_MEMBER);
    }
    def "getPoolHideFields method"() {
        given:

        PowerMockito.stub(PowerMockito.method(ObjectPoolUtil.class, "getHideFields", String.class, String.class,List.class)).toReturn(hideFields)
        when:
        Whitebox.invokeMethod(accountAddrService, "getPoolHideFields", this.user, highSeasIds)
        then:
        OK
        where:
        objectDataList                                                     | highSeasIds            |hideFields
        getObjectDataList(["account_id", "_id", "name","high_seas_id"], 3) | Sets.newHashSet("xxxx")| Lists.newArrayList(Maps.newHashMap("pool_id", "xxxx","field_name","xxxx"));
    }

    def "findAccountList method"() {
        given:
        
        

        Whitebox.setInternalState(accountAddrService, "serviceFacade", serviceFacade)
        when:
        def result = Whitebox.invokeMethod(accountAddrService, "findAccountList", getServiceContext(user,"AccountAddrService", "findAccountList"), accountIds,accountDescribe)
        then:
        true
        where:
        accountIds                 | accountDescribe                    |ret
        Sets.newHashSet("xxxx") | getDescribe(Utils.ACCOUNT_API_NAME)|null;
    }

    def "findAccountAddrList method"() {
        given:
        
        def FindNearby.Arg arg = new FindNearby.Arg();
        def StopWatch stopWatch = StopWatch.create("findAccountAddrList");
        PowerMockito.stub(PowerMockito.method(AccountUtil.class, "isGrayFindAccountAddrUseDB", String.class)).toReturn(true)
        Whitebox.setInternalState(accountAddrService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(searchTemplateQuery).when(serviceFacade, "getSearchTemplateQuery",   any(), any(), any(), any())
        when:
        def result = Whitebox.invokeMethod(accountAddrService, "findAccountAddrList", getServiceContext(user,"AccountAddrService", "findAccountList"), arg,highSeasIds,
                accountAddrDescribe,accountDescribe,needFieldMap,stopWatch)
        then:
        true
        where:
        highSeasIds               | accountDescribe                    |accountAddrDescribe                     |needFieldMap                                                       |searchTemplateQuery                                                                              |queryResult        |ret
        Lists.newArrayList("xxxx")| getDescribe(Utils.ACCOUNT_API_NAME)|getDescribe(Utils.ACCOUNT_ADDR_API_NAME)|Maps.newHashMap(Utils.ACCOUNT_API_NAME, Lists.newArrayList("name"))|searchQueryAddFieldFilter(Lists.newArrayList("object_describe_api_name"),Lists.newArrayList("1"))|buildQueryResult() |null
        Lists.newArrayList("xxxx")| getDescribe(Utils.ACCOUNT_API_NAME)|getDescribe(Utils.ACCOUNT_ADDR_API_NAME)|Maps.newHashMap(Utils.ACCOUNT_API_NAME, Lists.newArrayList("name"))|searchQueryAddFieldFilter(Lists.newArrayList("object_describe_api_name"),Lists.newArrayList("1"))|null               |null;
    }

    def "getAccountAddrDataQueryResult method"() {
        given:
        

        
        Whitebox.setInternalState(accountAddrService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchTemplateQueryWithFields", any(), any(), any(), any())
        when:
        def result = Whitebox.invokeMethod(accountAddrService, "getAccountAddrDataQueryResult", getServiceContext(user,"AccountAddrService", "findAccountList"), searchTemplateQuery,
                needFieldMap)
        then:
        true
        where:
        needFieldMap                                                            |searchTemplateQuery                                                                              |queryResult        |ret
        Maps.newHashMap(Utils.ACCOUNT_API_NAME, Lists.newArrayList("name"))     |searchQueryAddFieldFilter(Lists.newArrayList("object_describe_api_name"),Lists.newArrayList("1"))|buildQueryResult() |null
        Maps.newHashMap(Utils.ACCOUNT_ADDR_API_NAME, Lists.newArrayList("name"))|searchQueryAddFieldFilter(Lists.newArrayList("object_describe_api_name"),Lists.newArrayList("1"))|buildQueryResult() |null;
    }

    def "buildSearchTemplateQueryForAccountAddr method"() {
        given:
        

        Whitebox.setInternalState(accountAddrService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(searchTemplateQuery).when(serviceFacade, "getSearchTemplateQuery", any() as User, any() as ObjectDescribeExt,anyString(),anyString())

        when:
        def result = Whitebox.invokeMethod(accountAddrService, "buildSearchTemplateQueryForAccountAddr", this.user,searchTemplateId,searchQueryInfo,type,highSeasIds,lon,lat,
                kilometers,skipPrivilege,geoSearchType,bottomGeoPoint,accountAddrDescribe,accountDescribe)
        then:
        true
        where:
        searchTemplateId|searchQueryInfo|type        |highSeasIds                |lon               |lat               |kilometers|skipPrivilege|geoSearchType|bottomGeoPoint           |accountAddrDescribe|accountDescribe                                      |searchTemplateQuery
        "dewdew"        |""             |"un_claimed"| Lists.newArrayList("name")|Double.valueOf(10)|Double.valueOf(10)|10        |true         |"dewdew"     |new FindNearby.GeoPoint()|null               |getDescribe(SFAPreDefineObject.Account.getApiName()) |isMasterField(true);
    }

    def "checkFieldPrivilege method"() {
        given:
        
        def commonPrivilegeService = PowerMockito.mock(PrivilegeService.class)
        Whitebox.setInternalState(accountAddrService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(accountAddrService, "commonPrivilegeService", commonPrivilegeService)
        PowerMockito.doReturn(fieldPrivileges).when(commonPrivilegeService, "getFieldPrivilege",  any(),any(), any(), any())

        when:
        def result = Whitebox.invokeMethod(accountAddrService, "checkFieldPrivilege", this.user)
        then:
        true
        where:
        fieldPrivileges                                                         |searchTemplateQuery                                                                              |queryResult        |ret
        Maps.newHashMap(Utils.ACCOUNT_API_NAME, Lists.newArrayList("name"))     |searchQueryAddFieldFilter(Lists.newArrayList("object_describe_api_name"),Lists.newArrayList("1"))|buildQueryResult() |null;
    }

    def "getNeedFieldMap method"() {
        given:

        def stopWatch = StopWatch.create("getNeedFieldMap");

        Whitebox.setInternalState(accountAddrService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(accountAddrService, "configService", configService)
        PowerMockito.doReturn(JSONObject.toJSONString(oldNeedFieldMap)).when(configService, "findTenantConfig",  any(),any())
        when:
        def result = Whitebox.invokeMethod(accountAddrService, "getNeedFieldMap", this.user,oldNeedFieldMap,layoutList,includeLayout,needSuppleLayout,needSuppleConfig,stopWatch,accountDescribe)
        then:
        true
        where:
        oldNeedFieldMap                                                                                           |accountDescribe  |layoutList          |includeLayout|needSuppleLayout|needSuppleConfig|ret
        Maps.newHashMap("apiname", Utils.ACCOUNT_API_NAME,Utils.ACCOUNT_API_NAME, Lists.newArrayList("name"))     |getDescribe()    |getLayoutList()     |true         |true            |true            |null;
    }

    def "buildResult method"() {
        given:
        def stopWatch = StopWatch.create("buildResult");

        Whitebox.setInternalState(accountAddrService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(accountAddrService, "configService", configService)
        when:
        def result = Whitebox.invokeMethod(accountAddrService, "buildResult", query,includeDescribe,includeLayout,describeVersionMap,accountDescribe,layoutList,stopWatch)
        then:
        true
        where:
        query                                                                                            |accountDescribe  |layoutList          |includeDescribe|includeLayout |describeVersionMap                                                  |ret
        searchQueryAddFieldFilter(Lists.newArrayList("object_describe_api_name"),Lists.newArrayList("1"))|getDescribe()    |getLayoutList()     |true           |true          |Maps.newHashMap(Utils.ACCOUNT_API_NAME, Lists.newArrayList("name")) |null;
    }

    def "getHighSeasIds method"() {
        given:
        def stopWatch = StopWatch.create("buildResult");
        def accountPoolServiceImpl = PowerMockito.mock(AccountPoolServiceImpl)

        Whitebox.setInternalState(accountAddrService, "accountPoolServiceImpl", accountPoolServiceImpl)
        PowerMockito.doReturn(objectDataList).when(accountPoolServiceImpl, "getAllVisiblePoolList",  anyString(),anyString(),anyString())
        when:
        def result = Whitebox.invokeMethod(accountAddrService, "getHighSeasIds", this.user)
        then:
        true
        where:
        query                                                                                            |objectDataList                                                     |accountDescribe  |layoutList          |includeDescribe|includeLayout |describeVersionMap                                                  |ret
        searchQueryAddFieldFilter(Lists.newArrayList("object_describe_api_name"),Lists.newArrayList("1"))|getObjectDataList(["account_id", "_id", "name","high_seas_id"], 3) |getDescribe()    |getLayoutList()     |true           |true          |Maps.newHashMap(Utils.ACCOUNT_API_NAME, Lists.newArrayList("name")) |null;
    }

    def buildAccountAddrList() {
        given:
        
        def arg = new FindNearby.Arg()
        arg.setLongitude(1.0)
        arg.setLatitude(1.0)
        arg.setSearchQueryInfo("{}")
        arg.setSkipPrivilege(false)
        arg.setType("1")
        arg.setNeedFieldMap(new HashMap<>())
        arg.setIncludeAllData(true)
        arg.setIncludeDescribe(true)
        arg.setDescribeVersionMap(new HashMap<>())
        arg.setNeedSuppleLayout(true)
        arg.setNeedSuppleConfig(true)
        arg.setIncludeLayout(true)
    }

    def getSearchQuery(SearchTemplateQuery searchTemplateQuery) {
        JSON.toJSONString(searchTemplateQuery)
    }


    def searchQueryAddFilterAndWheres(List<String> fields, List<Object> values) {
        def query = buildSearchQuery()
        fields.eachWithIndex { String f, int i ->
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, f, values[i]);
            query.getFilters().addAll(filters)
        }
        query
    }

    SearchTemplateQuery isMasterField(Boolean isMasterField) {
        def searchTemplateQuery = searchQueryAddFieldFilter(["name", "record_type"], ["account_name", "account_record_type"])
        def filters = searchTemplateQuery.getFilters()
        for (final def f in filters) {
            f.setIsMasterField(isMasterField)
            if (!isMasterField) {
                f.setValueType(0)
            }
        }
        Wheres wheres = new Wheres();
        wheres.setConnector(Where.CONN.OR.toString());
        wheres.setFilters(filters);
        searchTemplateQuery.getWheres().addAll(wheres)
      return   searchTemplateQuery
    }

    SearchTemplateQuery isMasterFieldNoFilter(Boolean isMasterField) {
        SearchTemplateQuery query = buildSearchQueryNoFilter()
        def searchTemplateQuery = searchQueryAddFieldFilter(["name", "record_type"], ["account_name", "account_record_type"])
        def filters = searchTemplateQuery.getFilters()
        for (final def f in filters) {
            f.setIsMasterField(isMasterField)
        }
        Wheres wheres = new Wheres();
        wheres.setConnector(Where.CONN.OR.toString());
        wheres.setFilters(filters);
        query.getWheres().addAll(wheres)
       return query
    }

    def DataRightsParameter buildDataRightsParameter(Boolean cascadeSubordinates, String roleType, String sceneType, Boolean isDetailObject) {
        if (roleType == null || sceneType == null) {
            return null
        }
        DataRightsParameter dataRightsParameter = new DataRightsParameter();
        dataRightsParameter.setIsDetailObject(isDetailObject);
        dataRightsParameter.setRoleType(roleType);
        dataRightsParameter.setSceneType(sceneType);
        dataRightsParameter.setCascadeSubordinates(cascadeSubordinates);
        dataRightsParameter;
    }

    List<ILayout> getLayoutList(){
        ILayout layout = new Layout();
        layout.setAgentType("agent_type_mobile")
        TableComponent tableComponent = TableComponentBuilderNew.builder().refObjectApiName(AccountsReceivableNoteObjConstants.API_NAME).includeFields(Lists.newArrayList()).buttons(null).build();
        layout.setComponents(Lists.newArrayList(tableComponent))
        return Lists.newArrayList(layout)
    }
    IObjectDescribe getDescribe(){
        IObjectDescribe objectDescribe = getDescribe(Utils.ACCOUNT_API_NAME)
        IFieldDescribe fieldDescribe = new SelectManyFieldDescribe()
        fieldDescribe.setApiName("name")
        QuoteFieldDescribe quoteFieldDescribe = QuoteFieldDescribeBuilder.builder().quoteField("high_seas_name").quoteFieldType("object_reference").apiName("high_seas_name").unique(false).isIndex(false).required(false).label("分类").build();
        objectDescribe.setFieldDescribes(Lists.newArrayList(fieldDescribe,quoteFieldDescribe));
        return objectDescribe;
    }


}
