package com.facishare.crm.sfa.predefine.service

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.facishare.crm.describebuilder.TextFieldDescribeBuilder
import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.model.SFAObjectPoolCommon
import com.facishare.crm.sfa.predefine.SFAPreDefineObject
import com.facishare.crm.sfa.predefine.action.BaseSFAAllocateAction
import com.facishare.crm.sfa.predefine.service.model.GetPoolAdminById
import com.facishare.crm.sfa.predefine.service.model.GetPoolMembersByIds
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolModels
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission
import com.facishare.crm.sfa.service.IPrmService
import com.facishare.crm.sfa.utilities.proxy.PaasLogServiceProxy
import com.facishare.crm.sfa.utilities.proxy.model.PaasLogServiceModel
import com.facishare.crm.sfa.utilities.util.AccountUtil
import com.facishare.crm.sfa.utilities.util.LeadsUtils
import com.facishare.crm.util.CommonBizOrgUtils
import com.facishare.crm.util.RestUtils
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.action.StandardChangeOwnerAction
import com.facishare.paas.appframework.core.util.Lang
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.metadata.api.INameCache
import com.facishare.paas.metadata.api.action.IActionContext
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.IObjectDescribeService
import com.facishare.paas.metadata.impl.NameCache
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl
import com.fxiaoke.functions.utils.Maps
import com.google.gson.internal.LinkedTreeMap
import org.apache.curator.shaded.com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.Logger
import org.spockframework.runtime.Sputnik
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyBoolean
import static org.mockito.ArgumentMatchers.anyList
import static org.mockito.ArgumentMatchers.anyString

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([
        RequestContextManager.class,SFAPreDefineObject.class,AccountUtil, LeadsUtils, CommonBizOrgUtils, I18N.class, UdobjGrayConfig.class, PaasLogServiceProxy.class, RestUtils.class
])
@SuppressStaticInitializationFor([
        "com.facishare.crm.util.RestUtils",
        "com.facishare.crm.sfa.utilities.proxy.PaasLogServiceProxy",
        "com.facishare.crm.sfa.utilities.util.AccountUtil",
        "com.facishare.crm.sfa.utilities.util.LeadsUtils",
        "com.facishare.paas.appframework.core.model.User",
        "com.facishare.crm.sfa.utilities.util.CommonSqlUtil",
        "com.facishare.crm.sfa.utilities.util.SearchListUtil",
        "com.facishare.crm.util.CommonBizOrgUtils",
        "com.fxiaoke.notifier.support.NotifierAgent",
        "com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl",
        "com.facishare.paas.I18N",
        "com.facishare.crm.sfa.predefine.SFAPreDefineObject"
])
class ObjectPoolServiceTest extends EnhancedBaseGroovyTest {
    @Shared
    ObjectPoolService objectPoolService
    @Shared
    private User user
    @Shared
    ServiceContext context
    @Shared
    ServiceFacade serviceFacade
    @Shared
     ObjectPoolServiceManager objectPoolServiceManager;
    @Shared
     RequestContext requestContext
    @Shared
    EnterpriseInitService enterpriseInitService
    @Shared
    IObjectDescribeService objectDescribeService;

    def setupSpec() {

        removeConfigFactory()
        removeI18N()
        initSpringContext()
        serviceFacade = PowerMockito.mock(ServiceFacade)
        user = User.systemUser("82681")
        objectPoolService = PowerMockito.spy(new ObjectPoolService())
        Whitebox.setInternalState(ObjectPoolService, "log", Mock(Logger))
        def tenantId = "79337"
        def userId = "1000"
        context = new ServiceContext(RequestContext.builder().user(new User(tenantId, userId)).tenantId(tenantId).build(), "", "")

        SFAPreDefineObject AccountObj = PowerMockito.mock(SFAPreDefineObject);
        PowerMockito.when(AccountObj.getApiName()).thenReturn("AccountObj");
        Whitebox.setInternalState(SFAPreDefineObject.class, "Account", AccountObj);
        SFAPreDefineObject LeadsObj = PowerMockito.mock(SFAPreDefineObject);
        PowerMockito.when(LeadsObj.getApiName()).thenReturn("LeadsObj");
        Whitebox.setInternalState(SFAPreDefineObject.class, "Leads", LeadsObj);

        objectPoolServiceManager = PowerMockito.mock(ObjectPoolServiceManager)


        requestContext = RequestContext.builder()
                .user(user)
                .tenantId(tenantId)
                .lang(Lang.zh_CN)
                .requestSource(RequestContext.RequestSource.CEP)
                .peerName("dht")
                .appId("123")
                .build()

        enterpriseInitService = PowerMockito.mock(EnterpriseInitService)
        objectDescribeService = PowerMockito.mock(IObjectDescribeService)
    }

    def setup() {
        PowerMockito.stub(PowerMockito.method(I18N.class, "text", String.class))
                .toReturn("xx")
        PowerMockito.stub(PowerMockito.method(UdobjGrayConfig.class, "isAllow", String.class, String.class))
                .toReturn(true)
    }
    def "test getPoolLog method"() {
        given:

        def logService = Stub(PaasLogServiceProxy)
        Whitebox.setInternalState(objectPoolService, "logService", logService)
        JSONObject jsonObject = new JSONObject()
        jsonObject.put("operationTime", Double.valueOf(1.72198073359E12))
        jsonObject.put("bizOperationName", "55")
        jsonObject.put("userId", "1000")
        jsonObject.put("userName", "hh")
        LinkedTreeMap jsonMessage = new LinkedTreeMap()
        LinkedTreeMap textMsg1 = new LinkedTreeMap()
        textMsg1.put("dataID", "001")
        textMsg1.put("objectApiName", "LeadsObj")
        textMsg1.put("text", "线索")
        textMsg1.put("type", Double.valueOf(0.0))
        textMsg1.put("objectType", Double.valueOf(1.0))
        LinkedTreeMap textMsg2 = new LinkedTreeMap()
        textMsg2.put("dataID", "002")
        textMsg2.put("objectApiName", "LeadsObj")
        textMsg2.put("text", "线索1")
        textMsg2.put("type", Double.valueOf(1.0))
        textMsg2.put("objectType", Double.valueOf(1.0))
        LinkedTreeMap textMsg3 = new LinkedTreeMap()
        textMsg3.put("dataID", "001")
        textMsg3.put("objectApiName", "LeadsObj")
        textMsg3.put("text", "线索--")
        textMsg3.put("type", Double.valueOf(0.0))
        textMsg3.put("objectType", Double.valueOf(1.0))
        jsonMessage.put("textMsg", Lists.newArrayList(textMsg1, textMsg2, textMsg3))
        jsonObject.put("jsonMessage", jsonMessage)
        LinkedTreeMap internationalTextMessage = new LinkedTreeMap()
        internationalTextMessage.put("internationalKey", "sfa.leads.return.pool.log")
        internationalTextMessage.put("defaultInternationalValue", "销售线索 线索122 --，原线索池 --，目标线索池 新建线索池1222")
        internationalTextMessage.put("internationalParameters", Lists.newArrayList("1", "2", "3", "4"))
        internationalTextMessage.put("internationalValue", "线索 线索122 --，源线索池 --，目标线索池 新建线索池1222")
        jsonObject.put("internationalTextMessage", internationalTextMessage)
        def logSearchResult = PaasLogServiceModel.LogSearchResult.builder()
                .page(1)
                .pageSize(20)
                .totalCount(41)
                .totalPage(3)
                .results(Lists.newArrayList(jsonObject))
                .build()
        logService.getWebLog((Map<String, String>) _, (JSONObject) _) >> logSearchResult

        PowerMockito.stub(PowerMockito.method(RestUtils.class, "getDDSHeaders", String.class, String.class))
                .toReturn(Maps.of("key", "value"))
        def serviceContext = getServiceContext(user,"pool", "getPoolLog")
        def arg = new ObjectPoolModels.GetPoolLogArg()
        arg.setApiName(apiName)
        arg.setPoolId(poolId)
        arg.setSearchQueryInfo(queryString)
        when:
        objectPoolService.getPoolLog(serviceContext, arg)
        then:
        noExceptionThrown()
        where:
        apiName    | poolId   | queryString
        ""         | "poolId" | ""
        "LeadsObj" | ""       | ""
        "LeadsObj" | "poolId" | "{\"limit\":0,\"offset\":0,\"orders\":[{\"fieldName\":\"operationTime\",\"isAsc\":false}],\"filters\":[]}"
        "LeadsObj" | "poolId" | "{\"limit\":0,\"offset\":0,\"orders\":[],\"filters\":[]}"
        "LeadsObj" | "poolId" | "{\"limit\":20,\"offset\":0,\"orders\":[{\"fieldName\":\"operationTime\",\"isAsc\":false}],\"filters\":[{\"field_name\":\"bizOperationName\",\"field_values\":[\"1\"],\"operator\":\"EQ\"}]}"

    }


    def "changeOwner"() {
        given:

        PowerMockito.when(serviceFacade.findObjectDataByIds(anyString(), anyList(), anyString())).thenReturn([
                new ObjectData("owner": ["1000"]),
                new ObjectData("_id": "1", "high_seas_id": "1", "leads_pool_id": "2")
        ])
        PowerMockito.when(serviceFacade.<StandardChangeOwnerAction.Arg, StandardChangeOwnerAction.Result> triggerAction(any(), any(StandardChangeOwnerAction.Arg), any())).thenReturn(
                StandardChangeOwnerAction.Result.builder().errorCode(errorCode).build()
        )
        PowerMockito.when(serviceFacade.<BaseSFAAllocateAction.Arg, SFAObjectPoolCommon.Result> triggerAction(any(), any(BaseSFAAllocateAction.Arg), any())).thenReturn(
                SFAObjectPoolCommon.Result.builder().successList([""]).build()
        )
        def arg = new ObjectPoolModels.ChangeOwnerArg(data: [
                new ObjectPoolModels.ChangeOwnerData(ownerId: ["1000"], objectDataId: "a01")
        ])
        def service = new ObjectPoolService(serviceFacade: serviceFacade)
        def res = service.changeOwner(context, objectApiName, arg)
        expect:
        res.isSuccess() == expect
        where:
        objectApiName | errorCode | expect
        "LeadsObj"    | "1"       | false
        "LeadsObj"    | "0"       | true
        "AccountObj"  | "0"       | true
    }

    def "getPoolPrivilege"() {
        given:
        PowerMockito.spy(AccountUtil)
        PowerMockito.doReturn(["1000"]).when(AccountUtil, "getUserDepartIds", anyString(), anyString())
        PowerMockito.spy(CommonBizOrgUtils)
        PowerMockito.doReturn(["1000"]).when(CommonBizOrgUtils, "getUserGroupIdsByMemberId", anyString(), anyString())
        PowerMockito.doReturn(["1000"]).when(CommonBizOrgUtils, "getUserRoleIdsByMemberId", anyString(), anyString())
        def objectDataService = PowerMockito.mock(ObjectDataServiceImpl)
        PowerMockito.when(objectDataService.findBySql(anyString(), anyString())).thenReturn([
                ["object_api_name": objectApiName, "pool_id": "1", "is_admin": isAdmin]
        ])
        def objectPoolService = PowerMockito.mock(IObjectPoolService)
        PowerMockito.when(objectPoolService.getObjectPoolByIds(anyString(), anyList())).thenReturn(pool)
        PowerMockito.when(objectPoolServiceManager.getObjectPoolService(anyString())).thenReturn(objectPoolService)
        def service = new ObjectPoolService(objectPoolServiceManager: objectPoolServiceManager, objectDataService: objectDataService)
        def res = service.getPoolPrivilege(context)
        expect:
        res.getHighSeasVisible() == highSeasVisible
        res.getLeadsPoolVisible() == leadsPoolVisible
        true
        where:
        objectApiName  | isAdmin | pool                                            | highSeasVisible | leadsPoolVisible
        "HighSeasObj"  | true    | []                                              | true            | false
        "HighSeasObj"  | false   | []                                              | false           | false
        "HighSeasObj"  | false   | [new ObjectData("is_visible_to_member": true)]  | true            | false
        "HighSeasObj"  | false   | [new ObjectData("is_visible_to_member": false)] | false           | false
        "LeadsPoolObj" | true    | []                                              | false           | true
        "LeadsPoolObj" | false   | []                                              | false           | false
        "LeadsPoolObj" | false   | [new ObjectData("is_visible_to_member": true)]  | false           | true
        "LeadsPoolObj" | false   | [new ObjectData("is_visible_to_member": false)] | false           | false
    }

    def "getSceneSql"() {
        given:
        def objectDataService = PowerMockito.mock(ObjectDataServiceImpl)
        def service = new ObjectPoolService(serviceFacade: serviceFacade, objectDataService: objectDataService)
        service = PowerMockito.spy(service)
        PowerMockito.when(service.batchGetPoolPermission(any(), any(), any(), any(), any())).thenReturn(["1": permissions])
        PowerMockito.when(serviceFacade.findObject(any(), any())).thenReturn(new ObjectDescribe())
        PowerMockito.when(objectDataService.getSqlBySearchQuery(anyString(), any(IObjectDescribe), any(SearchTemplateQuery), anyBoolean(), any(IActionContext))).thenReturn("where 1=1")
        def res1 = service.getHighSeasSceneSql(context, new ObjectPoolModels.GetPoolSceneSqlArg(poolIds: ["1"], sceneId: sceneId)).getResult()
        def res2 = service.getLeadsPoolSceneSql(context, new ObjectPoolModels.GetPoolSceneSqlArg(poolIds: ["1"], sceneId: sceneId)).getResult()
        expect:
        res1.get("1") == expect
        res2.get("1") == expect
        where:
        sceneId       | permissions                                              | expect
        "All"         | ObjectPoolPermission.ObjectPoolPermissions.NO_PERMISSION | "1=2"
        "All"         | ObjectPoolPermission.ObjectPoolPermissions.POOL_ADMIN    | "1=1"
        "Allocated"   | ObjectPoolPermission.ObjectPoolPermissions.POOL_ADMIN    | " 1="
        "UnAllocated" | ObjectPoolPermission.ObjectPoolPermissions.POOL_ADMIN    | " 1="
        "All"         | ObjectPoolPermission.ObjectPoolPermissions.POOL_MEMBER   | " 1="
        "Allocated"   | ObjectPoolPermission.ObjectPoolPermissions.POOL_MEMBER   | " 1="
        "UnAllocated" | ObjectPoolPermission.ObjectPoolPermissions.POOL_MEMBER   | " 1="
    }

    def "getObjectPoolKeyName"() {
        given:

        Whitebox.setInternalState(objectPoolService, "objectPoolServiceManager", objectPoolServiceManager)
        def teamService = PowerMockito.mock(IObjectPoolService)
        PowerMockito.when(objectPoolServiceManager.getObjectPoolService(anyString())).thenReturn(teamService)
        when:
        objectPoolService.getObjectPoolKeyName(apiName)
        then:
        true
        where:
        apiName    | poolId
        "LeadsObj" | "poolId"
        "xxxxx"    | "poolId"

    }
    def "getPoolPermission"() {
        given:
        def arg = new GetPoolAdminById.Arg();
        def argGetPoolMembersByIds = new GetPoolMembersByIds.Arg();
        arg.setApiName(apiName)
        def argGetPoolObjectsCountInfoArg = new ObjectPoolModels.GetPoolObjectsCountInfoArg();
        argGetPoolObjectsCountInfoArg.setApiName(apiName)
        argGetPoolObjectsCountInfoArg.setPoolIds(objectPoolIds)
        Whitebox.setInternalState(objectPoolService, "objectPoolServiceManager", objectPoolServiceManager)
        def teamService = PowerMockito.mock(IObjectPoolService)
        PowerMockito.when(objectPoolServiceManager.getObjectPoolService(anyString())).thenReturn(teamService)
        when:
        objectPoolService.getPoolPermission(apiName,tenantId,userId,objectPoolId,outTenantId)
        objectPoolService.getPoolPermission(apiName,tenantId,userId,objectPoolIds,outTenantId)
        objectPoolService.getPoolMemberType(apiName,tenantId,userId,objectPoolId)
        objectPoolService.getObjectPoolByIds(apiName,tenantId,objectPoolIds)
        objectPoolService.getObjectPoolById(apiName,tenantId,objectPoolId)
        objectPoolService.choose(apiName,user,objectPoolId,objectIds,eventId,partnerId)
        objectPoolService.move(apiName,user,objectPoolId,objectIds,eventId)
        objectPoolService.allocate(apiName,user,objectPoolId,objectIds,userId,eventId,outTenantId1,outOwnerId,partnerId)
        objectPoolService.back(apiName,user,objectPoolId,objectIds,1,"","","",true)
        objectPoolService.takeBack(apiName,user,objectPoolId,objectIds,"",true)
        objectPoolService.remove(apiName,user,Lists.newArrayList(),"",true,"")
        objectPoolService.getPoolAdminById(context,arg)
        objectPoolService.getPoolMembersByIds(context,argGetPoolMembersByIds)
        objectPoolService.getPoolAdminByIds(context,argGetPoolMembersByIds)
        objectPoolService.getPoolAdminByIds(user,apiName,objectPoolIds)
        objectPoolService.batchGetPoolPermission(apiName,tenantId,userId,objectPoolIds,outTenantId)
        objectPoolService.getPoolObjectCountInfo(context,argGetPoolObjectsCountInfoArg)
        objectPoolService.getPoolObjectUnDeletedCount(context,argGetPoolObjectsCountInfoArg)
        then:
        true
        where:
        apiName    | poolId   | tenantId | userId  | objectPoolId | outTenantId|objectPoolIds            |objectIds                 |eventId|partnerId|outTenantId1|outOwnerId
        "LeadsObj" | "poolId" | "poolId" | "poolId"| "poolId"     | "poolId"   |Lists.newArrayList("xxx")|Lists.newArrayList("xxx") |"xxx"  |"xxxx"   |323232      |3234243
        "xxxxxxxx" | "poolId" | "poolId" | "poolId"| "poolId"     | "poolId"   |Lists.newArrayList("xxx")|Lists.newArrayList("xxx") |"xxx"  |"xxxx"   |323232      |3234243

    }


    def "customPoolDescribe"() {
        given:
        def prmService = PowerMockito.mock(IPrmService)
        Whitebox.setInternalState(objectPoolService, "prmService", prmService)
        Whitebox.setInternalState(objectPoolService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(objectPoolService, "enterpriseInitService", enterpriseInitService)
        Whitebox.setInternalState(objectPoolService, "objectDescribeService", objectDescribeService)

        PowerMockito.when(prmService.prmOpenAddObjects(anyString())).thenReturn(true)
        PowerMockito.mockStatic(RequestContextManager)
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(requestContext)
        PowerMockito.when(serviceFacade.findObjects(anyString(), anyList())).thenReturn(describeMap)
        PowerMockito.when(enterpriseInitService.getPoolJsonFromFieldName(anyString())).thenReturn("{\"describe_api_name\":\"LeadsObj\",\"is_index\":true,\"is_active\":true,\"create_time\":1527147612708,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"来源\",\"is_unique\":false,\"label\":\"来源\",\"type\":\"select_one\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"source\",\"options\":[{\"label\":\"搜索引擎\",\"value\":\"1\"}],\"define_type\":\"package\",\"_id\":\"67fab466492c26000145a4c7\",\"is_single\":false,\"is_index_field\":false,\"status\":\"released\"}")

        PowerMockito.when(objectDescribeService.addCustomFieldDescribe(any(), any())).thenReturn(null)
        when:
        objectPoolService.customPoolDescribe(context)
        then:
        true
        where:
        apiName    | describeMap
        "LeadsObj" | com.beust.jcommander.internal.Maps.newHashMap("HighSeasObj",getDescribeA("HighSeasObj"),"LeadsPoolObj",getDescribeA("LeadsPoolObj"))

    }
    def "movePoolObjects"() {
        given:
        def arg = new ObjectPoolModels.MovePoolObjectsArg();
        arg.setApiName(apiName)
        arg.setSourcePoolId("xxxx")
        arg.setTargetPoolId("xxxxxxx")
        INameCache nameCache = new NameCache();
        nameCache.setId("xxx")
        nameCache.setName("xxxxx")
        List<INameCache> nameCacheList = new ArrayList<>();
        nameCacheList.add(nameCache)
        Whitebox.setInternalState(objectPoolService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(objectPoolService, "objectPoolServiceManager", objectPoolServiceManager)
        def teamService = PowerMockito.mock(IObjectPoolService)
        PowerMockito.when(objectPoolServiceManager.getObjectPoolService(anyString())).thenReturn(teamService)
        PowerMockito.when(teamService.movePoolObjects(any(),anyString(),anyString())).thenReturn(true)
        PowerMockito.when(serviceFacade.findRecordName(any(),anyString(), anyList())).thenReturn(nameCacheList)
        when:
        objectPoolService.movePoolObjects(context,arg)
        then:
        true
        where:
        apiName    | describeMap
        "LeadsObj" | Maps.newHashMap()

    }

     IObjectDescribe getDescribeA(String objectApiName) {
         IObjectDescribe describe = new ObjectDescribe();
         describe.setApiName(objectApiName)
         describe.addFieldDescribe(TextFieldDescribeBuilder.builder().apiName("source").label("source").required(true).build())
         return describe
     }
}
