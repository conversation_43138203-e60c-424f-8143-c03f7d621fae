<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-crm-sfa</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.7.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fs-crm-web</artifactId>

    <packaging>war</packaging>
    <properties>
        <skip_maven_deploy>true</skip_maven_deploy>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.tomcat.maven</groupId>
                <artifactId>tomcat7-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <dependencies>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mongo-spring-support</artifactId>
        </dependency>

        <!--RestEasy基本包 -->
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jaxrs</artifactId>
            <version>${resteasy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-servlet-initializer</artifactId>
            <version>${resteasy.version}</version>
        </dependency>
        <!--序列化和反序列化 -->
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jackson2-provider</artifactId>
        </dependency>
        <!--RestEasy整合BeanValidation -->
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-validator-provider</artifactId>
            <version>${resteasy.version}</version>
        </dependency>
        <!--RestEasy整合Spring -->
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-spring</artifactId>
            <version>${resteasy.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>core-filter</artifactId>
        </dependency>
        <!--<dependency>-->
        <!--<artifactId>netty-all</artifactId>-->
        <!--<groupId>io.netty</groupId>-->
        <!--<version>4.1.8.Final</version>-->
        <!--</dependency>-->

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-privilege-temp</artifactId>
            <version>${appframework.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.athaydes</groupId>
                    <artifactId>spock-reports</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-pod-client</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-codec</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-buffer</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-codec-http</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-codec-http2</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-codec-socks</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-common</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-handler</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-handler-proxy</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-resolver</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-transport</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-rocketmq-support</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javassist-3.14.0-GA</artifactId>
                    <groupId>org.ow2.util.bundles</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--日志级别动态调整-->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>logconfig-core</artifactId>
        </dependency>



        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-hosts-record</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-provider-sfa</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.grpc</groupId>
                    <artifactId>grpc-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>
